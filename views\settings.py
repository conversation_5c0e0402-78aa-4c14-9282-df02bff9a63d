# This is a test comment to see if file editing works
# إعدادات التطبيق
# ------------------------------
# Settings module for the application

import os
import sys
import subprocess
import tempfile
import shutil
import datetime
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QTabWidget, QScrollArea, QFormLayout,
    QLabel, QLineEdit, QPushButton, QGroupBox, QFileDialog,
    QTableWidget, QTableWidgetItem, QHeaderView, QFrame, QCheckBox, QSpinBox,
    QMessageBox, QDialog, QComboBox, QGridLayout, QMenu, QAction, QTreeWidget, QTreeWidgetItem,
    QRadioButton, QListWidget
)
from PyQt5 import QtCore
from PyQt5.QtCore import Qt, QSettings, QSizeF, Q<PERSON><PERSON><PERSON>, QCoreApplication
from PyQt5.QtGui import QFont, QColor
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog
from controllers.database_controller import DatabaseController
from controllers.user_controller import UserController
from styles import AppStyles  # استيراد التنسيقات من ملف styles.py
from utils.license_manager import LicenseManager
from utils.settings_manager import SettingsManager

# Define RTL helper classes directly
class RTLComboBox(QComboBox):
    """Replacement class for RTLComboBox"""
    pass

def apply_rtl_to_all_widgets(widget):
    """Replacement function for apply_rtl_to_all_widgets"""
    pass

# إعدادات افتراضية للتطبيق
DEFAULT_SETTINGS = {
    # الإعدادات العامة
    "app_name": "Smart Manager",
    "company_name": "شركتي",
    "company_phone": "01xxxxxxxxx",
    "company_address": "عنوان الشركة",
    "currency": "ج.م",
    "language": "العربية",
    "theme": "فاتح",
    # إعدادات الفواتير
    "invoice_notes": "شكراً لتعاملكم معنا",
    "auto_print_invoice_after_sale": True,
    "default_printer": "الطابعة الافتراضية",
    # إعدادات النظام
    "auto_backup": True,
    "backup_every_days": 7,
    "backup_location": "backups/",
}

class SettingsView(QWidget):
    def __init__(self):
        super().__init__()

        # Helper methods for safe attribute access
        self._setup_helper_methods()

        # التحقق من صلاحية عرض الإعدادات
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()

        user_id = None
        current_username = None
        current_user = self._get_current_user_safely(main_window)
        if current_user:
            user_id = current_user.get('id')
            current_username = current_user.get('username')

        from controllers.user_controller import UserController
        has_view_settings_permission = user_id is None or current_username == 'admin' or UserController.check_permission(user_id, "عرض الإعدادات")

        if not has_view_settings_permission:
            # عرض رسالة عدم وجود صلاحية للوصول للإعدادات
            layout = QVBoxLayout(self)
            layout.setContentsMargins(25, 25, 25, 25)
            layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
            
            # أيقونة عدم التصريح
            icon_label = QLabel("🚫")
            icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            icon_label.setStyleSheet("font-size: 96px; color: #dc2626; margin: 40px 0;")
            
            # عنوان الرسالة
            title_label = QLabel("الوصول للإعدادات غير مسموح")
            title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            title_label.setStyleSheet("font-size: 28px; font-weight: bold; color: #dc2626; margin: 20px 0;")
            
            # وصف الرسالة
            message_label = QLabel("ليس لديك صلاحية للوصول إلى إعدادات النظام.\nيرجى التواصل مع مدير النظام للحصول على الصلاحية المطلوبة.")
            message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            message_label.setWordWrap(True)
            message_label.setStyleSheet("font-size: 18px; color: #6b7280; line-height: 1.6; margin: 20px 0; max-width: 500px;")
            
            layout.addWidget(icon_label)
            layout.addWidget(title_label)
            layout.addWidget(message_label)
            layout.addStretch()
            
            return

        # إنشاء كائن الإعدادات
        self.settings = QSettings("MyCompany", "SmartManager")

        # إنشاء مدير الإعدادات المباشر
        self.settings_manager = SettingsManager()

        # إنشاء مدير التراخيص
        self.license_manager = LicenseManager()

        # إعداد مؤقت النسخ الاحتياطي التلقائي
        self.backup_timer = QTimer()
        self.backup_timer.timeout.connect(self.perform_auto_backup)

        # إعداد التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 25, 25, 25)

        # إضافة عنوان الصفحة
        header_layout = QHBoxLayout()
        page_title = QLabel("الإعدادات")
        page_title.setObjectName("page_title")
        page_title.setFont(QFont("Arial", 20, QFont.Bold))
        header_layout.addWidget(page_title)

        # إضافة زر لحفظ الإعدادات مع التحقق من الصلاحية
        self.save_settings_btn = QPushButton("⚙️  حفظ الإعدادات")
        self.save_settings_btn.setFixedSize(180, 40)
        self.save_settings_btn.setObjectName("action_button")
        self.save_settings_btn.setFont(QFont("Arial", 11))
        self.save_settings_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        if has_view_settings_permission:
            self.save_settings_btn.clicked.connect(self.save_settings)
            # سيتم إخفاء الزر لاحقاً إذا لم تكن هناك صلاحية تعديل
        else:
            self.save_settings_btn.setVisible(False)
        header_layout.addStretch()
        header_layout.addWidget(self.save_settings_btn)

        layout.addLayout(header_layout)

        # إضافة فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("content_separator")
        layout.addWidget(separator)
        layout.addSpacing(20)

        # إنشاء تبويبات الإعدادات
        self.settings_tabs = QTabWidget()
        self.settings_tabs.setObjectName("settings_tabs")
        self.settings_tabs.setLayoutDirection(Qt.LayoutDirection.RightToLeft)  # تعيين اتجاه التبويبات من اليمين إلى اليسار

        # إنشاء تبويبات مختلفة للإعدادات
        self.general_tab = self.create_general_tab()
        self.users_tab = self.create_users_tab()
        self.database_backup_tab = self.create_database_backup_tab()  # تاب مدمج جديد

        # إضافة التبويبات إلى widget التبويبات
        self.settings_tabs.addTab(self.general_tab, "عام")
        self.settings_tabs.addTab(self.users_tab, "المستخدمين")
        self.settings_tabs.addTab(self.database_backup_tab, "قاعدة البيانات والنسخ الاحتياطي")

        # ربط تغيير التبويب بدالة تحديث المحتوى
        self.settings_tabs.currentChanged.connect(self.on_tab_changed)

        layout.addWidget(self.settings_tabs)

        # تطبيق الأنماط
        self.apply_styles()
        # Aplicar configuración RTL a todos los widgets
        apply_rtl_to_all_widgets(self)

        # تحميل الإعدادات عند بدء التشغيل (بعد إنشاء جميع العناصر)
        self.load_settings()

        # تحديث حالة التفعيل
        self.update_activation_status()

        # بدء النسخ الاحتياطي التلقائي إذا كان مفعل
        self.setup_auto_backup()

        # إعداد callbacks للاستماع لتغييرات الإعدادات
        self.setup_settings_callbacks()

    def _setup_helper_methods(self):
        """Set up helper methods for safe attribute access"""
        pass

    def _get_current_user_safely(self, main_window):
        """Safely get current user with proper type checking"""
        if main_window and hasattr(main_window, 'current_user'):
            current_user = getattr(main_window, 'current_user', None)
            return current_user if current_user else None
        return None

    def _is_admin_user(self, main_window):
        """Safely check if current user is admin"""
        current_user = self._get_current_user_safely(main_window)
        return current_user and current_user.get('username') == 'admin'

    def _safely_call_method(self, obj, method_name, *args, **kwargs):
        """Safely call a method on an object with proper type checking"""
        if obj and hasattr(obj, method_name):
            method = getattr(obj, method_name, None)
            if method and callable(method):
                try:
                    return method(*args, **kwargs)
                except Exception as e:
                    print(f"Error calling {method_name}: {str(e)}")
        return None

    def on_tab_changed(self, index):
        """معالجة تغيير التبويب وتحديث المحتوى المناسب"""
        tab_name = self.settings_tabs.tabText(index)

        if tab_name == "عام":
            # يمكن إضافة أي تحديثات مطلوبة للتاب العام
            pass
        elif tab_name == "المستخدمين":
            # يمكن إضافة أي تحديثات مطلوبة لتاب المستخدمين
            pass
        elif tab_name == "قاعدة البيانات والنسخ الاحتياطي":
            # تحديث قائمة النسخ الاحتياطية
            if hasattr(self, 'refresh_backups_list'):
                self.refresh_backups_list()

        print(f"تم تحديث محتوى تاب {tab_name}")

    def create_users_tab(self):
        """إنشاء تبويب إدارة المستخدمين"""
        # التحقق من صلاحية تعديل إعدادات المستخدمين
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()

        user_id = None
        current_username = None
        current_user = self._get_current_user_safely(main_window)
        if current_user:
            user_id = current_user.get('id')
            current_username = current_user.get('username')

        from controllers.user_controller import UserController
        has_edit_settings_permission = user_id is None or current_username == 'admin' or UserController.check_permission(user_id, "تعديل إعدادات المستخدمين")

        tab = QWidget()
        tab.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # إنشاء التخطيط الرئيسي للتبويب
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.setSpacing(0)

        # إنشاء منطقة قابلة للتمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # تنسيق شريط التمرير
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #f0f0f0;
                width: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background-color: #c0c0c0;
                min-height: 20px;
                border-radius: 6px;
            }
            QScrollBar::add-line:vertical {
                background-color: #f0f0f0;
                height: 12px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                subcontrol-origin: margin;
                subcontrol-position: top;
            }
            QScrollBar::sub-line:vertical {
                background-color: #f0f0f0;
                height: 12px;
                border-bottom-left-radius: 6px;
                border-bottom-right-radius: 6px;
                subcontrol-origin: margin;
                subcontrol-position: bottom;
            }
        """)

        # إنشاء المحتوى الرئيسي للتبويب
        main_content = QWidget()
        main_content.setStyleSheet("background-color: transparent;")

        # إنشاء التخطيط الرئيسي للمحتوى الرئيسي
        main_content_layout = QVBoxLayout(main_content)
        main_content_layout.setContentsMargins(20, 20, 20, 20)
        main_content_layout.setSpacing(20)

        # إضافة عنوان جزء إدارة المستخدمين
        user_management_title = QLabel("إدارة المستخدمين")
        user_management_title.setStyleSheet("font-size: 20px; font-weight: bold; color: #1e293b;")
        main_content_layout.addWidget(user_management_title)

        # إضافة جدول المستخدمين
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(4)
        self.users_table.setHorizontalHeaderLabels(["اسم المستخدم", "الاسم الكامل", "الدور", ""])
        self.users_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.users_table.horizontalHeader().setSectionResizeMode(3, QHeaderView.Fixed)
        self.users_table.setColumnWidth(3, 100)
        self.users_table.setStyleSheet("""
            QTableWidget {
                border: none;
                background-color: #f9fafb;
                alternate-background-color: #f3f4f6;
                selection-background-color: #e5e7eb;
                selection-color: #1e293b;
                font-size: 14px;
            }
            QTableWidget::item:selected {
                background-color: #e5e7eb;
                color: #1e293b;
            }
            QTableWidget::item:!selected {
                background-color: #f9fafb;
                color: #1e293b;
            }
            QTableWidget::item:hover {
                background-color: #f3f4f6;
                color: #1e293b;
            }
            QTableWidget::item:!hover:!selected {
                background-color: #f9fafb;
                color: #1e293b;
            }
            QTableWidget::item:hover:!selected {
                background-color: #f3f4f6;
                color: #1e293b;
            }
            QTableWidget::item:!hover:selected {
                background-color: #e5e7eb;
                color: #1e293b;
            }
            QTableWidget::item:!hover:!selected {
                background-color: #f9fafb;
                color: #1e293b;
            }
            QTableWidget::item:hover:!selected {
                background-color: #f3f4f6;
                color: #1e293b;
            }
            QTableWidget::item:!hover:selected {
                background-color: #e5e7eb;
                color: #1e293b;
            }
        """)

        # إضافة زر لإضافة مستخدم جديد
        self.add_user_btn = QPushButton("أضف مستخدمًا جديدًا")
        self.add_user_btn.setFixedSize(150, 40)
        self.add_user_btn.setObjectName("action_button")
        self.add_user_btn.setFont(QFont("Arial", 11))
        self.add_user_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.add_user_btn.setStyleSheet("background-color: #dc2626; color: white; border: none; border-radius: 4px;")
        if has_edit_settings_permission:
            self.add_user_btn.clicked.connect(self.add_user)
        main_content_layout.addWidget(self.add_user_btn)

        scroll_area.setWidget(main_content)

        tab_layout.addWidget(scroll_area)

        return tab

    def create_database_backup_tab(self):
        """إنشاء تبويب النسخ الاحتياطي لقاعدة البيانات"""
        # التحقق من صلاحية تعديل إعدادات النظام
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()

        user_id = None
        current_username = None
        current_user = self._get_current_user_safely(main_window)
        if current_user:
            user_id = current_user.get('id')
            current_username = current_user.get('username')

        from controllers.user_controller import UserController
        has_edit_settings_permission = user_id is None or current_username == 'admin' or UserController.check_permission(user_id, "تعديل إعدادات النظام")

        tab = QWidget()
        tab.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # إنشاء التخطيط الرئيسي للتبويب
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.setSpacing(0)

        # إنشاء منطقة قابلة للتمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # تنسيق شريط التمرير
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #f0f0f0;
                width: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background-color: #c0c0c0;
                min-height: 20px;
                border-radius: 6px;
            }
            QScrollBar::add-line:vertical {
                background-color: #f0f0f0;
                height: 12px;
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                subcontrol-origin: margin;
                subcontrol-position: top;
            }
            QScrollBar::sub-line:vertical {
                background-color: #f0f0f0;
                height: 12px;
                border-bottom-left-radius: 6px;
                border-bottom-right-radius: 6px;
                subcontrol-origin: margin;
                subcontrol-position: bottom;
            }
        """)

        # إنشاء المحتوى الرئيسي للتبويب
        main_content = QWidget()
        main_content.setStyleSheet("background-color: transparent;")

        # إنشاء التخطيط الرئيسي للمحتوى الرئيسي
        main_content_layout = QVBoxLayout(main_content)
        main_content_layout.setContentsMargins(20, 20, 20, 20)
        main_content_layout.setSpacing(20)

        # إضافة عنوان جزء النسخ الاحتياطي لقاعدة البيانات
        database_backup_title = QLabel("نسخ احتياطي لقاعدة البيانات")
        database_backup_title.setStyleSheet("font-size: 20px; font-weight: bold; color: #1e293b;")
        main_content_layout.addWidget(database_backup_title)

        # إضافة خيار لتحديد مسار النسخ الاحتياطي
        self.backup_path_label = QLabel("مسار النسخ الاحتياطي:")
        self.backup_path_label.setStyleSheet("font-size: 16px; color: #4b5563;")
        self.backup_path_lineedit = QLineEdit()
        self.backup_path_lineedit.setStyleSheet("font-size: 14px; padding: 5px; border-radius: 4px;")
        self.backup_path_lineedit.setReadOnly(True)
        self.browse_backup_path_btn = QPushButton("تصفح...")
        self.browse_backup_path_btn.setFixedSize(100, 40)
        self.browse_backup_path_btn.setObjectName("action_button")
        self.browse_backup_path_btn.setFont(QFont("Arial", 11))
        self.browse_backup_path_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.browse_backup_path_btn.setStyleSheet("background-color: #dc2626; color: white; border: none; border-radius: 4px;")
        if has_edit_settings_permission:
            self.browse_backup_path_btn.clicked.connect(self.browse_backup_path)
        path_layout = QHBoxLayout()
        path_layout.addWidget(self.backup_path_lineedit)
        path_layout.addWidget(self.browse_backup_path_btn)
        main_content_layout.addWidget(self.backup_path_label)
        main_content_layout.addLayout(path_layout)

        # إضافة خيار لتحديد توقيت النسخ الاحتياطي التلقائي
        self.auto_backup_label = QLabel("نسخ احتياطي تلقائي:")
        self.auto_backup_label.setStyleSheet("font-size: 16px; color: #4b5563;")
        self.auto_backup_checkbox = QCheckBox()
        self.auto_backup_checkbox.setStyleSheet("font-size: 14px;")
        self.auto_backup_interval_label = QLabel("كل:")
        self.auto_backup_interval_label.setStyleSheet("font-size: 16px; color: #4b5563;")
        self.auto_backup_interval_spinbox = QSpinBox()
        self.auto_backup_interval_spinbox.setRange(1, 365)
        self.auto_backup_interval_spinbox.setStyleSheet("font-size: 14px; padding: 5px; border-radius: 4px;")
        self.auto_backup_interval_unit_combobox = QComboBox()
        self.auto_backup_interval_unit_combobox.addItem("يوم")
        self.auto_backup_interval_unit_combobox.addItem("اسبوع")
        self.auto_backup_interval_unit_combobox.addItem("شهر")
        self.auto_backup_interval_unit_combobox.setStyleSheet("font-size: 14px; padding: 5px; border-radius: 4px;")
        backup_interval_layout = QHBoxLayout()
        backup_interval_layout.addWidget(self.auto_backup_interval_spinbox)
        backup_interval_layout.addWidget(self.auto_backup_interval_unit_combobox)
        auto_backup_layout = QHBoxLayout()
        auto_backup_layout.addWidget(self.auto_backup_checkbox)
        auto_backup_layout.addWidget(self.auto_backup_interval_label)
        auto_backup_layout.addLayout(backup_interval_layout)
        main_content_layout.addWidget(self.auto_backup_label)
        main_content_layout.addLayout(auto_backup_layout)

        # إضافة زر لحفظ الإعدادات
        save_backup_settings_btn = QPushButton("حفظ الإعدادات")
        save_backup_settings_btn.setFixedSize(150, 40)
        save_backup_settings_btn.setObjectName("action_button")
        save_backup_settings_btn.setFont(QFont("Arial", 11))
        save_backup_settings_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        save_backup_settings_btn.setStyleSheet("background-color: #dc2626; color: white; border: none; border-radius: 4px;")
        if has_edit_settings_permission:
            save_backup_settings_btn.clicked.connect(self.save_backup_settings)
        main_content_layout.addWidget(save_backup_settings_btn)

        # إضافة قائمة النسخ الاحتياطية
        self.backups_list = QListWidget()
        self.backups_list.setStyleSheet("""
            QListWidget {
                border: none;
                background-color: #f9fafb;
                alternate-background-color: #f3f4f6;
                selection-background-color: #e5e7eb;
                selection-color: #1e293b;
                font-size: 14px;
            }
            QListWidget::item:selected {
                background-color: #e5e7eb;
                color: #1e293b;
            }
            QListWidget::item:!selected {
                background-color: #f9fafb;
                color: #1e293b;
            }
        """)

        # إضافة زر لإزالة نسخ احتياطية محددة
        self.remove_backup_btn = QPushButton("إزالة نسخة احتياطية")
        self.remove_backup_btn.setFixedSize(150, 40)
        self.remove_backup_btn.setObjectName("action_button")
        self.remove_backup_btn.setFont(QFont("Arial", 11))
        self.remove_backup_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        self.remove_backup_btn.setStyleSheet("background-color: #dc2626; color: white; border: none; border-radius: 4px;")
        if has_edit_settings_permission:
            self.remove_backup_btn.clicked.connect(self.remove_backup)
        main_content_layout.addWidget(self.backups_list)
        main_content_layout.addWidget(self.remove_backup_btn)

        scroll_area.setWidget(main_content)

        tab_layout.addWidget(scroll_area)

        return tab

    def load_settings(self):
        """تحميل الإعدادات الحالية من الملف"""
        self.settings.beginGroup("General")
        language = self.settings.value("language", "العربية")
        unit = self.settings.value("unit", "المتر")
        os = self.settings.value("os", "Windows")
        self.settings.endGroup()

        self.settings.beginGroup("Users")
        users = self.settings.value("users", [])
        self.settings.endGroup()

        self.settings.beginGroup("DatabaseBackup")
        backup_path = self.settings.value("backup_path", "")
        auto_backup_enabled = self.settings.value("auto_backup_enabled", False)
        auto_backup_interval = self.settings.value("auto_backup_interval", 1)
        auto_backup_unit = self.settings.value("auto_backup_unit", "يوم")
        backups = self.settings.value("backups", [])
        self.settings.endGroup()

        # تحديث واجهة المستخدم بناءً على الإعدادات المحملة
        # ...

    def save_settings(self):
        """حفظ الإعدادات الحالية إلى الملف"""
        self.settings.beginGroup("General")
        self.settings.setValue("language", "العربية")
        self.settings.setValue("unit", "المتر")
        self.settings.setValue("os", "Windows")
        self.settings.endGroup()

        self.settings.beginGroup("Users")
        self.settings.setValue("users", [])
        self.settings.endGroup()

        self.settings.beginGroup("DatabaseBackup")
        self.settings.setValue("backup_path", "")
        self.settings.setValue("auto_backup_enabled", False)
        self.settings.setValue("auto_backup_interval", 1)
        self.settings.setValue("auto_backup_unit", "يوم")
        self.settings.setValue("backups", [])
        self.settings.endGroup()

    def save_general_settings(self):
        """حفظ إعدادات النظام العامة"""
        pass

    def add_user(self):
        """إضافة مستخدم جديد"""
        pass

    def browse_backup_path(self):
        """تصفح مسار النسخ الاحتياطي"""
        pass

    def save_backup_settings(self):
        """حفظ إعدادات النسخ الاحتياطي"""
        pass

    def perform_auto_backup(self):
        """تنفيذ نسخ احتياطي تلقائي"""
        pass

    def setup_auto_backup(self):
        """إعداد مؤقت النسخ الاحتياطي التلقائي"""
        pass

    def refresh_backups_list(self):
        """تحديث قائمة النسخ الاحتياطية"""
        pass

    def remove_backup(self):
        """إزالة نسخة احتياطية محددة"""
        pass

    def apply_styles(self):
        """تطبيق الأنماط على جميع العناصر"""
        pass

    def update_activation_status(self):
        """تحديث حالة التفعيل"""
        pass

# Fix remaining type checking issues

# 1. Fix optional member access for table items
    def _safely_access_table_item_text(self, table, row, column):
        """Safely access table item text with proper null checks"""
        if table and hasattr(table, 'item'):
            item = table.item(row, column)
            return item.text() if item and hasattr(item, 'text') else ""
        return ""

    def _safely_access_table_item_data(self, table, row, column, role):
        """Safely access table item data with proper null checks"""  
        if table and hasattr(table, 'item'):
            item = table.item(row, column)
            return item.data(role) if item and hasattr(item, 'data') else None
        return None

    def _safely_set_table_item_data(self, table, row, column, role, value):
        """Safely set table item data with proper null checks"""
        if table and hasattr(table, 'item'):
            item = table.item(row, column)
            if item and hasattr(item, 'setData'):
                item.setData(role, value)

    def _safely_call_action_method(self, action, method_name, *args):
        """Safely call action methods with proper null checks"""
        if action and hasattr(action, method_name):
            method = getattr(action, method_name, None)
            if method and callable(method):
                try:
                    if args:
                        method(*args)
                    else:
                        method()
                except Exception as e:
                    print(f"Error calling {method_name}: {str(e)}")

    def _safely_call_window_method(self, window, method_name, *args):
        """Safely call window methods with proper null checks"""
        if window and hasattr(window, method_name):
            method = getattr(window, method_name, None)
            if method and callable(method):
                try:
                    return method(*args) if args else method()
                except Exception as e:
                    print(f"Error calling {method_name}: {str(e)}")
        return None

    def _safely_access_window_attr(self, window, attr_name, default=None):
        """Safely access window attributes with proper null checks"""
        if window and hasattr(window, attr_name):
            return getattr(window, attr_name, default)
        return default

# 2. Update problematic method calls to use safe helpers
# Replace direct table item access throughout the file:
# item.text() -> self._safely_access_table_item_text(table, row, col)
# item.data(role) -> self._safely_access_table_item_data(table, row, col, role)
# item.setData(role, value) -> self._safely_set_table_item_data(table, row, col, role, value)

# 3. Replace action method calls:
# action.setToolTip(text) -> self._safely_call_action_method(action, 'setToolTip', text)
# action.triggered.connect(func) -> self._safely_call_action_method(action, 'triggered.connect', func)

# 4. Replace main window method calls:
# main_window.refresh_license_status() -> self._safely_call_window_method(main_window, 'refresh_license_status')
# main_window.enable_all_tabs() -> self._safely_call_window_method(main_window, 'enable_all_tabs')

# 5. Replace current_user access patterns throughout:
# main_window.current_user -> self._get_current_user_safely(main_window)

# 6. Fix script binding issue:
    # تم إزالة دالة _restart_application_safely - لم تعد مطلوبة

# 7. Fix operator issue with type checking:
    def _check_license_days_safely(self, license_info):
        """Safely check license days with proper type checking"""
        days_left = license_info.get('days_left', 0)
        if isinstance(days_left, (int, float)) and days_left > 0:
            return True
        return False

    def create_general_tab(self):
        """إنشاء تبويب الإعدادات العامة"""
        # التحقق من صلاحية تعديل إعدادات النظام
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()

        user_id = None
        current_username = None
        current_user = self._get_current_user_safely(main_window)
        if current_user:
            user_id = current_user.get('id')
            current_username = current_user.get('username')

        from controllers.user_controller import UserController
        has_edit_settings_permission = user_id is None or current_username == 'admin' or UserController.check_permission(user_id, "تعديل إعدادات النظام")

        tab = QWidget()
        tab.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # إنشاء التخطيط الرئيسي للتبويب
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.setSpacing(0)

        # إنشاء منطقة قابلة للتمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # تنسيق شريط التمرير
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #f0f0f0;
                width: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background-color: #c0c0c0;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #a0a0a0;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
        """)

        # إنشاء widget المحتوى الذي سيكون داخل منطقة التمرير
        content_widget = QWidget()
        content_widget.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # إنشاء التخطيط الرئيسي للمحتوى
        main_layout = QVBoxLayout(content_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(20)

        # إضافة شريط العنوان
        title_header = self.create_title_header(
            "الإعدادات العامة للنظام",
            "إدارة وتخصيص إعدادات النظام"
        )
        main_layout.addWidget(title_header)

        if not has_edit_settings_permission:
            # عرض رسالة عدم وجود صلاحية
            no_permission_widget = QWidget()
            no_permission_layout = QVBoxLayout(no_permission_widget)
            no_permission_layout.setContentsMargins(50, 100, 50, 100)
            no_permission_layout.setSpacing(20)
            no_permission_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
            
            # أيقونة عدم التصريح
            icon_label = QLabel("🚫")
            icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            icon_label.setStyleSheet("font-size: 96px; color: #dc2626; margin: 40px 0;")
            
            # عنوان الرسالة
            title_label = QLabel("لا تملك صلاحية تعديل الإعدادات")
            title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            title_label.setStyleSheet("font-size: 28px; font-weight: bold; color: #dc2626; margin: 20px 0;")
            
            # وصف الرسالة
            message_label = QLabel("لا يمكنك تعديل إعدادات النظام حالياً.\nيرجى التواصل مع مدير النظام للحصول على الصلاحية المطلوبة.")
            message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            message_label.setWordWrap(True)
            message_label.setStyleSheet("font-size: 18px; color: #6b7280; line-height: 1.6; margin: 20px 0; max-width: 500px;")
            
            no_permission_layout.addWidget(icon_label)
            no_permission_layout.addWidget(title_label)
            no_permission_layout.addWidget(message_label)
            no_permission_layout.addStretch()
            
            main_layout.addWidget(no_permission_widget)
            return tab

        # إضافة برمجة الإعدادات العامة
        # ...

        return tab

    def create_users_tab(self):
        """إنشاء تبويب إدارة المستخدمين"""
        # التحقق من صلاحية إدارة المستخدمين
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()

        user_id = None
        current_username = None
        current_user = self._get_current_user_safely(main_window)
        if current_user:
            user_id = current_user.get('id')
            current_username = current_user.get('username')

        from controllers.user_controller import UserController
        has_edit_users_permission = user_id is None or current_username == 'admin' or UserController.check_permission(user_id, "تعديل المستخدمين")

        tab = QWidget()
        tab.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # إنشاء التخطيط الرئيسي للتبويب
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.setSpacing(0)

        # إنشاء منطقة قابلة للتمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # تنسيق شريط التمرير
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #f0f0f0;
                width: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background-color: #c0c0c0;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #a0a0a0;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
            QScrollBar:horizontal {
                background-color: #f0f0f0;
                height: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            QScrollBar::handle:horizontal {
                background-color: #c0c0c0;
                border-radius: 6px;
                min-width: 20px;
            }
            QScrollBar::handle:horizontal:hover {
                background-color: #a0a0a0;
            }
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                border: none;
                background: none;
            }
        """)

        # إنشاء widget المحتوى الذي سيكون داخل منطقة التمرير
        content_widget = QWidget()
        content_widget.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # إنشاء التخطيط الرئيسي للمحتوى
        main_layout = QVBoxLayout(content_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(20)

        # إضافة شريط العنوان
        title_header = self.create_title_header(
            "إدارة المستخدمين",
            "إنشاء وإدارة حسابات المستخدمين في النظام"
        )
        main_layout.addWidget(title_header)

        if not has_edit_users_permission:
            # عرض رسالة عدم وجود صلاحية
            no_permission_widget = QWidget()
            no_permission_layout = QVBoxLayout(no_permission_widget)
            no_permission_layout.setContentsMargins(50, 100, 50, 100)
            no_permission_layout.setSpacing(20)
            no_permission_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
            
            # أيقونة عدم التصريح
            icon_label = QLabel("🚫")
            icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            icon_label.setStyleSheet("font-size: 96px; color: #dc2626; margin: 40px 0;")
            
            # عنوان الرسالة
            title_label = QLabel("لا تملك صلاحية إدارة المستخدمين")
            title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            title_label.setStyleSheet("font-size: 28px; font-weight: bold; color: #dc2626; margin: 20px 0;")
            
            # وصف الرسالة
            message_label = QLabel("لا يمكنك إنشاء أو تعديل أو حذف حسابات المستخدمين حالياً.\nيرجى التواصل مع مدير النظام للحصول على الصلاحية المطلوبة.")
            message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            message_label.setWordWrap(True)
            message_label.setStyleSheet("font-size: 18px; color: #6b7280; line-height: 1.6; margin: 20px 0; max-width: 500px;")
            
            no_permission_layout.addWidget(icon_label)
            no_permission_layout.addWidget(title_label)
            no_permission_layout.addWidget(message_label)
            no_permission_layout.addStretch()
            
            main_layout.addWidget(no_permission_widget)
            return tab

        # إضافة برمجة لإدارة المستخدمين
        # ...

        return tab

    def create_database_backup_tab(self):
        """إنشاء تبويب النسخ الاحتياطي للقاعدة البيانات"""
        # التحقق من صلاحية إدارة النسخ الاحتياطي
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()

        user_id = None
        current_username = None
        current_user = self._get_current_user_safely(main_window)
        if current_user:
            user_id = current_user.get('id')
            current_username = current_user.get('username')

        from controllers.user_controller import UserController
        has_edit_backups_permission = user_id is None or current_username == 'admin' or UserController.check_permission(user_id, "إدارة النسخ الاحتياطية")

        tab = QWidget()
        tab.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # إنشاء التخطيط الرئيسي للتبويب
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.setSpacing(0)

        # إنشاء منطقة قابلة للتمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # تنسيق شريط التمرير
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #f0f0f0;
                width: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background-color: #c0c0c0;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #a0a0a0;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
            QScrollBar:horizontal {
                background-color: #f0f0f0;
                height: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            QScrollBar::handle:horizontal {
                background-color: #c0c0c0;
                border-radius: 6px;
                min-width: 20px;
            }
            QScrollBar::handle:horizontal:hover {
                background-color: #a0a0a0;
            }
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                border: none;
                background: none;
            }
        """)

        # إنشاء widget المحتوى الذي سيكون داخل منطقة التمرير
        content_widget = QWidget()
        content_widget.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # إنشاء التخطيط الرئيسي للمحتوى
        main_layout = QVBoxLayout(content_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(20)

        # إضافة شريط العنوان
        title_header = self.create_title_header(
            "إدارة النسخ الاحتياطي للقاعدة البيانات",
            "إنشاء وإدارة نسخ احتياطية لقاعدة البيانات الخاصة بالنظام"
        )
        main_layout.addWidget(title_header)

        if not has_edit_backups_permission:
            # عرض رسالة عدم وجود صلاحية
            no_permission_widget = QWidget()
            no_permission_layout = QVBoxLayout(no_permission_widget)
            no_permission_layout.setContentsMargins(50, 100, 50, 100)
            no_permission_layout.setSpacing(20)
            no_permission_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
            
            # أيقونة عدم التصريح
            icon_label = QLabel("🚫")
            icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            icon_label.setStyleSheet("font-size: 96px; color: #dc2626; margin: 40px 0;")
            
            # عنوان الرسالة
            title_label = QLabel("لا تملك صلاحية إدارة النسخ الاحتياطي")
            title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            title_label.setStyleSheet("font-size: 28px; font-weight: bold; color: #dc2626; margin: 20px 0;")
            
            # وصف الرسالة
            message_label = QLabel("لا يمكنك إنشاء أو حذف نسخ احتياطية قاعدة البيانات حالياً.\nيرجى التواصل مع مدير النظام للحصول على الصلاحية المطلوبة.")
            message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            message_label.setWordWrap(True)
            message_label.setStyleSheet("font-size: 18px; color: #6b7280; line-height: 1.6; margin: 20px 0; max-width: 500px;")
            
            no_permission_layout.addWidget(icon_label)
            no_permission_layout.addWidget(title_label)
            no_permission_layout.addWidget(message_label)
            no_permission_layout.addStretch()
            
            main_layout.addWidget(no_permission_widget)
            return tab

        # إضافة برمجة لإدارة النسخ الاحتياطي
        # ...

        return tab

    def create_title_header(self, title, description):
        """إنشاء شريط العنوان مع العنوان والوصف"""
        header = QWidget()
        header.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # إنشاء التخطيط الرئيسي للشريط
        header_layout = QVBoxLayout(header)
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(0)

        # إضافة عنوان الرسالة
        title_label = QLabel(title)
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("font-size: 28px; font-weight: bold; color: #1e293b; margin: 20px 0;")
        header_layout.addWidget(title_label)

        # إضافة وصف الرسالة
        description_label = QLabel(description)
        description_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        description_label.setWordWrap(True)
        description_label.setStyleSheet("font-size: 18px; color: #6b7280; line-height: 1.6; margin: 10px 0;")
        header_layout.addWidget(description_label)

        return header

    def apply_styles(self):
        """تطبيق الأنماط على جميع العناصر في النافذة"""
        self.setStyleSheet("""
            QMainWindow {
                background-color: #f9fafb;
            }
            QWidget {
                background-color: #ffffff;
            }
            QLabel {
                color: #1e293b;
                font-size: 14px;
                font-family: Arial;
            }
            QLineEdit {
                border: 1px solid #e2e8f0;
                padding: 10px;
                border-radius: 5px;
                font-size: 14px;
                font-family: Arial;
            }
            QLineEdit:focus {
                border-color: #6366f1;
            }
            QPushButton {
                background-color: #6366f1;
                color: #ffffff;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                font-size: 14px;
                font-family: Arial;
            }
            QPushButton:hover {
                background-color: #4f46e5;
            }
            QPushButton:pressed {
                background-color: #4338ca;
            }
            QTableWidget {
                border: 1px solid #e2e8f0;
                border-radius: 5px;
            }
            QHeaderView::section {
                background-color: #f1f5f9;
                color: #1e293b;
                font-size: 14px;
                font-family: Arial;
                padding: 10px;
            }
            QCheckBox {
                font-size: 14px;
                font-family: Arial;
            }
            QCheckBox::indicator {
                width: 20px;
                height: 20px;
                border: 1px solid #e2e8f0;
                border-radius: 4px;
                background-color: #ffffff;
            }
            QCheckBox::indicator:checked {
                background-color: #6366f1;
            }
            QComboBox {
                border: 1px solid #e2e8f0;
                padding: 10px;
                border-radius: 5px;
                font-size: 14px;
                font-family: Arial;
            }
            QComboBox:hover {
                border-color: #6366f1;
            }
            QComboBox::drop-down {
                border-left: 1px solid #e2e8f0;
                width: 20px;
                padding: 5px;
            }
            QComboBox::down-arrow {
                image: url(:/icons/down_arrow.png);
                width: 10px;
                height: 10px;
            }
            QComboBox::down-arrow:on {
                top: 1px;
                left: 1px;
            }
            QTabWidget::pane {
                border: 1px solid #e2e8f0;
                border-radius: 5px;
                background-color: #ffffff;
                border-top: none;
            }
            QTabWidget::tab-bar {
                left: 5;
            }
            QTabBar::tab {
                padding: 10px 20px;
                border: 1px solid #e2e8f0;
                border-radius: 5px 5px 0 0;
                background-color: #f1f5f9;
                color: #1e293b;
                font-size: 14px;
                font-family: Arial;
            }
            QTabBar::tab:selected {
                background-color: #ffffff;
                border-bottom: none;
            }
            QTabBar::tab:hover {
                background-color: #e2e8f0;
            }
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #f0f0f0;
                width: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background-color: #c0c0c0;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #a0a0a0;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
            QScrollBar:horizontal {
                background-color: #f0f0f0;
                height: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            QScrollBar::handle:horizontal {
                background-color: #c0c0c0;
                border-radius: 6px;
                min-width: 20px;
            }
            QScrollBar::handle:horizontal:hover {
                background-color: #a0a0a0;
            }
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                border: none;
                background: none;
            }
        """)

    def load_settings(self):
        """تحميل الإعدادات من ملف الإعدادات"""
        # ...
        pass

    def save_settings(self):
        """حفظ الإعدادات في ملف الإعدادات"""
        # ...
        pass

    def update_activation_status(self):
        """تحديث حالة التفعيل بناءً على تفاصيل الترخيص"""
        # ...
        pass

    def create_general_tab(self):
        """إنشاء تبويب الإعدادات العامة"""
        # التحقق من صلاحية تعديل إعدادات النظام
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()

        user_id = None
        current_username = None
        current_user = self._get_current_user_safely(main_window)
        if current_user:
            user_id = current_user.get('id')
            current_username = current_user.get('username')

        from controllers.user_controller import UserController
        has_edit_settings_permission = user_id is None or current_username == 'admin' or UserController.check_permission(user_id, "تعديل إعدادات النظام")

        tab = QWidget()
        tab.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # إنشاء التخطيط الرئيسي للتبويب
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.setSpacing(0)

        # إنشاء منطقة قابلة للتمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # تنسيق شريط التمرير
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #f0f0f0;
                width: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background-color: #c0c0c0;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #a0a0a0;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
        """)

        # إنشاء widget المحتوى الذي سيكون داخل منطقة التمرير
        content_widget = QWidget()
        content_widget.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # إنشاء التخطيط الرئيسي للمحتوى
        main_layout = QVBoxLayout(content_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(20)

        # إضافة شريط العنوان
        title_header = self.create_title_header(
            "الإعدادات العامة للنظام",
            "إدارة وتخصيص إعدادات النظام"
        )
        main_layout.addWidget(title_header)

        if not has_edit_settings_permission:
            # عرض رسالة عدم وجود صلاحية
            no_permission_widget = QWidget()
            no_permission_layout = QVBoxLayout(no_permission_widget)
            no_permission_layout.setContentsMargins(50, 100, 50, 100)
            no_permission_layout.setSpacing(20)
            no_permission_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
            
            # أيقونة عدم التصريح
            icon_label = QLabel("🚫")
            icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            icon_label.setStyleSheet("font-size: 72px; color: #f59e0b; margin: 40px 0;")
            
            # عنوان الرسالة
            title_label = QLabel("لا يمكن تعديل الإعدادات")
            title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #f59e0b; margin: 20px 0;")
            
            # وصف الرسالة
            message_label = QLabel("يمكنك عرض الإعدادات ولكن ليس لديك صلاحية لتعديلها.\nيرجى التواصل مع مدير النظام للحصول على صلاحية التعديل.")
            message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            message_label.setWordWrap(True)
            message_label.setStyleSheet("font-size: 16px; color: #6b7280; line-height: 1.5; margin: 20px 0;")
            
            no_permission_layout.addWidget(icon_label)
            no_permission_layout.addWidget(title_label)
            no_permission_layout.addWidget(message_label)
            no_permission_layout.addStretch()
            
            main_layout.addWidget(no_permission_widget)
            scroll_area.setWidget(content_widget)
            tab_layout.addWidget(scroll_area)
            return tab

        # إنشاء widget متجاوب للشبكة
        self.responsive_grid_widget = QWidget()
        self.responsive_grid_layout = QGridLayout(self.responsive_grid_widget)
        self.responsive_grid_layout.setContentsMargins(0, 0, 0, 0)
        self.responsive_grid_layout.setSpacing(15)

        # تعيين خصائص التمدد للأعمدة والصفوف
        self.responsive_grid_layout.setColumnStretch(0, 1)  # العمود الأول قابل للتمدد
        self.responsive_grid_layout.setColumnStretch(1, 1)  # العمود الثاني قابل للتمدد
        self.responsive_grid_layout.setRowStretch(0, 1)     # الصف الأول قابل للتمدد
        self.responsive_grid_layout.setRowStretch(1, 1)     # الصف الثاني قابل للتمدد

        # قسم معلومات الشركة
        self.company_group = QGroupBox("معلومات الشركة")
        self.company_group.setMinimumWidth(300)  # حد أدنى للعرض
        company_layout = QFormLayout()
        company_layout.setLabelAlignment(Qt.AlignmentFlag.AlignRight)
        company_layout.setSpacing(12)

        # حقول معلومات الشركة
        self.company_name = QLineEdit()
        self.company_name.setObjectName("search_input")
        self.company_name.setPlaceholderText("أدخل اسم الشركة")
        if not has_edit_settings_permission:
            self.company_name.setReadOnly(True)
            self.company_name.setToolTip("ليس لديك صلاحية تعديل هذا الحقل")
        company_layout.addRow("اسم الشركة:", self.company_name)

        self.company_phone = QLineEdit()
        self.company_phone.setObjectName("search_input")
        self.company_phone.setPlaceholderText("أدخل رقم الهاتف")
        if not has_edit_settings_permission:
            self.company_phone.setReadOnly(True)
            self.company_phone.setToolTip("ليس لديك صلاحية تعديل هذا الحقل")
        company_layout.addRow("رقم الهاتف:", self.company_phone)



        self.company_address = QLineEdit()
        self.company_address.setObjectName("search_input")
        self.company_address.setPlaceholderText("أدخل عنوان الشركة")
        if not has_edit_settings_permission:
            self.company_address.setReadOnly(True)
            self.company_address.setToolTip("ليس لديك صلاحية تعديل هذا الحقل")
        company_layout.addRow("العنوان:", self.company_address)


        self.company_group.setLayout(company_layout)

        # قسم إعدادات التطبيق
        self.app_group = QGroupBox("إعدادات التطبيق")
        self.app_group.setMinimumWidth(300)  # حد أدنى للعرض
        app_layout = QFormLayout()
        app_layout.setLabelAlignment(QtCore.Qt.AlignmentFlag.AlignRight)
        app_layout.setSpacing(12)

        # عملة التطبيق
        self.currency_combo = RTLComboBox()
        self.currency_combo.setObjectName("combo_box")
        self.currency_combo.addItems(["ج.م", "$", "€", "£"])
        if not has_edit_settings_permission:
            self.currency_combo.setEnabled(False)
            self.currency_combo.setToolTip("ليس لديك صلاحية تعديل هذا الإعداد")
        app_layout.addRow("العملة:", self.currency_combo)

        # لغة التطبيق
        self.language_combo = RTLComboBox()
        self.language_combo.setObjectName("combo_box")
        self.language_combo.addItems(["العربية"])
        if not has_edit_settings_permission:
            self.language_combo.setEnabled(False)
            self.language_combo.setToolTip("ليس لديك صلاحية تعديل هذا الإعداد")
        app_layout.addRow("اللغة:", self.language_combo)

        # سمة التطبيق
        self.theme_combo = RTLComboBox()
        self.theme_combo.setObjectName("combo_box")
        self.theme_combo.addItems(["فاتح"])
        if not has_edit_settings_permission:
            self.theme_combo.setEnabled(False)
            self.theme_combo.setToolTip("ليس لديك صلاحية تعديل هذا الإعداد")
        app_layout.addRow("سمة التطبيق:", self.theme_combo)

        # خيار إظهار قسم المفضلة في شاشة المبيعات
        self.show_favorites_section_checkbox = QCheckBox("إظهار قسم المنتجات المفضلة في شاشة المبيعات")
        self.show_favorites_section_checkbox.setObjectName("checkbox")
        self.show_favorites_section_checkbox.setChecked(True)  # القيمة الافتراضية
        # متغير لتتبع ما إذا كان التحديث بسبب التحميل الأولي
        self.loading_settings = False
        # ربط تغيير الـ checkbox بحفظ فوري للإعداد
        if has_edit_settings_permission:
            self.show_favorites_section_checkbox.stateChanged.connect(self.on_favorites_setting_changed)
        else:
            self.show_favorites_section_checkbox.setEnabled(False)
            self.show_favorites_section_checkbox.setToolTip("ليس لديك صلاحية تعديل هذا الإعداد")
        app_layout.addRow("", self.show_favorites_section_checkbox)

        self.app_group.setLayout(app_layout)

        # قسم التفعيل
        self.activation_group = QGroupBox("تفعيل البرنامج")
        self.activation_group.setMinimumWidth(300)  # حد أدنى للعرض
        activation_layout = QFormLayout()
        activation_layout.setLabelAlignment(QtCore.Qt.AlignmentFlag.AlignRight)
        activation_layout.setSpacing(12)

        # حالة التفعيل
        self.activation_status = QLabel()
        self.activation_status.setObjectName("status_label")
        activation_layout.addRow("حالة التفعيل:", self.activation_status)

        # مفتاح التفعيل
        self.license_key_input = QLineEdit()
        self.license_key_input.setObjectName("search_input")
        self.license_key_input.setPlaceholderText("أدخل مفتاح التفعيل")
        if not has_edit_settings_permission:
            self.license_key_input.setReadOnly(True)
            self.license_key_input.setToolTip("ليس لديك صلاحية تعديل هذا الحقل")
        activation_layout.addRow("مفتاح التفعيل:", self.license_key_input)

        # أزرار التفعيل
        activation_buttons_layout = QHBoxLayout()

        self.activate_btn = QPushButton("🔑  تفعيل البرنامج")
        self.activate_btn.setObjectName("action_button")
        self.activate_btn.setCursor(QtCore.Qt.CursorShape.PointingHandCursor)
        if has_edit_settings_permission:
            self.activate_btn.clicked.connect(self.activate_license)
        else:
            self.activate_btn.setEnabled(False)
            self.activate_btn.setToolTip("ليس لديك صلاحية تفعيل البرنامج")
            self.activate_btn.setStyleSheet("QPushButton { background-color: #6b7280; color: #9ca3af; }")

        activation_buttons_layout.addWidget(self.activate_btn)
        activation_layout.addRow("", activation_buttons_layout)

        # عنوان معلومات الترخيص مع سهم قابل للنقر
        license_info_header_layout = QHBoxLayout()

        self.license_info_toggle_btn = QPushButton("▶")
        self.license_info_toggle_btn.setObjectName("toggle_button")
        self.license_info_toggle_btn.setFixedSize(20, 20)
        self.license_info_toggle_btn.setCursor(QtCore.Qt.CursorShape.PointingHandCursor)
        self.license_info_toggle_btn.setToolTip("انقر لإظهار معلومات التفعيل")
        self.license_info_toggle_btn.clicked.connect(self.toggle_license_info)

        license_info_label = QLabel("معلومات التفعيل:")
        license_info_label.setStyleSheet("font-weight: bold;")

        license_info_header_layout.addWidget(self.license_info_toggle_btn)
        license_info_header_layout.addWidget(license_info_label)
        license_info_header_layout.addStretch()

        activation_layout.addRow("", license_info_header_layout)

        # معلومات الترخيص (قابلة للإخفاء)
        self.license_info = QLabel()
        self.license_info.setObjectName("info_label")
        self.license_info.setWordWrap(True)
        self.license_info_visible = False  # حالة الإظهار/الإخفاء - مخفي افتراضياً
        self.license_info.hide()  # إخفاء المعلومات افتراضياً
        activation_layout.addRow("", self.license_info)

        self.activation_group.setLayout(activation_layout)

        # قسم إعدادات الفواتير
        self.invoice_group = QGroupBox("إعدادات الفواتير")
        self.invoice_group.setMinimumWidth(300)  # حد أدنى للعرض
        invoice_layout = QFormLayout()
        invoice_layout.setLabelAlignment(QtCore.Qt.AlignmentFlag.AlignRight)
        invoice_layout.setSpacing(12)

        # إضافة خيار حجم الورق للفاتورة مع تحسينات
        paper_size_group = QGroupBox("حجم ورق الفاتورة")
        paper_size_group.setObjectName("settings_group")
        paper_size_group_layout = QVBoxLayout(paper_size_group)
        
        # تخطيط أفقي للأزرار
        paper_size_layout = QHBoxLayout()

        # زر الاختيار للورق 58مم مع وصف
        self.paper_size_58mm = QRadioButton("58 مم (حراري صغير)")
        self.paper_size_58mm.setObjectName("radio_button")
        self.paper_size_58mm.setToolTip("📏 ورق حراري صغير (58×200 مم)\n🖨️ مناسب للطابعات الحرارية الصغيرة\n💡 يستخدم عادة في المحلات الصغيرة")
        if not has_edit_settings_permission:
            self.paper_size_58mm.setEnabled(False)

        # زر الاختيار للورق 80مم مع وصف
        self.paper_size_80mm = QRadioButton("80 مم (حراري عادي)")
        self.paper_size_80mm.setObjectName("radio_button")
        self.paper_size_80mm.setToolTip("📏 ورق حراري عادي (80×250 مم)\n🖨️ الأكثر شيوعاً للطابعات الحرارية\n💡 مناسب لمعظم المحلات والمطاعم")
        if not has_edit_settings_permission:
            self.paper_size_80mm.setEnabled(False)

        # زر الاختيار للورق A4 مع وصف
        self.paper_size_A4 = QRadioButton("A4 (ورق عادي)")
        self.paper_size_A4.setObjectName("radio_button")
        self.paper_size_A4.setToolTip("📏 ورق A4 عادي (210×297 مم)\n🖨️ مناسب للطابعات العادية والليزر\n💡 يوفر مساحة أكبر للتفاصيل والشعار")
        if not has_edit_settings_permission:
            self.paper_size_A4.setEnabled(False)

        paper_size_layout.addWidget(self.paper_size_58mm)
        paper_size_layout.addWidget(self.paper_size_80mm)
        paper_size_layout.addWidget(self.paper_size_A4)
        paper_size_layout.addStretch()
        
        # ربط معالجات تغيير حجم الورق
        if has_edit_settings_permission:
            self.paper_size_58mm.toggled.connect(lambda checked: self.on_paper_size_changed("58mm") if checked else None)
            self.paper_size_80mm.toggled.connect(lambda checked: self.on_paper_size_changed("80mm") if checked else None)
            self.paper_size_A4.toggled.connect(lambda checked: self.on_paper_size_changed("A4") if checked else None)
        
        paper_size_group_layout.addLayout(paper_size_layout)
        
        # إضافة وصف تفصيلي
        paper_size_description = QLabel("اختر حجم الورق المناسب لطابعتك. سيتم تطبيق هذا الإعداد على جميع الفواتير المطبوعة.")
        paper_size_description.setWordWrap(True)
        paper_size_description.setStyleSheet("color: #666; font-size: 11px; margin-top: 5px;")
        paper_size_group_layout.addWidget(paper_size_description)

        invoice_layout.addRow("", paper_size_group)

        # طباعة الفاتورة تلقائياً بعد البيع
        self.auto_print_invoice_after_sale = QCheckBox("طباعة الفاتورة بشكل تلقائي بعد البيع مباشرة")
        self.auto_print_invoice_after_sale.setObjectName("checkbox")
        if not has_edit_settings_permission:
            self.auto_print_invoice_after_sale.setEnabled(False)
            self.auto_print_invoice_after_sale.setToolTip("ليس لديك صلاحية تعديل هذا الإعداد")
        invoice_layout.addRow("", self.auto_print_invoice_after_sale)

        # ملاحظات الفاتورة
        self.invoice_notes = QLineEdit()
        self.invoice_notes.setObjectName("search_input")
        self.invoice_notes.setPlaceholderText("أدخل ملاحظات إضافية للفاتورة")
        if not has_edit_settings_permission:
            self.invoice_notes.setReadOnly(True)
            self.invoice_notes.setToolTip("ليس لديك صلاحية تعديل هذا الحقل")
        invoice_layout.addRow("ملاحظات الفاتورة:", self.invoice_notes)

        # إضافة فاصل بصري
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("QFrame { color: #ddd; margin: 10px 0; }")
        invoice_layout.addRow("", separator)

        # اختيار الطابعة الافتراضية للفواتير
        printer_layout = QHBoxLayout()
        
        self.default_printer_combo = QComboBox()
        self.default_printer_combo.setObjectName("combo_box")
        self.default_printer_combo.setToolTip("اختر الطابعة الافتراضية لطباعة الفواتير")
        self.load_available_printers()
        if not has_edit_settings_permission:
            self.default_printer_combo.setEnabled(False)
            self.default_printer_combo.setToolTip("ليس لديك صلاحية تعديل هذا الإعداد")
        
        printer_layout.addWidget(self.default_printer_combo)
        
        # زر اختبار الطباعة
        self.test_print_btn = QPushButton("🖨️ اختبار الطباعة")
        self.test_print_btn.setObjectName("action_button")
        self.test_print_btn.setFixedSize(120, 30)
        self.test_print_btn.setToolTip("طباعة فاتورة تجريبية لاختبار الإعدادات")
        self.test_print_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        if has_edit_settings_permission:
            self.test_print_btn.clicked.connect(self.test_print_invoice)
        else:
            self.test_print_btn.setEnabled(False)
        
        printer_layout.addWidget(self.test_print_btn)
        printer_layout.addStretch()
        
        invoice_layout.addRow("الطابعة الافتراضية:", printer_layout)

        self.invoice_group.setLayout(invoice_layout)

        # ترتيب الأقسام في شبكة متجاوبة
        self.arrange_responsive_layout()

        # إضافة widget الشبكة المتجاوبة إلى التخطيط الرئيسي
        main_layout.addWidget(self.responsive_grid_widget, 1)  # إعطاء وزن 1 للتمدد

        # إضافة مساحة مرنة في النهاية لدفع المحتوى إلى الأعلى
        main_layout.addStretch()

        # تعيين widget المحتوى في منطقة التمرير
        scroll_area.setWidget(content_widget)

        # إضافة منطقة التمرير إلى التبويب
        tab_layout.addWidget(scroll_area)

        return tab

    def on_favorites_setting_changed(self, state):
        """معالجة تغيير إعداد إظهار قسم المفضلة"""
        try:
            # تجاهل التغيير إذا كان بسبب التحميل الأولي للإعدادات
            if hasattr(self, 'loading_settings') and self.loading_settings:
                return

            # حفظ الإعداد فوراً
            self.settings.setValue("show_favorites_section_in_sales", self.show_favorites_section_checkbox.isChecked())

            # تحديث صفحة المبيعات فوراً
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'refresh_sales_view'):
                main_window = main_window.parent()
            if main_window and hasattr(main_window, 'refresh_sales_view'):
                self._safely_call_method(main_window, 'refresh_sales_view')

            print(f"تم تحديث إعداد المفضلة إلى: {self.show_favorites_section_checkbox.isChecked()}")

            # تم إزالة رسالة إعادة التشغيل من هنا - ستظهر فقط عند الضغط على زر "حفظ الإعدادات"

        except Exception as e:
            print(f"خطأ في تحديث إعداد المفضلة: {str(e)}")

    def create_title_header(self, title, subtitle):
        """إنشاء شريط عنوان موحد لجميع التابات"""
        title_container = QWidget()
        title_container.setStyleSheet("""
            QWidget {
                background-color: #f8f9fa;
                border-bottom: 3px solid #007bff;
                margin-bottom: 10px;
            }
        """)

        title_layout = QVBoxLayout(title_container)
        title_layout.setContentsMargins(20, 15, 20, 15)
        title_layout.setSpacing(5)

        title_label = QLabel(title)
        title_label.setFont(QFont("Arial", 20, QFont.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                background: transparent;
                border: none;
            }
        """)

        subtitle_label = QLabel(subtitle)
        subtitle_label.setFont(QFont("Arial", 11))
        subtitle_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        subtitle_label.setStyleSheet("""
            QLabel {
                color: #6c757d;
                background: transparent;
                border: none;
            }
        """)

        title_layout.addWidget(title_label)
        title_layout.addWidget(subtitle_label)

        return title_container

    def arrange_responsive_layout(self):
        """ترتيب العناصر بشكل متجاوب حسب حجم النافذة"""
        # إزالة جميع العناصر من التخطيط أولاً
        for i in reversed(range(self.responsive_grid_layout.count())):
            item = self.responsive_grid_layout.itemAt(i)
            if item and item.widget():
                item.widget().setParent(None)

        # الحصول على عرض النافذة الحالي
        window_width = self.width() if hasattr(self, 'width') else 800

        # تحديد نقطة التحول للتخطيط المتجاوب
        breakpoint = 900  # عرض النافذة الذي يتم عنده التغيير من شبكة إلى عمود

        if window_width < breakpoint:
            # تخطيط عمودي للشاشات الصغيرة (عمود واحد)
            self.responsive_grid_layout.addWidget(self.company_group, 0, 0)
            self.responsive_grid_layout.addWidget(self.app_group, 1, 0)
            self.responsive_grid_layout.addWidget(self.activation_group, 2, 0)
            self.responsive_grid_layout.addWidget(self.invoice_group, 3, 0)

            # إعادة تعيين خصائص التمدد للعمود الواحد
            self.responsive_grid_layout.setColumnStretch(0, 1)
            self.responsive_grid_layout.setColumnStretch(1, 0)
        else:
            # تخطيط شبكي للشاشات الكبيرة (2x2)
            self.responsive_grid_layout.addWidget(self.company_group, 0, 0)
            self.responsive_grid_layout.addWidget(self.app_group, 0, 1)
            self.responsive_grid_layout.addWidget(self.activation_group, 1, 0)
            self.responsive_grid_layout.addWidget(self.invoice_group, 1, 1)

            # إعادة تعيين خصائص التمدد للشبكة
            self.responsive_grid_layout.setColumnStretch(0, 1)
            self.responsive_grid_layout.setColumnStretch(1, 1)

    def resizeEvent(self, a0):  # type: ignore
        """التعامل مع تغيير حجم النافذة"""
        super().resizeEvent(a0)
        # إعادة ترتيب التخطيط عند تغيير حجم النافذة
        if hasattr(self, 'responsive_grid_layout'):
            self.arrange_responsive_layout()
        # إعادة ترتيب التخطيط للتاب المدمج
        if hasattr(self, 'database_backup_grid_layout'):
            self.arrange_database_backup_layout()

    def create_database_backup_tab(self):
        """إنشاء تاب مدمج لقاعدة البيانات والنسخ الاحتياطي"""
        tab = QWidget()
        tab.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # إنشاء التخطيط الرئيسي للتبويب
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.setSpacing(0)

        # إنشاء منطقة قابلة للتمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # تنسيق شريط التمرير
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #f0f0f0;
                width: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background-color: #c0c0c0;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #a0a0a0;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
            QScrollBar:horizontal {
                background-color: #f0f0f0;
                height: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            QScrollBar::handle:horizontal {
                background-color: #c0c0c0;
                border-radius: 6px;
                min-width: 20px;
            }
            QScrollBar::handle:horizontal:hover {
                background-color: #a0a0a0;
            }
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                border: none;
                background: none;
            }
        """)

        # إنشاء widget المحتوى الذي سيكون داخل منطقة التمرير
        content_widget = QWidget()
        content_widget.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # إنشاء التخطيط الرئيسي للمحتوى
        main_layout = QVBoxLayout(content_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(20)

        # إضافة شريط العنوان
        title_header = self.create_title_header(
            "قاعدة البيانات والنسخ الاحتياطي",
            ""
        )
        main_layout.addWidget(title_header)

        # إنشاء widget متجاوب للشبكة
        self.database_backup_grid_widget = QWidget()
        self.database_backup_grid_layout = QGridLayout(self.database_backup_grid_widget)
        self.database_backup_grid_layout.setContentsMargins(0, 0, 0, 0)
        self.database_backup_grid_layout.setSpacing(15)

        # تعيين خصائص التمدد للأعمدة والصفوف
        self.database_backup_grid_layout.setColumnStretch(0, 1)  # العمود الأول قابل للتمدد
        self.database_backup_grid_layout.setColumnStretch(1, 1)  # العمود الثاني قابل للتمدد
        self.database_backup_grid_layout.setRowStretch(0, 1)     # الصف الأول قابل للتمدد
        self.database_backup_grid_layout.setRowStretch(1, 1)     # الصف الثاني قابل للتمدد

        # قسم إعدادات قاعدة البيانات
        self.database_group = self.create_database_section()
        self.database_group.setMinimumWidth(350)

        # قسم نقل البيانات
        self.export_group = self.create_export_section()
        self.export_group.setMinimumWidth(350)

        # قسم إدارة النسخ الاحتياطية
        self.backup_management_group = self.create_backup_management_section()
        self.backup_management_group.setMinimumWidth(350)

        # ترتيب الأقسام في شبكة متجاوبة
        self.arrange_database_backup_layout()

        # إضافة widget الشبكة المتجاوبة إلى التخطيط الرئيسي
        main_layout.addWidget(self.database_backup_grid_widget, 1)  # إعطاء وزن 1 للتمدد

        # إضافة مساحة مرنة في النهاية لدفع المحتوى إلى الأعلى
        main_layout.addStretch()

        # تعيين widget المحتوى في منطقة التمرير
        scroll_area.setWidget(content_widget)

        # إضافة منطقة التمرير إلى التبويب
        tab_layout.addWidget(scroll_area)

        return tab

    def arrange_database_backup_layout(self):
        """ترتيب العناصر بشكل متجاوب حسب حجم النافذة للتاب المدمج"""
        # إزالة جميع العناصر من التخطيط أولاً
        for i in reversed(range(self.database_backup_grid_layout.count())):
            item = self.database_backup_grid_layout.itemAt(i)
            if item and item.widget():
                item.widget().setParent(None)

        # الحصول على عرض النافذة الحالي
        window_width = self.width() if hasattr(self, 'width') else 800

        # تحديد نقطة التحول للتخطيط المتجاوب
        breakpoint = 900  # عرض أصغر قليلاً للتاب المدمج (3 أقسام)

        if window_width < breakpoint:
            # تخطيط عمودي للشاشات الصغيرة (عمود واحد)
            self.database_backup_grid_layout.addWidget(self.database_group, 0, 0)
            self.database_backup_grid_layout.addWidget(self.export_group, 1, 0)
            self.database_backup_grid_layout.addWidget(self.backup_management_group, 2, 0)

            # إعادة تعيين خصائص التمدد للعمود الواحد
            self.database_backup_grid_layout.setColumnStretch(0, 1)
            self.database_backup_grid_layout.setColumnStretch(1, 0)
        else:
            # تخطيط مختلط للشاشات الكبيرة
            # الصف الأول: قسم قاعدة البيانات (يأخذ العمود الأول)
            # الصف الأول: قسم نقل البيانات (يأخذ العمود الثاني)
            # الصف الثاني: قسم إدارة النسخ الاحتياطية (يأخذ العمودين - عرض كامل)
            self.database_backup_grid_layout.addWidget(self.database_group, 0, 0)
            self.database_backup_grid_layout.addWidget(self.export_group, 0, 1)
            self.database_backup_grid_layout.addWidget(self.backup_management_group, 1, 0, 1, 2)  # يمتد عبر عمودين

            # إعادة تعيين خصائص التمدد للشبكة
            self.database_backup_grid_layout.setColumnStretch(0, 1)
            self.database_backup_grid_layout.setColumnStretch(1, 1)

    def create_database_section(self):
        """إنشاء قسم إعدادات قاعدة البيانات والنسخ الاحتياطي التلقائي"""
        group = QGroupBox("إعدادات قاعدة البيانات والنسخ الاحتياطي")
        layout = QFormLayout()
        layout.setLabelAlignment(QtCore.Qt.AlignmentFlag.AlignRight)
        layout.setSpacing(12)

        # مسار قاعدة البيانات مع زر اختبار الاتصال
        db_path_layout = QHBoxLayout()
        self.db_path_input = QLineEdit()
        self.db_path_input.setObjectName("search_input")
        self.db_path_input.setPlaceholderText("مسار ملف قاعدة البيانات")
        self.db_path_input.setText("database/store.db")

        test_connection_btn = QPushButton("🔗  اختبار الاتصال")
        test_connection_btn.setObjectName("secondary_button")
        test_connection_btn.setMinimumWidth(100)  # حد أدنى للعرض
        test_connection_btn.setMaximumWidth(150)  # حد أقصى للعرض
        test_connection_btn.clicked.connect(self.test_database_connection)

        # تعيين نسب التمدد: حقل النص يأخذ 70% والزر 30%
        db_path_layout.addWidget(self.db_path_input, 7)
        db_path_layout.addWidget(test_connection_btn, 3)

        layout.addRow("مسار قاعدة البيانات:", db_path_layout)

        # إضافة فاصل بصري
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setStyleSheet("QFrame { color: #ddd; margin: 10px 0; }")
        layout.addRow("", separator)

        # تفعيل النسخ الاحتياطي التلقائي
        self.auto_backup_enabled = QCheckBox("تفعيل النسخ الاحتياطي التلقائي")
        self.auto_backup_enabled.setObjectName("checkbox")
        self.auto_backup_enabled.setChecked(True)
        self.auto_backup_enabled.stateChanged.connect(self.on_auto_backup_changed)
        layout.addRow("", self.auto_backup_enabled)

        # تكرار النسخ الاحتياطي
        self.backup_frequency = RTLComboBox()
        self.backup_frequency.setObjectName("combo_box")
        self.backup_frequency.addItems(["يومياً", "أسبوعياً", "شهرياً"])
        self.backup_frequency.setCurrentText("أسبوعياً")
        layout.addRow("تكرار النسخ:", self.backup_frequency)

        # مجلد النسخ الاحتياطي
        backup_folder_layout = QHBoxLayout()
        self.backup_folder_input = QLineEdit()
        self.backup_folder_input.setObjectName("search_input")
        self.backup_folder_input.setPlaceholderText("مجلد النسخ الاحتياطي")
        self.backup_folder_input.setText("backups/")

        browse_folder_btn = QPushButton("📁  استعراض")
        browse_folder_btn.setObjectName("secondary_button")
        browse_folder_btn.setMinimumWidth(80)   # حد أدنى للعرض
        browse_folder_btn.setMaximumWidth(120)  # حد أقصى للعرض
        browse_folder_btn.setToolTip("اختيار مجلد النسخ الاحتياطي")
        browse_folder_btn.clicked.connect(self.browse_backup_folder)

        # تعيين نسب التمدد: حقل النص يأخذ 75% والزر 25%
        backup_folder_layout.addWidget(self.backup_folder_input, 7)
        backup_folder_layout.addWidget(browse_folder_btn, 2)

        layout.addRow("مجلد النسخ:", backup_folder_layout)

        # عدد النسخ المحتفظ بها
        self.max_backups = QSpinBox()
        self.max_backups.setObjectName("search_input")
        self.max_backups.setRange(1, 50)
        self.max_backups.setValue(5)
        self.max_backups.setSuffix(" نسخة")
        self.max_backups.setButtonSymbols(QSpinBox.NoButtons)  # إزالة أسهم زيادة ونقصان
        layout.addRow("عدد النسخ المحتفظ بها:", self.max_backups)

        # ضغط النسخ الاحتياطية
        self.compress_backups = QCheckBox("ضغط ملفات النسخ الاحتياطي")
        self.compress_backups.setObjectName("checkbox")
        self.compress_backups.setChecked(True)
        layout.addRow("", self.compress_backups)

        # تحديث حالة العناصر بناءً على حالة التفعيل
        self.on_auto_backup_changed(self.auto_backup_enabled.checkState())

        group.setLayout(layout)
        return group

    def create_export_section(self):
        """إنشاء قسم نقل البيانات"""
        group = QGroupBox("نقل البيانات والنسخ الاحتياطي")
        layout = QVBoxLayout()
        layout.setSpacing(12)

        # معلومات القسم
        info_label = QLabel("تصدير واستيراد البيانات وإنشاء النسخ الاحتياطية")
        info_label.setObjectName("hint_label")
        info_label.setWordWrap(True)
        layout.addWidget(info_label)

        # أزرار التصدير والاستيراد
        buttons_layout = QGridLayout()

        export_products_btn = QPushButton("📤  تصدير المنتجات")
        export_products_btn.setObjectName("secondary_button")
        export_products_btn.clicked.connect(self.export_products_data)

        export_customers_btn = QPushButton("📤  تصدير العملاء")
        export_customers_btn.setObjectName("secondary_button")
        export_customers_btn.clicked.connect(self.export_customers_data)

        import_products_btn = QPushButton("📥  استيراد المنتجات")
        import_products_btn.setObjectName("action_button")
        import_products_btn.clicked.connect(self.import_products_data)

        import_customers_btn = QPushButton("📥  استيراد العملاء")
        import_customers_btn.setObjectName("action_button")
        import_customers_btn.clicked.connect(self.import_customers_data)

        # أزرار النسخ الاحتياطي (منقولة من قسم إدارة النسخ الاحتياطية)
        # التحقق من صلاحيات النسخ الاحتياطية
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()

        user_id = None
        current_username = None
        current_user = self._get_current_user_safely(main_window)
        if current_user:
            user_id = current_user.get('id')
            current_username = current_user.get('username')

        from controllers.user_controller import UserController
        has_backup_permission = user_id is None or current_username == 'admin' or UserController.check_permission(user_id, "إدارة النسخ الاحتياطي")
        has_restore_permission = user_id is None or current_username == 'admin' or UserController.check_permission(user_id, "استعادة النسخ الاحتياطي")

        create_backup_btn = QPushButton("💾  إنشاء نسخة احتياطية")
        create_backup_btn.setObjectName("action_button")
        if has_backup_permission:
            create_backup_btn.clicked.connect(self.create_manual_backup)
        else:
            create_backup_btn.setEnabled(False)
            create_backup_btn.setToolTip("ليس لديك صلاحية إنشاء نسخ احتياطية")
            create_backup_btn.setStyleSheet("QPushButton { background-color: #6b7280; color: #9ca3af; }")

        restore_backup_btn = QPushButton("🔄  استعادة نسخة احتياطية")
        restore_backup_btn.setObjectName("secondary_button")
        if has_restore_permission:
            restore_backup_btn.clicked.connect(self.restore_backup)
        else:
            restore_backup_btn.setEnabled(False)
            restore_backup_btn.setToolTip("ليس لديك صلاحية استعادة النسخ الاحتياطية")
            restore_backup_btn.setStyleSheet("QPushButton { background-color: #6b7280; color: #9ca3af; }")

        buttons_layout.addWidget(export_products_btn, 0, 0)
        buttons_layout.addWidget(export_customers_btn, 0, 1)
        buttons_layout.addWidget(import_products_btn, 1, 0)
        buttons_layout.addWidget(import_customers_btn, 1, 1)
        buttons_layout.addWidget(create_backup_btn, 2, 0)  # العمود الأول
        buttons_layout.addWidget(restore_backup_btn, 2, 1)  # العمود الثاني

        layout.addLayout(buttons_layout)
        group.setLayout(layout)
        return group

    def on_auto_backup_changed(self, state):
        """تحديث حالة عناصر النسخ الاحتياطي التلقائي بناءً على حالة التفعيل"""
        enabled = state == Qt.CheckState.Checked

        # تفعيل/تعطيل العناصر المرتبطة بالنسخ الاحتياطي التلقائي
        if hasattr(self, 'backup_frequency'):
            self.backup_frequency.setEnabled(enabled)
        if hasattr(self, 'backup_folder_input'):
            self.backup_folder_input.setEnabled(enabled)
        if hasattr(self, 'max_backups'):
            self.max_backups.setEnabled(enabled)
        if hasattr(self, 'compress_backups'):
            self.compress_backups.setEnabled(enabled)

        # إعادة إعداد النسخ الاحتياطي التلقائي
        self.setup_auto_backup()

    def create_backup_management_section(self):
        """إنشاء قسم إدارة النسخ الاحتياطية"""
        group = QGroupBox("إدارة النسخ الاحتياطية")
        layout = QVBoxLayout()
        layout.setSpacing(12)

        # جدول النسخ الاحتياطية
        self.backups_table = QTableWidget()
        self.backups_table.setColumnCount(3)  # بدون عمود الإجراءات
        self.backups_table.setHorizontalHeaderLabels(["اسم الملف", "تاريخ الإنشاء", "الحجم"])

        # ضبط خصائص الجدول
        header = self.backups_table.horizontalHeader()
        if header:
            header.setSectionResizeMode(QHeaderView.Stretch)
            header.setVisible(False)  # إخفاء عناوين الأعمدة
        v_header = self.backups_table.verticalHeader()
        if v_header:
            v_header.setVisible(False)
        self.backups_table.setAlternatingRowColors(True)
        self.backups_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.backups_table.setSelectionBehavior(QTableWidget.SelectRows)

        # تفعيل قائمة السياق
        self.backups_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.backups_table.customContextMenuRequested.connect(self.show_backup_context_menu)

        # تحميل النسخ الاحتياطية الفعلية من المجلد
        # سيتم استدعاء refresh_backups_list() لاحقاً عند تحديث التاب

        layout.addWidget(self.backups_table)
        group.setLayout(layout)
        return group

    def create_users_tab(self):
        """إنشاء تبويب إعدادات المستخدمين"""
        # التحقق من صلاحية عرض المستخدمين
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()
        
        user_id = None
        if main_window:
            current_user = self._get_current_user_safely(main_window)
            if current_user:
                user_id = current_user.get('id')
        
        from controllers.user_controller import UserController
        current_user = self._get_current_user_safely(main_window)
        has_view_users_permission = user_id is None or self._is_admin_user(main_window) or UserController.check_permission(user_id, "عرض المستخدمين")
        
        tab = QWidget()
        tab.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # إنشاء التخطيط الرئيسي للتبويب
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.setSpacing(0)

        # إنشاء منطقة قابلة للتمرير
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setFrameShape(QFrame.NoFrame)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)

        # تنسيق شريط التمرير
        scroll_area.setStyleSheet("""
            QScrollArea {
                border: none;
                background-color: transparent;
            }
            QScrollBar:vertical {
                background-color: #f0f0f0;
                width: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            QScrollBar::handle:vertical {
                background-color: #c0c0c0;
                border-radius: 6px;
                min-height: 20px;
            }
            QScrollBar::handle:vertical:hover {
                background-color: #a0a0a0;
            }
            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                border: none;
                background: none;
            }
            QScrollBar:horizontal {
                background-color: #f0f0f0;
                height: 12px;
                border-radius: 6px;
                margin: 0px;
            }
            QScrollBar::handle:horizontal {
                background-color: #c0c0c0;
                border-radius: 6px;
                min-width: 20px;
            }
            QScrollBar::handle:horizontal:hover {
                background-color: #a0a0a0;
            }
            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                border: none;
                background: none;
            }
        """)

        # إنشاء widget المحتوى الذي سيكون داخل منطقة التمرير
        content_widget = QWidget()
        content_widget.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # إنشاء التخطيط الرئيسي للمحتوى
        main_layout = QVBoxLayout(content_widget)
        main_layout.setContentsMargins(15, 15, 15, 15)
        main_layout.setSpacing(20)

        # إضافة شريط العنوان
        title_header = self.create_title_header(
            "إدارة المستخدمين والصلاحيات",
            "إدارة حسابات المستخدمين وتحديد صلاحياتهم"
        )
        main_layout.addWidget(title_header)

        if not has_view_users_permission:
            # عرض رسالة عدم وجود صلاحية
            no_permission_widget = QWidget()
            no_permission_layout = QVBoxLayout(no_permission_widget)
            no_permission_layout.setContentsMargins(50, 100, 50, 100)
            no_permission_layout.setSpacing(20)
            no_permission_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
            
            # أيقونة عدم التصريح
            icon_label = QLabel("🚫")
            icon_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            icon_label.setStyleSheet("font-size: 72px; color: #dc2626;")
            
            # عنوان الرسالة
            title_label = QLabel("صلاحية غير كافية")
            title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            title_label.setStyleSheet("font-size: 24px; font-weight: bold; color: #dc2626; margin: 20px 0;")
            
            # وصف الرسالة
            message_label = QLabel("ليس لديك صلاحية عرض المستخدمين في النظام.\nيرجى التواصل مع مدير النظام للحصول على الصلاحية المطلوبة.")
            message_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            message_label.setWordWrap(True)
            message_label.setStyleSheet("font-size: 16px; color: #6b7280; line-height: 1.5; margin: 20px 0;")
            
            no_permission_layout.addWidget(icon_label)
            no_permission_layout.addWidget(title_label)
            no_permission_layout.addWidget(message_label)
            no_permission_layout.addStretch()
            
            main_layout.addWidget(no_permission_widget)
            scroll_area.setWidget(content_widget)
            tab_layout.addWidget(scroll_area)
            return tab

        # قسم المستخدم الحالي - يفترض أن هناك نظام تسجيل دخول يحفظ معلومات المستخدم الحالي
        # هنا نعرض معلومات المستخدم admin افتراضياً
        current_user_group = QGroupBox("المستخدم الحالي")
        current_user_layout = QFormLayout()
        current_user_layout.setLabelAlignment(QtCore.Qt.AlignmentFlag.AlignRight)
        current_user_layout.setSpacing(12)

        # معلومات المستخدم
        self.current_user_name = QLabel("المدير")
        self.current_user_name.setFont(QFont("Arial", 10, QFont.Bold))
        current_user_layout.addRow("اسم المستخدم:", self.current_user_name)

        self.current_user_role = QLabel("مدير النظام")
        self.current_user_role.setStyleSheet("color: #3b82f6; font-weight: bold;")
        current_user_layout.addRow("الصلاحية:", self.current_user_role)

        # أزرار تغيير كلمة المرور وتسجيل الخروج
        buttons_layout = QHBoxLayout()

        change_password_btn = QPushButton("تغيير كلمة المرور")
        change_password_btn.setObjectName("secondary_button")
        change_password_btn.clicked.connect(self.change_current_password)

        logout_btn = QPushButton("تسجيل الخروج")
        logout_btn.setObjectName("danger_button")
        logout_btn.clicked.connect(self.logout_user)

        buttons_layout.addWidget(change_password_btn)
        buttons_layout.addWidget(logout_btn)
        buttons_layout.addStretch()

        current_user_layout.addRow("", buttons_layout)

        current_user_group.setLayout(current_user_layout)
        main_layout.addWidget(current_user_group)

        # قسم إدارة المستخدمين
        users_group = QGroupBox("إدارة المستخدمين")
        users_layout = QVBoxLayout()

        # جدول المستخدمين
        self.users_table = QTableWidget()
        self.users_table.setColumnCount(4)
        self.users_table.setHorizontalHeaderLabels(["اسم المستخدم", "الاسم الكامل", "الصلاحية", "الحالة"])

        # ضبط خصائص الجدول
        header = self.users_table.horizontalHeader()
        if header:
            header.setSectionResizeMode(QHeaderView.Stretch)
        v_header = self.users_table.verticalHeader()
        if v_header:
            v_header.setVisible(False)
        self.users_table.setAlternatingRowColors(True)
        self.users_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.users_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.users_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)  # تمكين قائمة السياق المخصصة
        self.users_table.customContextMenuRequested.connect(self.show_users_context_menu)  # ربط حدث طلب قائمة السياق

        # تحميل بيانات المستخدمين من قاعدة البيانات
        self.load_users_data()

        users_layout.addWidget(self.users_table)

        # أزرار إدارة المستخدمين مع التحقق من الصلاحيات
        user_buttons_layout = QHBoxLayout()

        # التحقق من صلاحيات المستخدم للأزرار
        has_add_user_permission = user_id is None or self._is_admin_user(main_window) or UserController.check_permission(user_id, "إضافة مستخدم")
        has_edit_user_permission = user_id is None or self._is_admin_user(main_window) or UserController.check_permission(user_id, "تعديل مستخدم")
        has_delete_user_permission = user_id is None or self._is_admin_user(main_window) or UserController.check_permission(user_id, "حذف مستخدم")

        self.add_user_btn = QPushButton("➕  إضافة مستخدم جديد")
        self.add_user_btn.setObjectName("action_button")
        self.add_user_btn.clicked.connect(self.add_user)
        
        # تطبيق صلاحية إضافة المستخدم
        if not has_add_user_permission:
            self.add_user_btn.setEnabled(False)
            self.add_user_btn.setToolTip("ليس لديك صلاحية إضافة مستخدمين جدد")
            self.add_user_btn.setStyleSheet("QPushButton { background-color: #6b7280; color: #9ca3af; }")

        self.edit_user_btn = QPushButton("✏️  تعديل المستخدم المحدد")
        self.edit_user_btn.setObjectName("secondary_button")
        self.edit_user_btn.clicked.connect(self.edit_user)
        
        # تطبيق صلاحية تعديل المستخدم
        if not has_edit_user_permission:
            self.edit_user_btn.setEnabled(False)
            self.edit_user_btn.setToolTip("ليس لديك صلاحية تعديل المستخدمين")
            self.edit_user_btn.setStyleSheet("QPushButton { background-color: #6b7280; color: #9ca3af; }")

        self.delete_user_btn = QPushButton("❌  حذف المستخدم المحدد")
        self.delete_user_btn.setObjectName("danger_button")
        self.delete_user_btn.clicked.connect(self.delete_user)
        
        # تطبيق صلاحية حذف المستخدم
        if not has_delete_user_permission:
            self.delete_user_btn.setEnabled(False)
            self.delete_user_btn.setToolTip("ليس لديك صلاحية حذف المستخدمين")
            self.delete_user_btn.setStyleSheet("QPushButton { background-color: #6b7280; color: #9ca3af; }")

        user_buttons_layout.addWidget(self.add_user_btn)
        user_buttons_layout.addWidget(self.edit_user_btn)
        user_buttons_layout.addWidget(self.delete_user_btn)
        user_buttons_layout.addStretch()

        users_layout.addLayout(user_buttons_layout)
        users_group.setLayout(users_layout)
        main_layout.addWidget(users_group, 1)  # إعطاء وزن 1 للتمدد

        # إضافة مساحة مرنة في النهاية لدفع المحتوى إلى الأعلى
        main_layout.addStretch()

        # تعيين widget المحتوى في منطقة التمرير
        scroll_area.setWidget(content_widget)

        # إضافة منطقة التمرير إلى التبويب
        tab_layout.addWidget(scroll_area)

        return tab

    def load_users_data(self):
        """تحميل بيانات المستخدمين من قاعدة البيانات"""
        try:
            # تفريغ الجدول أولاً
            self.users_table.setRowCount(0)

            # الحصول على بيانات المستخدمين من قاعدة البيانات
            users = UserController.get_all_users()

            # ملء الجدول بالبيانات
            for row, user in enumerate(users):
                self.users_table.insertRow(row)
                self.users_table.setItem(row, 0, QTableWidgetItem(user['username']))
                self.users_table.setItem(row, 1, QTableWidgetItem(user['full_name']))
                self.users_table.setItem(row, 2, QTableWidgetItem(user['role']))

                status_item = QTableWidgetItem(user['status'])
                if user['is_active']:
                    status_item.setForeground(QColor("#27ae60"))  # لون أخضر للمستخدمين النشطين
                else:
                    status_item.setForeground(QColor("#e74c3c"))  # لون أحمر للمستخدمين غير النشطين
                self.users_table.setItem(row, 3, status_item)

                # تخزين معرف المستخدم كبيانات إضافية
                self.users_table.item(row, 0).setData(Qt.ItemDataRole.UserRole, user['id'])

            print(f"تم تحميل {len(users)} مستخدم من قاعدة البيانات")
        except Exception as e:
            QMessageBox.critical(self, "خطأ في تحميل البيانات", f"حدث خطأ أثناء تحميل بيانات المستخدمين: {str(e)}")

    def load_permissions_data(self):
        """تحميل بيانات صلاحيات الأدوار من قاعدة البيانات"""
        try:
            # تفريغ الجدول أولاً
            self.permissions_table.setRowCount(0)

            # الحصول على بيانات الصلاحيات من قاعدة البيانات
            permissions = UserController.get_role_permissions()

            # الصلاحيات المتاحة - مجموعة بحسب الوظائف
            available_permissions = {
                "المبيعات": [
                    "عرض المبيعات",
                    "إضافة عملية بيع",
                    "تعديل عملية بيع",
                    "حذف عملية بيع",
                    "تعديل سعر المنتج في الفاتورة",
                    "تعديل كمية المنتج في الفاتورة",
                    "طباعة فاتورة مبيعات"
                ],
                "المنتجات": [
                    "عرض المنتجات",
                    "إضافة منتج",
                    "تعديل منتج",
                    "حذف منتج",
                    "إدارة فئات المنتجات",
                    "تعديل أسعار المنتجات"
                ],
                "المخزون": [
                    "عرض المخزون",
                    "إضافة للمخزون",
                    "تعديل المخزون"
                ],
                "الفواتير": [
                    "عرض الفواتير",
                    "إلغاء فاتورة",
                    "طباعة فاتورة",
                    "تعديل فاتورة"
                ],
                "التقارير": [
                    "عرض تقارير المبيعات",
                    "عرض تقارير المخزون",
                    "عرض تقارير الأرباح",
                    "عرض تقارير العملاء",
                    "عرض تقرير المنتجات الأكثر مبيعاً"
                ],
                "العملاء": [
                    "عرض العملاء",
                    "إضافة عميل",
                    "تعديل عميل",
                    "حذف عميل",
                    "إدارة ديون العملاء",
                    "عرض تفاصيل العميل"
                ],
                "الموردين": [
                    "عرض الموردين",
                    "إضافة مورد",
                    "تعديل مورد",
                    "حذف مورد",
                    "إدارة مدفوعات الموردين",
                    "عرض تفاصيل المورد"
                ],
                "المصروفات": [
                    "عرض المصروفات",
                    "إضافة مصروف",
                    "تعديل مصروف",
                    "حذف مصروف"
                ],
                "المستخدمين": [
                    "عرض المستخدمين",
                    "إضافة مستخدم",
                    "تعديل مستخدم",
                    "حذف مستخدم",
                    "إدارة صلاحيات المستخدمين"
                ],
                "الإعدادات": [
                    "عرض الإعدادات",
                    "تعديل إعدادات النظام",
                    "إدارة النسخ الاحتياطي",
                    "استعادة النسخ الاحتياطي"
                ]
            }

            # إعادة تهيئة جدول الصلاحيات
            self.permissions_table.clear()
            self.permissions_table.setColumnCount(4)
            self.permissions_table.setHorizontalHeaderLabels(["الصلاحية", "مدير", "كاشير", "مدير مخزون"])

            # تتبع الصف الحالي
            current_row = 0

            # ملء الجدول بالبيانات المجمعة حسب الفئات
            for category, perms in available_permissions.items():
                # إضافة صف لعنوان الفئة
                self.permissions_table.insertRow(current_row)
                category_item = QTableWidgetItem(category)
                category_item.setBackground(QColor("#f1f5f9"))  # لون خلفية فاتح للفئة
                font = category_item.font()
                font.setBold(True)
                category_item.setFont(font)
                self.permissions_table.setItem(current_row, 0, category_item)

                # إضافة زر "تحديد الكل" للفئة
                for col, role in enumerate(["مدير", "كاشير", "مدير مخزون"]):
                    # إنشاء محتوى مخصص لوضع الـ checkbox في وسط الخلية
                    cell_widget = QWidget()
                    cell_layout = QHBoxLayout(cell_widget)
                    cell_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
                    cell_layout.setContentsMargins(0, 0, 0, 0)

                    # إنشاء checkbox للفئة
                    category_checkbox = QCheckBox()

                    # تعيين حالة الـ checkbox بناءً على البيانات المستلمة
                    # المدير دائماً لديه كل الصلاحيات
                    is_checked = True if role == "مدير" else False

                    # تعطيل التعديل للمدير (يجب أن يكون لديه كل الصلاحيات)
                    if role == "مدير":
                        category_checkbox.setEnabled(False)
                    else:
                        # ربط الـ checkbox بوظيفة تغيير صلاحيات الفئة
                        from functools import partial
                        category_checkbox.stateChanged.connect(
                            partial(self.on_category_permission_changed, category, current_row, col+1)
                        )

                    category_checkbox.setChecked(is_checked)
                    cell_layout.addWidget(category_checkbox)
                    self.permissions_table.setCellWidget(current_row, col + 1, cell_widget)

                current_row += 1

                # إضافة صفوف للصلاحيات الفرعية
                for perm_name in perms:
                    self.permissions_table.insertRow(current_row)
                    perm_item = QTableWidgetItem("    " + perm_name)  # إضافة مسافات للإزاحة
                    self.permissions_table.setItem(current_row, 0, perm_item)

                    # إنشاء خلايا للصلاحيات
                    for col, role in enumerate(["مدير", "كاشير", "مدير مخزون"]):
                        checkbox = QCheckBox()
                        # تعيين حالة الـ checkbox بناءً على البيانات المستلمة
                        is_checked = True if role == "مدير" else False

                        if role in permissions and perm_name in permissions[role]:
                            is_checked = permissions[role][perm_name]

                        checkbox.setChecked(is_checked)

                        # تعطيل التعديل للمدير (يجب أن يكون لديه كل الصلاحيات)
                        if role == "مدير":
                            checkbox.setEnabled(False)
                        else:
                            # ربط الـ checkbox بوظيفة تغيير الصلاحيات
                            # استخدام functools.partial لتجنب مشاكل الـ lambda مع الحلقات
                            from functools import partial
                            checkbox.stateChanged.connect(
                                partial(self.on_permission_changed, current_row, col+1, perm_name, category)
                            )

                        # إنشاء محتوى مخصص لوضع الـ checkbox في وسط الخلية
                        cell_widget = QWidget()
                        cell_layout = QHBoxLayout(cell_widget)
                        cell_layout.setAlignment(Qt.AlignmentFlag.AlignCenter)
                        cell_layout.setContentsMargins(0, 0, 0, 0)
                        cell_layout.addWidget(checkbox)

                        self.permissions_table.setCellWidget(current_row, col + 1, cell_widget)

                    current_row += 1

            print("تم تحميل بيانات الصلاحيات")
        except Exception as e:
            QMessageBox.critical(self, "خطأ في تحميل البيانات", f"حدث خطأ أثناء تحميل بيانات الصلاحيات: {str(e)}")

    def change_current_password(self):
        """تغيير كلمة مرور المستخدم الحالي"""
        try:
            # في هذا المثال نفترض أن المستخدم الحالي هو admin
            # في التطبيق الحقيقي يجب استخدام معرف المستخدم الحالي من نظام تسجيل الدخول
            dialog = QDialog(self)
            dialog.setWindowTitle("تغيير كلمة المرور")
            dialog.setMinimumWidth(400)

            # تخطيط النموذج
            form_layout = QFormLayout(dialog)

            # حقول كلمة المرور
            old_password = QLineEdit()
            old_password.setObjectName("search_input")
            old_password.setPlaceholderText("أدخل كلمة المرور الحالية")
            old_password.setEchoMode(QLineEdit.Password)

            new_password = QLineEdit()
            new_password.setObjectName("search_input")
            new_password.setPlaceholderText("أدخل كلمة المرور الجديدة")
            new_password.setEchoMode(QLineEdit.Password)

            confirm_password = QLineEdit()
            confirm_password.setObjectName("search_input")
            confirm_password.setPlaceholderText("تأكيد كلمة المرور الجديدة")
            confirm_password.setEchoMode(QLineEdit.Password)

            # إضافة الحقول إلى التخطيط
            form_layout.addRow("كلمة المرور الحالية:", old_password)
            form_layout.addRow("كلمة المرور الجديدة:", new_password)
            form_layout.addRow("تأكيد كلمة المرور:", confirm_password)

            # أزرار الإجراءات
            button_box = QHBoxLayout()
            save_btn = QPushButton("تغيير كلمة المرور")
            save_btn.setObjectName("action_button")

            cancel_btn = QPushButton("إلغاء")
            cancel_btn.setObjectName("secondary_button")

            button_box.addWidget(save_btn)
            button_box.addWidget(cancel_btn)

            form_layout.addRow("", button_box)

            # ربط الأزرار بالوظائف
            cancel_btn.clicked.connect(dialog.reject)

            # وظيفة تغيير كلمة المرور
            def change_password():
                # التحقق من الإدخال
                if not old_password.text() or not new_password.text() or not confirm_password.text():
                    QMessageBox.warning(dialog, "بيانات غير مكتملة", "يرجى ملء جميع الحقول")
                    return

                # التحقق من تطابق كلمة المرور الجديدة
                if new_password.text() != confirm_password.text():
                    QMessageBox.warning(dialog, "كلمات المرور غير متطابقة", "كلمة المرور الجديدة وتأكيدها غير متطابقين")
                    return

                # تغيير كلمة المرور - افتراضياً نستخدم المستخدم الأول (admin)
                admin_user = UserController.get_user_info(username="admin")
                if admin_user:
                    success, message = UserController.change_password(
                        admin_user['id'], old_password.text(), new_password.text()
                    )

                    if success:
                        QMessageBox.information(dialog, "تم تغيير كلمة المرور", message)
                        dialog.accept()
                    else:
                        QMessageBox.warning(dialog, "خطأ", message)
                else:
                    QMessageBox.warning(dialog, "خطأ", "لم يتم العثور على المستخدم")

            save_btn.clicked.connect(change_password)

            # عرض الحوار
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تغيير كلمة المرور: {str(e)}")

    def logout_user(self):
        """تسجيل خروج المستخدم"""
        reply = QMessageBox.question(
            self,
            "تسجيل الخروج",
            "هل أنت متأكد من رغبتك بتسجيل الخروج؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            QMessageBox.information(self, "تسجيل الخروج", "تم تسجيل الخروج بنجاح")

            # استيراد المكتبات اللازمة
            from views.login import LoginWindow
            from PyQt5.QtWidgets import QApplication

            # الحصول على النافذة الرئيسية
            main_window = self.window()

            # إخفاء النافذة الرئيسية
            main_window.hide()

            # إنشاء وعرض نافذة تسجيل الدخول
            login_window = LoginWindow()

            # إذا تم تسجيل الدخول بنجاح، أعد فتح النافذة الرئيسية
            if login_window.exec_() == LoginWindow.Accepted:
                # تحديث بيانات المستخدم في النافذة الرئيسية
                main_window.current_user = login_window.user_data
                main_window.update_user_info()
                main_window.show()
            else:
                # إغلاق التطبيق إذا تم إلغاء تسجيل الدخول
                import sys
                QApplication.quit()
                sys.exit(0)

    def browse_db_file(self):
        """اختيار مسار ملف قاعدة البيانات"""
        file_name, _ = QFileDialog.getSaveFileName(
            self,
            "اختر مسار ملف قاعدة البيانات",
            "",
            "قواعد بيانات SQLite (*.db *.sqlite);;كل الملفات (*.*)"
        )
        if file_name:
            self.db_path_input.setText(file_name)

    def test_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        # جمع إعدادات الاتصال الحالية من واجهة المستخدم
        db_settings = {
            "db_type": "sqlite",
            "db_path": self.db_path_input.text(),
            "db_host": "",
            "db_port": "",
            "db_name": "",
            "db_user": "",
            "db_password": ""
        }

        try:
            # استخدام المتحكم لاختبار الاتصال
            db_controller = DatabaseController()
            success, message = db_controller.test_connection(db_settings)

            if success:
                QMessageBox.information(
                    self,
                    "نجاح الاتصال",
                    f"تم الاتصال بقاعدة البيانات SQLite بنجاح.\n\n{message}"
                )
            else:
                QMessageBox.critical(
                    self,
                    "فشل الاتصال",
                    f"فشل الاتصال بقاعدة البيانات: {message}"
                )
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء محاولة الاتصال: {str(e)}"
            )

    def test_database_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        # جمع إعدادات الاتصال الحالية من واجهة المستخدم
        db_settings = {
            "db_type": "sqlite",
            "db_path": self.db_path_input.text(),
            "db_host": "",
            "db_port": "",
            "db_name": "",
            "db_user": "",
            "db_password": ""
        }

        try:
            # استخدام المتحكم لاختبار الاتصال
            db_controller = DatabaseController()
            success, message = db_controller.test_connection(db_settings)

            if success:
                QMessageBox.information(
                    self,
                    "نجاح الاتصال",
                    f"تم الاتصال بقاعدة البيانات SQLite بنجاح.\n\n{message}"
                )
            else:
                QMessageBox.critical(
                    self,
                    "فشل الاتصال",
                    f"فشل الاتصال بقاعدة البيانات: {message}"
                )
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء محاولة الاتصال: {str(e)}"
            )

    def export_products_data(self):
        """تصدير بيانات المنتجات إلى CSV"""
        try:
            # الحصول على بيانات المنتجات
            from models.products import ProductModel
            products = ProductModel.get_all_products()

            if not products:
                QMessageBox.information(
                    self,
                    "لا توجد بيانات",
                    "لا توجد منتجات للتصدير"
                )
                return

            # اختيار مكان حفظ الملف
            file_name, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ ملف المنتجات",
                "products.csv",
                "ملفات CSV (*.csv);;كل الملفات (*.*)"
            )

            if file_name:
                import csv

                # كتابة البيانات إلى ملف CSV
                with open(file_name, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    fieldnames = ['الرقم', 'الكود', 'اسم المنتج', 'الوصف', 'الفئة', 'سعر البيع', 'سعر التكلفة', 'الكمية', 'الحد الأدنى', 'نوع المنتج', 'مفضل']
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                    # كتابة العناوين
                    writer.writeheader()

                    # كتابة بيانات المنتجات
                    for product in products:
                        writer.writerow({
                            'الرقم': product.get('id', ''),
                            'الكود': product.get('code', ''),
                            'اسم المنتج': product.get('name', ''),
                            'الوصف': product.get('description', ''),
                            'الفئة': product.get('category', ''),
                            'سعر البيع': product.get('price', 0),
                            'سعر التكلفة': product.get('cost', 0),
                            'الكمية': product.get('stock', 0),
                            'الحد الأدنى': product.get('min_quantity', 1),
                            'نوع المنتج': product.get('product_type', 'physical'),
                            'مفضل': 'نعم' if product.get('is_favorite') else 'لا'
                        })

                QMessageBox.information(
                    self,
                    "تم التصدير بنجاح",
                    f"تم تصدير {len(products)} منتج بنجاح إلى:\n{file_name}"
                )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ في التصدير",
                f"حدث خطأ أثناء تصدير بيانات المنتجات:\n\n{str(e)}"
            )

    def export_customers_data(self):
        """تصدير بيانات العملاء إلى CSV"""
        try:
            # الحصول على بيانات العملاء
            from models.customers import CustomerModel
            customers = CustomerModel.get_all_customers()

            if not customers:
                QMessageBox.information(
                    self,
                    "لا توجد بيانات",
                    "لا توجد عملاء للتصدير"
                )
                return

            # اختيار مكان حفظ الملف
            file_name, _ = QFileDialog.getSaveFileName(
                self,
                "حفظ ملف العملاء",
                "customers.csv",
                "ملفات CSV (*.csv);;كل الملفات (*.*)"
            )

            if file_name:
                import csv

                # كتابة البيانات إلى ملف CSV
                with open(file_name, 'w', newline='', encoding='utf-8-sig') as csvfile:
                    fieldnames = ['الرقم', 'اسم العميل', 'رقم الهاتف', 'البريد الإلكتروني', 'العنوان', 'إجمالي المشتريات', 'آخر زيارة', 'عدد الزيارات']
                    writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

                    # كتابة العناوين
                    writer.writeheader()

                    # كتابة بيانات العملاء
                    for customer in customers:
                        writer.writerow({
                            'الرقم': customer.get('id', ''),
                            'اسم العميل': customer.get('name', ''),
                            'رقم الهاتف': customer.get('phone', ''),
                            'البريد الإلكتروني': customer.get('email', ''),
                            'العنوان': customer.get('address', ''),
                            'إجمالي المشتريات': customer.get('total_purchases', 0),
                            'آخر زيارة': customer.get('last_purchase', ''),
                            'عدد الزيارات': customer.get('visit_count', 0)
                        })

                QMessageBox.information(
                    self,
                    "تم التصدير بنجاح",
                    f"تم تصدير {len(customers)} عميل بنجاح إلى:\n{file_name}"
                )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ في التصدير",
                f"حدث خطأ أثناء تصدير بيانات العملاء:\n\n{str(e)}"
            )

    def import_products_data(self):
        """استيراد بيانات المنتجات من CSV"""
        try:
            # اختيار ملف الاستيراد
            file_name, _ = QFileDialog.getOpenFileName(
                self,
                "اختر ملف المنتجات",
                "",
                "ملفات CSV (*.csv);;كل الملفات (*.*)"
            )

            if not file_name:
                return

            # التأكيد قبل الاستيراد
            reply = QMessageBox.question(
                self,
                "تأكيد الاستيراد",
                f"هل تريد استيراد بيانات المنتجات من:\n{file_name}\n\nسيتم دمج البيانات مع البيانات الموجودة.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            import csv
            from models.products import ProductModel

            imported_count = 0
            updated_count = 0
            error_count = 0
            errors = []

            # قراءة ملف CSV
            with open(file_name, 'r', encoding='utf-8-sig') as csvfile:
                # محاولة تحديد الفاصل تلقائياً
                sample = csvfile.read(1024)
                csvfile.seek(0)
                sniffer = csv.Sniffer()
                delimiter = sniffer.sniff(sample).delimiter

                reader = csv.DictReader(csvfile, delimiter=delimiter)

                for row_num, row in enumerate(reader, start=2):  # البدء من الصف 2 (بعد العناوين)
                    try:
                        # تنظيف البيانات وتحويلها
                        product_data = {}

                        # معالجة الحقول المختلفة (العربية والإنجليزية)
                        name_fields = ['اسم المنتج', 'name', 'product_name', 'Name', 'Product Name']
                        code_fields = ['الكود', 'code', 'product_code', 'Code', 'Product Code']
                        price_fields = ['سعر البيع', 'price', 'sell_price', 'Price', 'Sell Price']
                        cost_fields = ['سعر التكلفة', 'cost', 'cost_price', 'Cost', 'Cost Price']
                        stock_fields = ['الكمية', 'stock', 'quantity', 'Stock', 'Quantity']

                        # البحث عن اسم المنتج
                        product_name = None
                        for field in name_fields:
                            if field in row and row[field].strip():
                                product_name = row[field].strip()
                                break

                        if not product_name:
                            errors.append(f"الصف {row_num}: اسم المنتج مطلوب")
                            error_count += 1
                            continue

                        product_data['name'] = product_name

                        # البحث عن كود المنتج
                        for field in code_fields:
                            if field in row and row[field].strip():
                                product_data['code'] = row[field].strip()
                                break

                        # البحث عن السعر
                        for field in price_fields:
                            if field in row and row[field].strip():
                                try:
                                    product_data['price'] = float(row[field].strip())
                                except ValueError:
                                    pass
                                break

                        # البحث عن التكلفة
                        for field in cost_fields:
                            if field in row and row[field].strip():
                                try:
                                    product_data['cost'] = float(row[field].strip())
                                except ValueError:
                                    pass
                                break

                        # البحث عن الكمية
                        for field in stock_fields:
                            if field in row and row[field].strip():
                                try:
                                    product_data['stock'] = int(float(row[field].strip()))
                                except ValueError:
                                    pass
                                break

                        # إضافة الحقول الأخرى
                        if 'الوصف' in row:
                            product_data['description'] = row['الوصف'].strip()
                        elif 'description' in row:
                            product_data['description'] = row['description'].strip()

                        if 'الفئة' in row:
                            product_data['category'] = row['الفئة'].strip()
                        elif 'category' in row:
                            product_data['category'] = row['category'].strip()

                        # التحقق من وجود المنتج
                        existing_product = None
                        if 'code' in product_data:
                            existing_product = ProductModel.get_product_by_code(product_data['code'])

                        if existing_product:
                            # تحديث المنتج الموجود
                            ProductModel.update_product(existing_product['id'], product_data)
                            updated_count += 1
                        else:
                            # إضافة منتج جديد
                            # إنشاء كود تلقائي إذا لم يكن موجود
                            if 'code' not in product_data or not product_data['code']:
                                import random
                                product_data['code'] = f"AUTO_{random.randint(1000, 9999)}"

                            # تعيين قيم افتراضية
                            product_data.setdefault('price', 0)
                            product_data.setdefault('cost', 0)
                            product_data.setdefault('stock', 0)
                            product_data.setdefault('min_quantity', 1)
                            product_data.setdefault('description', '')
                            product_data.setdefault('category', 'غير مصنف')
                            product_data.setdefault('product_type', 'physical')
                            product_data.setdefault('is_favorite', 0)

                            ProductModel.add_product(product_data)
                            imported_count += 1

                    except Exception as e:
                        errors.append(f"الصف {row_num}: {str(e)}")
                        error_count += 1

            # عرض نتائج الاستيراد
            message = f"تم الاستيراد بنجاح!\n\n"
            message += f"المنتجات الجديدة: {imported_count}\n"
            message += f"المنتجات المحدثة: {updated_count}\n"

            if error_count > 0:
                message += f"الأخطاء: {error_count}\n\n"
                if len(errors) <= 5:
                    message += "تفاصيل الأخطاء:\n" + "\n".join(errors)
                else:
                    message += f"تفاصيل أول 5 أخطاء:\n" + "\n".join(errors[:5])
                    message += f"\n... و {len(errors) - 5} أخطاء أخرى"

            if error_count > 0 and (imported_count + updated_count) == 0:
                QMessageBox.critical(self, "فشل الاستيراد", message)
            elif error_count > 0:
                QMessageBox.warning(self, "اكتمل الاستيراد مع أخطاء", message)
            else:
                QMessageBox.information(self, "تم الاستيراد بنجاح", message)

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ في الاستيراد",
                f"حدث خطأ أثناء استيراد بيانات المنتجات:\n\n{str(e)}"
            )

    def import_customers_data(self):
        """استيراد بيانات العملاء من CSV"""
        try:
            # اختيار ملف الاستيراد
            file_name, _ = QFileDialog.getOpenFileName(
                self,
                "اختر ملف العملاء",
                "",
                "ملفات CSV (*.csv);;كل الملفات (*.*)"
            )

            if not file_name:
                return

            # التأكيد قبل الاستيراد
            reply = QMessageBox.question(
                self,
                "تأكيد الاستيراد",
                f"هل تريد استيراد بيانات العملاء من:\n{file_name}\n\nسيتم دمج البيانات مع البيانات الموجودة.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply != QMessageBox.Yes:
                return

            import csv
            from models.customers import CustomerModel

            imported_count = 0
            updated_count = 0
            error_count = 0
            errors = []

            # قراءة ملف CSV
            with open(file_name, 'r', encoding='utf-8-sig') as csvfile:
                # محاولة تحديد الفاصل تلقائياً
                sample = csvfile.read(1024)
                csvfile.seek(0)
                sniffer = csv.Sniffer()
                delimiter = sniffer.sniff(sample).delimiter

                reader = csv.DictReader(csvfile, delimiter=delimiter)

                for row_num, row in enumerate(reader, start=2):  # البدء من الصف 2 (بعد العناوين)
                    try:
                        # تنظيف البيانات وتحويلها
                        customer_data = {}

                        # معالجة الحقول المختلفة (العربية والإنجليزية)
                        name_fields = ['اسم العميل', 'name', 'customer_name', 'Name', 'Customer Name']
                        phone_fields = ['رقم الهاتف', 'phone', 'Phone', 'mobile', 'Mobile']
                        email_fields = ['البريد الإلكتروني', 'email', 'Email', 'e-mail', 'E-mail']
                        address_fields = ['العنوان', 'address', 'Address']

                        # البحث عن اسم العميل
                        customer_name = None
                        for field in name_fields:
                            if field in row and row[field].strip():
                                customer_name = row[field].strip()
                                break

                        if not customer_name:
                            errors.append(f"الصف {row_num}: اسم العميل مطلوب")
                            error_count += 1
                            continue

                        customer_data['name'] = customer_name

                        # البحث عن رقم الهاتف
                        for field in phone_fields:
                            if field in row and row[field].strip():
                                customer_data['phone'] = row[field].strip()
                                break

                        # البحث عن البريد الإلكتروني
                        for field in email_fields:
                            if field in row and row[field].strip():
                                customer_data['email'] = row[field].strip()
                                break

                        # البحث عن العنوان
                        for field in address_fields:
                            if field in row and row[field].strip():
                                customer_data['address'] = row[field].strip()
                                break

                        # التحقق من وجود العميل (بالاسم أو رقم الهاتف)
                        existing_customer = None
                        if 'phone' in customer_data and customer_data['phone']:
                            existing_customer = CustomerModel.get_customer_by_phone(customer_data['phone'])

                        if not existing_customer:
                            existing_customer = CustomerModel.get_customer_by_name(customer_name)

                        if existing_customer:
                            # تحديث العميل الموجود
                            CustomerModel.update_customer(existing_customer['id'], customer_data)
                            updated_count += 1
                        else:
                            # إضافة عميل جديد
                            # تعيين قيم افتراضية
                            customer_data.setdefault('phone', '')
                            customer_data.setdefault('email', '')
                            customer_data.setdefault('address', '')
                            customer_data.setdefault('total_purchases', 0)
                            customer_data.setdefault('visit_count', 0)

                            CustomerModel.add_customer(customer_data)
                            imported_count += 1

                    except Exception as e:
                        errors.append(f"الصف {row_num}: {str(e)}")
                        error_count += 1

            # عرض نتائج الاستيراد
            message = f"تم الاستيراد بنجاح!\n\n"
            message += f"العملاء الجدد: {imported_count}\n"
            message += f"العملاء المحدثون: {updated_count}\n"

            if error_count > 0:
                message += f"الأخطاء: {error_count}\n\n"
                if len(errors) <= 5:
                    message += "تفاصيل الأخطاء:\n" + "\n".join(errors)
                else:
                    message += f"تفاصيل أول 5 أخطاء:\n" + "\n".join(errors[:5])
                    message += f"\n... و {len(errors) - 5} أخطاء أخرى"

            if error_count > 0 and (imported_count + updated_count) == 0:
                QMessageBox.critical(self, "فشل الاستيراد", message)
            elif error_count > 0:
                QMessageBox.warning(self, "اكتمل الاستيراد مع أخطاء", message)
            else:
                QMessageBox.information(self, "تم الاستيراد بنجاح", message)

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ في الاستيراد",
                f"حدث خطأ أثناء استيراد بيانات العملاء:\n\n{str(e)}"
            )

    def browse_backup_folder(self):
        """اختيار مجلد النسخ الاحتياطي"""
        dir_path = QFileDialog.getExistingDirectory(
            self,
            "اختر مجلد النسخ الاحتياطي",
            ""
        )
        if dir_path:
            self.backup_folder_input.setText(dir_path)

    def create_manual_backup(self):
        """إنشاء نسخة احتياطية يدوياً"""
        # التحقق من صلاحية إدارة النسخ الاحتياطي
        try:
            # الحصول على النافذة الرئيسية
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            user_id = None
            current_username = None
            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                current_username = main_window.current_user.get('username')

            from controllers.user_controller import UserController
            has_backup_permission = user_id is None or current_username == 'admin' or UserController.check_permission(user_id, "إدارة النسخ الاحتياطي")

            if not has_backup_permission:
                QMessageBox.warning(self, "غير مسموح", "ليس لديك صلاحية إنشاء نسخ احتياطية")
                return
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التحقق من الصلاحيات: {str(e)}")
            return

        try:
            import os
            import shutil
            from datetime import datetime

            # الحصول على مسار قاعدة البيانات الحالية
            current_db_path = self.db_path_input.text() or "database/store.db"

            # التحقق من وجود ملف قاعدة البيانات
            if not os.path.exists(current_db_path):
                QMessageBox.warning(
                    self,
                    "خطأ",
                    f"لا يمكن العثور على ملف قاعدة البيانات:\n{current_db_path}"
                )
                return

            # الحصول على مسار مجلد النسخ الاحتياطي
            backup_folder = self.backup_folder_input.text() or "backups"

            # إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
            if not os.path.exists(backup_folder):
                os.makedirs(backup_folder)

            # إنشاء اسم الملف بناءً على التاريخ والوقت الحالي
            timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            backup_filename = f"backup_{timestamp}.db"
            backup_path = os.path.join(backup_folder, backup_filename)

            # نسخ ملف قاعدة البيانات
            shutil.copy2(current_db_path, backup_path)

            # الحصول على حجم الملف
            file_size = os.path.getsize(backup_path)
            if file_size < 1024:
                size_str = f"{file_size} بايت"
            elif file_size < 1024 * 1024:
                size_str = f"{file_size / 1024:.1f} كيلوبايت"
            else:
                size_str = f"{file_size / (1024 * 1024):.1f} ميجابايت"

            # عرض رسالة نجاح مع المعلومات الفعلية
            QMessageBox.information(
                self,
                "تم إنشاء النسخة الاحتياطية بنجاح",
                f"تم إنشاء نسخة احتياطية بنجاح!\n\n"
                f"📁 اسم الملف: {backup_filename}\n"
                f"📍 المسار الكامل: {backup_path}\n"
                f"📊 حجم الملف: {size_str}\n"
                f"📅 تاريخ الإنشاء: {datetime.now().strftime('%Y/%m/%d %H:%M:%S')}"
            )

            # تحديث قائمة النسخ الاحتياطية
            self.refresh_backups_list()

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ في إنشاء النسخة الاحتياطية",
                f"حدث خطأ أثناء إنشاء النسخة الاحتياطية:\n\n{str(e)}"
            )

    def restore_backup(self):
        """استعادة نسخة احتياطية من ملف"""
        # التحقق من صلاحية استعادة النسخ الاحتياطي
        try:
            # الحصول على النافذة الرئيسية
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            user_id = None
            current_username = None
            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                current_username = main_window.current_user.get('username')

            from controllers.user_controller import UserController
            has_restore_permission = user_id is None or current_username == 'admin' or UserController.check_permission(user_id, "استعادة النسخ الاحتياطي")

            if not has_restore_permission:
                QMessageBox.warning(self, "غير مسموح", "ليس لديك صلاحية استعادة النسخ الاحتياطية")
                return
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التحقق من الصلاحيات: {str(e)}")
            return

        file_name, _ = QFileDialog.getOpenFileName(
            self,
            "اختر ملف النسخة الاحتياطية",
            "",
            "قواعد بيانات (*.db *.sqlite);;كل الملفات (*.*)"
        )
        if file_name:
            # التأكيد قبل الاستعادة
            reply = QMessageBox.warning(
                self,
                "تأكيد الاستعادة",
                f"هل أنت متأكد من استعادة النسخة الاحتياطية من الملف:\n\n"
                f"📁 {file_name}\n\n"
                f"⚠️ تحذير: ستتم استبدال البيانات الحالية بالكامل!\n"
                f"هذا الإجراء لا يمكن التراجع عنه.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # تنفيذ عملية الاستعادة الفعلية
                from controllers.database_controller import DatabaseController

                try:
                    success, message = DatabaseController.restore_backup(file_name)

                    if success:
                        QMessageBox.information(
                            self,
                            "تمت الاستعادة",
                            f"تم استعادة النسخة الاحتياطية بنجاح!\n\n"
                            f"📁 الملف: {file_name.split('/')[-1]}\n"
                            f"✅ تم استبدال قاعدة البيانات الحالية"
                        )

                        # تحديث قائمة النسخ الاحتياطية
                        self.refresh_backups_list()

                        # تحديث جميع الصفحات في التطبيق
                        self.refresh_all_application_pages()

                    else:
                        QMessageBox.critical(
                            self,
                            "خطأ في الاستعادة",
                            f"فشل في استعادة النسخة الاحتياطية:\n\n{message}"
                        )

                except Exception as e:
                    QMessageBox.critical(
                        self,
                        "خطأ في الاستعادة",
                        f"حدث خطأ أثناء استعادة النسخة الاحتياطية:\n\n{str(e)}"
                    )

    def refresh_backups_list(self):
        """تحديث قائمة النسخ الاحتياطية"""
        try:
            import os
            from datetime import datetime

            # الحصول على مسار مجلد النسخ الاحتياطي
            backup_folder = self.backup_folder_input.text() or "backups"

            # مسح الجدول الحالي
            self.backups_table.setRowCount(0)

            # التحقق من وجود المجلد
            if not os.path.exists(backup_folder):
                # عرض رسالة في شريط الحالة
                window = self.window()
                status_bar = window.statusBar if hasattr(window, 'statusBar') else None
                if status_bar:
                    status_bar.showMessage("مجلد النسخ الاحتياطي غير موجود", 3000)
                return

            # البحث عن ملفات النسخ الاحتياطية
            backup_files = []
            for filename in os.listdir(backup_folder):
                if filename.endswith('.db') or filename.endswith('.sqlite'):
                    file_path = os.path.join(backup_folder, filename)
                    if os.path.isfile(file_path):
                        # الحصول على معلومات الملف
                        stat = os.stat(file_path)

                        # تنسيق التاريخ
                        modified_time = datetime.fromtimestamp(stat.st_mtime)
                        date_str = modified_time.strftime("%Y/%m/%d %H:%M")

                        # تنسيق حجم الملف
                        file_size = stat.st_size
                        if file_size < 1024:
                            size_str = f"{file_size} بايت"
                        elif file_size < 1024 * 1024:
                            size_str = f"{file_size / 1024:.1f} كيلوبايت"
                        else:
                            size_str = f"{file_size / (1024 * 1024):.1f} ميجابايت"

                        backup_files.append({
                            "name": filename,
                            "date": date_str,
                            "size": size_str,
                            "timestamp": stat.st_mtime
                        })

            # ترتيب الملفات حسب التاريخ (الأحدث أولاً)
            backup_files.sort(key=lambda x: x["timestamp"], reverse=True)

            # إضافة الملفات إلى الجدول
            for backup in backup_files:
                row = self.backups_table.rowCount()
                self.backups_table.insertRow(row)
                self.backups_table.setItem(row, 0, QTableWidgetItem(backup["name"]))
                self.backups_table.setItem(row, 1, QTableWidgetItem(backup["date"]))
                self.backups_table.setItem(row, 2, QTableWidgetItem(backup["size"]))

            # عرض رسالة في شريط الحالة
            window = self.window()
            status_bar = window.statusBar if hasattr(window, 'statusBar') else None
            if status_bar:
                count = len(backup_files)
                status_bar.showMessage(f"تم العثور على {count} نسخة احتياطية", 2000)

        except Exception as e:
            QMessageBox.warning(
                self,
                "خطأ في تحديث القائمة",
                f"حدث خطأ أثناء تحديث قائمة النسخ الاحتياطية:\n\n{str(e)}"
            )

    def setup_auto_backup(self):
        """إعداد النسخ الاحتياطي التلقائي"""
        try:
            # إيقاف المؤقت الحالي
            self.backup_timer.stop()

            # التحقق من تفعيل النسخ الاحتياطي التلقائي
            if not hasattr(self, 'auto_backup_enabled') or not self.auto_backup_enabled.isChecked():
                print("النسخ الاحتياطي التلقائي معطل")
                return

            # الحصول على تكرار النسخ الاحتياطي
            frequency = self.backup_frequency.currentText() if hasattr(self, 'backup_frequency') else "أسبوعياً"

            # تحويل التكرار إلى ميلي ثانية
            intervals = {
                "يومياً": 24 * 60 * 60 * 1000,      # 24 ساعة
                "أسبوعياً": 7 * 24 * 60 * 60 * 1000,  # 7 أيام
                "شهرياً": 30 * 24 * 60 * 60 * 1000    # 30 يوم
            }

            interval = intervals.get(frequency, intervals["أسبوعياً"])

            # بدء المؤقت
            self.backup_timer.start(interval)

            print(f"تم تفعيل النسخ الاحتياطي التلقائي: {frequency} ({interval/1000/60/60:.1f} ساعة)")

        except Exception as e:
            print(f"خطأ في إعداد النسخ الاحتياطي التلقائي: {str(e)}")

    def perform_auto_backup(self):
        """تنفيذ النسخ الاحتياطي التلقائي"""
        try:
            print("بدء النسخ الاحتياطي التلقائي...")

            import os
            import shutil
            from datetime import datetime

            # الحصول على مسار قاعدة البيانات الحالية
            current_db_path = self.db_path_input.text() if hasattr(self, 'db_path_input') else "database/store.db"

            # التحقق من وجود ملف قاعدة البيانات
            if not os.path.exists(current_db_path):
                print(f"لا يمكن العثور على ملف قاعدة البيانات: {current_db_path}")
                return

            # الحصول على مسار مجلد النسخ الاحتياطي
            backup_folder = self.backup_folder_input.text() if hasattr(self, 'backup_folder_input') else "backups"

            # إنشاء مجلد النسخ الاحتياطي إذا لم يكن موجوداً
            if not os.path.exists(backup_folder):
                os.makedirs(backup_folder)

            # إنشاء اسم الملف بناءً على التاريخ والوقت الحالي
            timestamp = datetime.now().strftime("%Y-%m-%d_%H-%M-%S")
            backup_filename = f"auto_backup_{timestamp}.db"
            backup_path = os.path.join(backup_folder, backup_filename)

            # نسخ ملف قاعدة البيانات
            shutil.copy2(current_db_path, backup_path)

            print(f"تم إنشاء نسخة احتياطية تلقائية: {backup_path}")

            # تنظيف النسخ القديمة
            self.cleanup_old_backups()

            # تحديث قائمة النسخ الاحتياطية إذا كان التاب مفتوح
            if hasattr(self, 'refresh_backups_list'):
                self.refresh_backups_list()

        except Exception as e:
            print(f"خطأ في النسخ الاحتياطي التلقائي: {str(e)}")

    def cleanup_old_backups(self):
        """تنظيف النسخ الاحتياطية القديمة"""
        try:
            import os
            from datetime import datetime

            # الحصول على مسار مجلد النسخ الاحتياطي
            backup_folder = self.backup_folder_input.text() if hasattr(self, 'backup_folder_input') else "backups"

            # الحصول على عدد النسخ المحتفظ بها
            max_backups = self.max_backups.value() if hasattr(self, 'max_backups') else 5

            if not os.path.exists(backup_folder):
                return

            # البحث عن ملفات النسخ الاحتياطية
            backup_files = []
            for filename in os.listdir(backup_folder):
                if filename.endswith('.db') or filename.endswith('.sqlite'):
                    file_path = os.path.join(backup_folder, filename)
                    if os.path.isfile(file_path):
                        stat = os.stat(file_path)
                        backup_files.append({
                            "path": file_path,
                            "timestamp": stat.st_mtime
                        })

            # ترتيب الملفات حسب التاريخ (الأقدم أولاً)
            backup_files.sort(key=lambda x: x["timestamp"])

            # حذف الملفات الزائدة
            while len(backup_files) > max_backups:
                old_file = backup_files.pop(0)
                try:
                    os.remove(old_file["path"])
                    print(f"تم حذف النسخة الاحتياطية القديمة: {old_file['path']}")
                except Exception as e:
                    print(f"خطأ في حذف النسخة الاحتياطية القديمة: {str(e)}")

        except Exception as e:
            print(f"خطأ في تنظيف النسخ الاحتياطية القديمة: {str(e)}")

    def show_backup_context_menu(self, position):
        """عرض قائمة السياق لجدول النسخ الاحتياطية"""
        # التحقق من وجود صف محدد
        item = self.backups_table.itemAt(position)
        if item is None:
            return

        # الحصول على الصف المحدد
        row = item.row()
        backup_name = self.backups_table.item(row, 0).text()

        # إنشاء قائمة السياق مع التحقق من الصلاحيات
        context_menu = QMenu(self)
        context_menu.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # التحقق من صلاحيات المستخدم
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()

        user_id = None
        current_username = None
        current_user = self._get_current_user_safely(main_window)
        if current_user:
            user_id = current_user.get('id')
            current_username = current_user.get('username')

        from controllers.user_controller import UserController
        has_restore_permission = user_id is None or current_username == 'admin' or UserController.check_permission(user_id, "استعادة النسخ الاحتياطي")
        has_backup_permission = user_id is None or current_username == 'admin' or UserController.check_permission(user_id, "إدارة النسخ الاحتياطي")

        # إضافة إجراء الاستعادة
        restore_action = context_menu.addAction("🔄  استعادة النسخة الاحتياطية")
        if has_restore_permission:
            restore_action.setToolTip(f"استعادة النسخة الاحتياطية: {backup_name}")
            restore_action.triggered.connect(lambda: self.restore_selected_backup(row))
        else:
            restore_action.setEnabled(False)
            restore_action.setText("🔄  استعادة النسخة الاحتياطية (لا يوجد صلاحية)")
            restore_action.setToolTip("ليس لديك صلاحية استعادة النسخ الاحتياطية")

        context_menu.addSeparator()

        delete_action = context_menu.addAction("🗑️  حذف النسخة الاحتياطية")
        if has_backup_permission:
            delete_action.setToolTip(f"حذف النسخة الاحتياطية: {backup_name}")
            delete_action.triggered.connect(lambda: self.delete_selected_backup(row))
        else:
            delete_action.setEnabled(False)
            delete_action.setText("🗑️  حذف النسخة الاحتياطية (لا يوجد صلاحية)")
            delete_action.setToolTip("ليس لديك صلاحية حذف النسخ الاحتياطية")

        context_menu.addSeparator()

        info_action = context_menu.addAction("ℹ️  معلومات النسخة الاحتياطية")
        info_action.setToolTip(f"عرض معلومات النسخة الاحتياطية: {backup_name}")
        info_action.triggered.connect(lambda: self.show_backup_info(row))

        # تطبيق الأنماط على قائمة السياق
        context_menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 6px;
                padding: 5px;
                font-size: 12px;
            }
            QMenu::item {
                padding: 8px 20px;
                border-radius: 4px;
                margin: 2px;
            }
            QMenu::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QMenu::separator {
                height: 1px;
                background-color: #eee;
                margin: 5px 10px;
            }
        """)

        # عرض قائمة السياق
        context_menu.exec_(self.backups_table.mapToGlobal(position))

    def restore_selected_backup(self, row):
        """استعادة النسخة الاحتياطية المحددة"""
        # التحقق من صلاحية استعادة النسخ الاحتياطي
        try:
            # الحصول على النافذة الرئيسية
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            user_id = None
            current_username = None
            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                current_username = main_window.current_user.get('username')

            from controllers.user_controller import UserController
            has_restore_permission = user_id is None or current_username == 'admin' or UserController.check_permission(user_id, "استعادة النسخ الاحتياطي")

            if not has_restore_permission:
                QMessageBox.warning(self, "غير مسموح", "ليس لديك صلاحية استعادة النسخ الاحتياطية")
                return
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التحقق من الصلاحيات: {str(e)}")
            return

        backup_name = self.backups_table.item(row, 0).text()
        backup_date = self.backups_table.item(row, 1).text()

        # الحصول على مسار مجلد النسخ الاحتياطية
        backup_folder = self.backup_folder_input.text() if hasattr(self, 'backup_folder_input') else "backups"
        backup_path = os.path.join(backup_folder, backup_name)

        # التأكيد قبل الاستعادة
        reply = QMessageBox.warning(
            self,
            "تأكيد الاستعادة",
            f"هل أنت متأكد من استعادة النسخة الاحتياطية:\n\n"
            f"📁 الملف: {backup_name}\n"
            f"📅 التاريخ: {backup_date}\n\n"
            f"⚠️ تحذير: ستتم استبدال البيانات الحالية بالكامل!",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # تنفيذ عملية الاستعادة الفعلية
            from controllers.database_controller import DatabaseController

            try:
                success, message = DatabaseController.restore_backup(backup_path)

                if success:
                    QMessageBox.information(
                        self,
                        "تمت الاستعادة",
                        f"تم استعادة النسخة الاحتياطية '{backup_name}' بنجاح!"
                    )

                    # تحديث قائمة النسخ الاحتياطية
                    self.refresh_backups_list()

                    # تحديث جميع الصفحات في التطبيق
                    self.refresh_all_application_pages()

                else:
                    QMessageBox.critical(
                        self,
                        "خطأ في الاستعادة",
                        f"فشل في استعادة النسخة الاحتياطية:\n\n{message}"
                    )

            except Exception as e:
                QMessageBox.critical(
                    self,
                    "خطأ في الاستعادة",
                    f"حدث خطأ أثناء استعادة النسخة الاحتياطية:\n\n{str(e)}"
                )

    def refresh_all_application_pages(self):
        """تحديث جميع الصفحات في التطبيق بعد استعادة قاعدة البيانات"""
        try:
            # الحصول على النافذة الرئيسية
            main_window = self.window()

            # استخدام دالة refresh_all_pages في النافذة الرئيسية
            if hasattr(main_window, 'refresh_all_pages') and callable(main_window.refresh_all_pages):
                main_window.refresh_all_pages()
            else:
                # طريقة بديلة إذا لم تكن الدالة متوفرة
                if hasattr(main_window, 'content_widget'):
                    for i in range(main_window.content_widget.count()):
                        widget = main_window.content_widget.widget(i)
                        if hasattr(widget, 'refresh_page') and callable(widget.refresh_page):
                            widget.refresh_page()
                            print(f"تم تحديث الصفحة: {widget.__class__.__name__}")

        except Exception as e:
            print(f"خطأ في تحديث الصفحات: {str(e)}")

            # محاولة بديلة في حالة فشل الطريقة الأولى
            try:
                main_window = self.window()
                if hasattr(main_window, 'content_widget'):
                    for i in range(main_window.content_widget.count()):
                        widget = main_window.content_widget.widget(i)
                        if hasattr(widget, 'refresh_page') and callable(widget.refresh_page):
                            widget.refresh_page()
            except Exception as fallback_error:
                print(f"فشل في الطريقة البديلة: {str(fallback_error)}")

                # عرض حوار إعادة التشغيل
                # عرض رسالة نجاح بسيطة
                QMessageBox.information(
                    self,
                    "تم الاستعادة بنجاح",
                    "تم استعادة قاعدة البيانات بنجاح!\n\nسيتم تحديث جميع البيانات تلقائياً.",
                    QMessageBox.Ok
                )
                
                # تحديث البيانات فوراً
                try:
                    # إعادة تحميل الإعدادات
                    self.load_settings()
                    
                    # تحديث النوافذ المفتوحة
                    main_window = self.parent()
                    while main_window and not hasattr(main_window, 'refresh_all_views'):
                        main_window = main_window.parent()
                    if main_window and hasattr(main_window, 'refresh_all_views'):
                        self._safely_call_method(main_window, 'refresh_all_views')
                        
                except Exception as e:
                    print(f"خطأ في تحديث البيانات بعد الاستعادة: {str(e)}")

    def delete_selected_backup(self, row):
        """حذف النسخة الاحتياطية المحددة"""
        backup_name = self.backups_table.item(row, 0).text()
        backup_date = self.backups_table.item(row, 1).text()

        # التأكيد قبل الحذف
        reply = QMessageBox.warning(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف النسخة الاحتياطية:\n\n"
            f"📁 الملف: {backup_name}\n"
            f"📅 التاريخ: {backup_date}\n\n"
            f"⚠️ تحذير: هذا الإجراء لا يمكن التراجع عنه!",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # حذف الصف من الجدول
            self.backups_table.removeRow(row)

            # هنا يمكن إضافة كود لحذف الملف الفعلي
            QMessageBox.information(
                self,
                "تم الحذف",
                f"تم حذف النسخة الاحتياطية '{backup_name}' بنجاح!"
            )

    def show_backup_info(self, row):
        """عرض معلومات النسخة الاحتياطية"""
        backup_name = self.backups_table.item(row, 0).text()
        backup_date = self.backups_table.item(row, 1).text()
        backup_size = self.backups_table.item(row, 2).text()

        # إنشاء نافذة معلومات
        info_dialog = QDialog(self)
        info_dialog.setWindowTitle("معلومات النسخة الاحتياطية")
        info_dialog.setFixedSize(400, 300)
        info_dialog.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        layout = QVBoxLayout(info_dialog)
        layout.setSpacing(15)
        layout.setContentsMargins(20, 20, 20, 20)

        # عنوان النافذة
        title_label = QLabel("📋 معلومات النسخة الاحتياطية")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(title_label)

        # معلومات النسخة الاحتياطية
        info_text = f"""
        <div style="font-size: 12px; line-height: 1.6;">
            <p><b>📁 اسم الملف:</b><br>{backup_name}</p>
            <p><b>📅 تاريخ الإنشاء:</b><br>{backup_date}</p>
            <p><b>📊 حجم الملف:</b><br>{backup_size}</p>
            <p><b>📍 المسار:</b><br>backups/{backup_name}</p>
            <p><b>🔧 نوع الملف:</b><br>قاعدة بيانات SQLite</p>
        </div>
        """

        info_label = QLabel(info_text)
        info_label.setWordWrap(True)
        info_label.setStyleSheet("""
            QLabel {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 6px;
                padding: 15px;
            }
        """)
        layout.addWidget(info_label)

        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setObjectName("secondary_button")
        close_btn.clicked.connect(info_dialog.accept)

        button_layout = QHBoxLayout()
        button_layout.addStretch()
        button_layout.addWidget(close_btn)
        layout.addLayout(button_layout)

        # تطبيق الأنماط
        info_dialog.setStyleSheet(AppStyles.get_all_view_styles())

        # عرض النافذة
        info_dialog.exec_()



    def load_settings(self):
        """تحميل الإعدادات المحفوظة باستخدام SettingsManager"""
        try:
            # تعيين متغير التحميل لمنع إرسال إشعارات أثناء التحميل
            self.loading_settings = True

            # تحميل إعدادات الشركة
            if hasattr(self, 'company_name'):
                company_name = self.settings_manager.get_setting("company", "name", DEFAULT_SETTINGS["company_name"])
                self.company_name.setText(company_name)
                
            if hasattr(self, 'company_phone'):
                company_phone = self.settings_manager.get_setting("company", "phone", DEFAULT_SETTINGS["company_phone"])
                self.company_phone.setText(company_phone)
                
            if hasattr(self, 'company_address'):
                company_address = self.settings_manager.get_setting("company", "address", DEFAULT_SETTINGS["company_address"])
                self.company_address.setText(company_address)

            # تحميل إعدادات الفواتير
            if hasattr(self, 'invoice_notes'):
                invoice_notes = self.settings_manager.get_setting("invoice", "notes", DEFAULT_SETTINGS["invoice_notes"])
                self.invoice_notes.setText(invoice_notes)

            # تحميل إعدادات الطباعة التلقائية للفاتورة
            if hasattr(self, 'auto_print_invoice_after_sale'):
                auto_print = self.settings_manager.get_setting("invoice", "auto_print_invoice", DEFAULT_SETTINGS["auto_print_invoice_after_sale"])
                if isinstance(auto_print, str):
                    auto_print = auto_print.lower() == 'true'
                self.auto_print_invoice_after_sale.setChecked(bool(auto_print))

            # تحميل إعدادات الطابعة
            if hasattr(self, 'default_printer_combo'):
                default_printer = self.settings_manager.get_setting("printer", "default_printer", DEFAULT_SETTINGS["default_printer"])
                index = self.default_printer_combo.findText(default_printer)
                if index >= 0:
                    self.default_printer_combo.setCurrentIndex(index)
                else:
                    self.default_printer_combo.setCurrentIndex(0)

            # تحميل إعدادات حجم ورق الفاتورة
            if hasattr(self, 'paper_size_58mm') and hasattr(self, 'paper_size_80mm') and hasattr(self, 'paper_size_A4'):
                paper_width = self.settings_manager.get_setting("printer", "paper_width", 58)
                if isinstance(paper_width, str):
                    paper_width = int(paper_width)
                    
                if paper_width == 58:
                    self.paper_size_58mm.setChecked(True)
                elif paper_width == 210:  # A4 width
                    self.paper_size_A4.setChecked(True)
                else:  # 80mm
                    self.paper_size_80mm.setChecked(True)

            # تحميل إعدادات قاعدة البيانات (باستخدام QSettings للتوافق)
            if hasattr(self, 'db_path_input'):
                db_path = self.settings.value("db_path", "database.db")
                self.db_path_input.setText(db_path)

            # تحميل خيار إظهار قسم المفضلة (باستخدام QSettings للتوافق)
            if hasattr(self, 'show_favorites_section_checkbox'):
                show_favorites = self.settings.value("show_favorites_section_in_sales", True)
                if isinstance(show_favorites, str):
                    show_favorites = show_favorites.lower() == 'true'
                self.show_favorites_section_checkbox.setChecked(bool(show_favorites))

            print("تم تحميل الإعدادات بنجاح باستخدام SettingsManager")

        except Exception as e:
            print(f"خطأ في تحميل الإعدادات: {str(e)}")
            # في حالة الخطأ، استخدام القيم الافتراضية
            self._load_default_settings()
        finally:
            # إعادة تعيين متغير التحميل
            self.loading_settings = False

    def _load_default_settings(self):
        """تحميل الإعدادات الافتراضية في حالة الخطأ"""
        try:
            if hasattr(self, 'company_name'):
                self.company_name.setText(DEFAULT_SETTINGS["company_name"])
            if hasattr(self, 'company_phone'):
                self.company_phone.setText(DEFAULT_SETTINGS["company_phone"])
            if hasattr(self, 'company_address'):
                self.company_address.setText(DEFAULT_SETTINGS["company_address"])
            if hasattr(self, 'invoice_notes'):
                self.invoice_notes.setText(DEFAULT_SETTINGS["invoice_notes"])
            if hasattr(self, 'auto_print_invoice_after_sale'):
                self.auto_print_invoice_after_sale.setChecked(DEFAULT_SETTINGS["auto_print_invoice_after_sale"])
                
            print("تم تحميل الإعدادات الافتراضية")
            
        except Exception as e:
            print(f"خطأ في تحميل الإعدادات الافتراضية: {str(e)}")

    def save_settings(self):
        """حفظ إعدادات التطبيق باستخدام SettingsManager"""
        try:
            # جمع إعدادات الشركة
            company_settings = {
                "name": self.company_name.text(),
                "phone": self.company_phone.text(),
                "address": self.company_address.text()
            }

            # جمع إعدادات الفواتير
            invoice_settings = {
                "notes": self.invoice_notes.text(),
                "auto_print_invoice": self.auto_print_invoice_after_sale.isChecked()
            }

            # جمع إعدادات الطباعة
            printer_settings = {
                "default_printer": self.default_printer_combo.currentText()
            }

            # تحديد حجم ورق الفاتورة
            if self.paper_size_58mm.isChecked():
                paper_width = 58
            elif self.paper_size_A4.isChecked():
                paper_width = 210  # A4 width in mm
            else:  # 80mm
                paper_width = 80
            
            printer_settings["paper_width"] = paper_width

            # جمع إعدادات قاعدة البيانات SQLite
            db_settings = {
                "db_type": "sqlite",
                "db_path": self.db_path_input.text()
            }

            # التحقق من صحة مسار قاعدة البيانات SQLite
            if not self.db_path_input.text():
                QMessageBox.warning(
                    self,
                    "خطأ في الإعدادات",
                    "يرجى تحديد مسار ملف قاعدة البيانات SQLite.",
                    QMessageBox.Ok
                )
                return

            # حفظ الإعدادات باستخدام SettingsManager
            success_count = 0
            total_categories = 3

            # حفظ إعدادات الشركة
            if self.settings_manager.apply_settings_category("company", company_settings):
                success_count += 1
                print("تم حفظ إعدادات الشركة بنجاح")
            else:
                print("فشل في حفظ إعدادات الشركة")

            # حفظ إعدادات الفواتير
            if self.settings_manager.apply_settings_category("invoice", invoice_settings):
                success_count += 1
                print("تم حفظ إعدادات الفواتير بنجاح")
            else:
                print("فشل في حفظ إعدادات الفواتير")

            # حفظ إعدادات الطباعة
            if self.settings_manager.apply_settings_category("printer", printer_settings):
                success_count += 1
                print("تم حفظ إعدادات الطباعة بنجاح")
            else:
                print("فشل في حفظ إعدادات الطباعة")

            # حفظ إعدادات إضافية باستخدام QSettings القديم (للتوافق)
            self.settings.setValue("show_favorites_section_in_sales", self.show_favorites_section_checkbox.isChecked())

            # حفظ إعدادات قاعدة البيانات باستخدام وحدة التحكم
            db_success, db_message = DatabaseController.save_settings(db_settings)

            if db_success and success_count == total_categories:
                # إعادة تحميل الإعدادات في واجهة المستخدم
                self.load_settings()

                # تحديث صفحة المبيعات في MainWindow إذا كانت مفتوحة
                main_window = self.parent()
                while main_window and not hasattr(main_window, 'refresh_sales_view'):
                    main_window = main_window.parent()
                if main_window and hasattr(main_window, 'refresh_sales_view'):
                    self._safely_call_method(main_window, 'refresh_sales_view')

                # عرض رسالة نجاح مفصلة
                # تحديد حجم الورق المحفوظ للعرض في الرسالة
                paper_size_text = ""
                if self.paper_size_58mm.isChecked():
                    paper_size_text = "58 مم (حراري صغير)"
                elif self.paper_size_80mm.isChecked():
                    paper_size_text = "80 مم (حراري عادي)"
                elif self.paper_size_A4.isChecked():
                    paper_size_text = "A4 (ورق عادي)"
                
                success_message = f"""✅ تم حفظ جميع الإعدادات بنجاح!

📋 الإعدادات المحفوظة:
• معلومات الشركة
• إعدادات الفواتير
• إعدادات الطباعة (حجم الورق: {paper_size_text})
• إعدادات قاعدة البيانات

💡 سيتم تطبيق الإعدادات الجديدة فوراً على جميع العمليات."""

                QMessageBox.information(
                    self,
                    "تم الحفظ بنجاح",
                    success_message,
                    QMessageBox.Ok
                )
            else:
                # عرض رسالة خطأ مع التفاصيل
                error_details = []
                if success_count < total_categories:
                    error_details.append(f"فشل في حفظ {total_categories - success_count} فئات إعدادات")
                if not db_success:
                    error_details.append(f"خطأ في قاعدة البيانات: {db_message}")

                QMessageBox.warning(
                    self,
                    "تحذير في الحفظ",
                    f"تم حفظ {success_count}/{total_categories} فئات إعدادات بنجاح.\n\nالمشاكل:\n" + "\n".join(error_details),
                    QMessageBox.Ok
                )

        except Exception as e:
            print(f"خطأ في حفظ الإعدادات: {str(e)}")
            QMessageBox.critical(
                self,
                "خطأ في الحفظ",
                f"حدث خطأ أثناء حفظ الإعدادات:\n{str(e)}",
                QMessageBox.Ok
            )

    # تم إزالة دوال إعادة التشغيل - سيتم تطبيق الإعدادات فوراً

    def apply_styles(self):
        """تطبيق الأنماط على العناصر"""
        # استخدام التنسيقات من ملف الستايلات بدلاً من التعريف المحلي
        self.setStyleSheet(AppStyles.get_all_view_styles() + """
            QGroupBox {
                font-weight: bold;
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                margin-top: 12px;
                padding-top: 10px;
            }

            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px;
            }

            QCheckBox {
                spacing: 8px;
            }

            QCheckBox::indicator {
                width: 18px;
                height: 18px;
            }

            QPushButton#toggle_button {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 3px;
                color: #495057;
                font-weight: bold;
                font-size: 12px;
            }

            QPushButton#toggle_button:hover {
                background-color: #e9ecef;
                border-color: #adb5bd;
            }

            QPushButton#toggle_button:pressed {
                background-color: #dee2e6;
                border-color: #6c757d;
            }
        """)



    def load_available_printers(self):
        """تحميل قائمة الطابعات المتصلة بالكمبيوتر"""
        try:
            printers = self.get_system_printers()

            # إضافة الطابعة الافتراضية في البداية
            self.default_printer_combo.addItem("الطابعة الافتراضية")

            # إضافة الطابعات المتصلة
            if printers:
                for printer in printers:
                    self.default_printer_combo.addItem(printer)
            else:
                # إضافة خيارات افتراضية إذا لم يتم العثور على طابعات
                self.default_printer_combo.addItem("لا توجد طابعات متصلة")

        except Exception as e:
            print(f"خطأ في تحميل قائمة الطابعات: {str(e)}")
            # إضافة خيارات افتراضية في حالة الخطأ
            self.default_printer_combo.addItems([
                "الطابعة الافتراضية",
                "خطأ في تحميل الطابعات"
            ])

    def get_system_printers(self):
        """الحصول على قائمة الطابعات المتصلة بالنظام"""
        printers = []

        # الطريقة الأولى: استخدام win32print (الأكثر دقة)
        try:
            import win32print  # type: ignore

            # الحصول على الطابعات المحلية والمتصلة عبر الشبكة
            flags = win32print.PRINTER_ENUM_LOCAL | win32print.PRINTER_ENUM_CONNECTIONS
            printer_list = win32print.EnumPrinters(flags)

            for printer in printer_list:
                printer_name = printer[2]  # اسم الطابعة
                if printer_name and printer_name.strip():
                    printers.append(printer_name)

            print(f"تم العثور على {len(printers)} طابعة باستخدام win32print")

        except ImportError:
            print("win32print غير متوفر، جاري المحاولة بطريقة أخرى...")

        except Exception as e:
            print(f"خطأ في win32print: {str(e)}")

        # الطريقة الثانية: استخدام wmic إذا فشلت الأولى أو لم تجد طابعات
        if not printers:
            try:
                import subprocess
                # إعداد معاملات subprocess لإخفاء نافذة cmd على Windows
                kwargs = {
                    'capture_output': True,
                    'text': True,
                    'timeout': 15
                }
                if os.name == 'nt':  # Windows only
                    kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW

                result = subprocess.run(['wmic', 'printer', 'get', 'name'], **kwargs)

                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:  # تخطي العنوان
                        printer_name = line.strip()
                        if printer_name and printer_name != "Name" and printer_name:
                            printers.append(printer_name)

                    print(f"تم العثور على {len(printers)} طابعة باستخدام wmic")

            except Exception as e:
                print(f"فشل في الحصول على الطابعات باستخدام wmic: {str(e)}")

        # الطريقة الثالثة: استخدام PowerShell كبديل أخير
        if not printers:
            try:
                import subprocess
                ps_command = "Get-WmiObject -Class Win32_Printer | Select-Object Name | Format-Table -HideTableHeaders"

                # إعداد معاملات subprocess لإخفاء نافذة cmd على Windows
                kwargs = {
                    'capture_output': True,
                    'text': True,
                    'timeout': 15
                }
                if os.name == 'nt':  # Windows only
                    kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW

                result = subprocess.run(['powershell', '-Command', ps_command], **kwargs)

                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        printer_name = line.strip()
                        if printer_name and len(printer_name) > 0:
                            printers.append(printer_name)

                    print(f"تم العثور على {len(printers)} طابعة باستخدام PowerShell")

            except Exception as e:
                print(f"فشل في الحصول على الطابعات باستخدام PowerShell: {str(e)}")

        # تنظيف القائمة
        if printers:
            # إزالة التكرارات والعناصر الفارغة
            printers = [p.strip() for p in printers if p and p.strip()]
            printers = list(set(printers))
            printers.sort()

            # إزالة الطابعات الوهمية أو غير المفيدة
            filtered_printers = []
            for printer in printers:
                # تجاهل الطابعات الافتراضية أو الوهمية
                if not any(skip in printer.lower() for skip in ['fax', 'xps', 'onenote', 'pdf']):
                    filtered_printers.append(printer)

            printers = filtered_printers

        print(f"النتيجة النهائية: {len(printers)} طابعة متاحة")
        return printers







    def refresh_page(self):
        """تحديث بيانات الصفحة عند الانتقال إليها أو عند فتح التطبيق"""
        # تحديث قائمة النسخ الاحتياطية
        self.refresh_backups_list()

        # إعادة تحميل الإعدادات
        self.load_settings()

        print("تم تحديث صفحة الإعدادات")

    def add_user(self):
        """إضافة مستخدم جديد"""
        # التحقق من صلاحية إضافة مستخدم
        try:
            # الحصول على النافذة الرئيسية
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            user_id = None
            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')

            from controllers.user_controller import UserController
            has_add_permission = user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "إضافة مستخدم")

            if not has_add_permission:
                QMessageBox.warning(self, "غير مسموح", "ليس لديك صلاحية إضافة مستخدمين جدد")
                return
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التحقق من الصلاحيات: {str(e)}")
            return

        try:
            dialog = QDialog(self)
            dialog.setWindowTitle("إضافة مستخدم جديد")
            dialog.setMinimumWidth(400)

            # تخطيط النموذج
            form_layout = QFormLayout(dialog)

            # حقول المستخدم
            username = QLineEdit()
            username.setObjectName("search_input")
            username.setPlaceholderText("اسم المستخدم")

            full_name = QLineEdit()
            full_name.setObjectName("search_input")
            full_name.setPlaceholderText("الاسم الكامل")

            password = QLineEdit()
            password.setObjectName("search_input")
            password.setPlaceholderText("كلمة المرور")
            password.setEchoMode(QLineEdit.Password)

            confirm_password = QLineEdit()
            confirm_password.setObjectName("search_input")
            confirm_password.setPlaceholderText("تأكيد كلمة المرور")
            confirm_password.setEchoMode(QLineEdit.Password)

            role_combo = RTLComboBox()
            role_combo.addItems(["مدير", "كاشير", "مدير مخزون"])
            role_combo.setObjectName("search_input")

            # إضافة الحقول إلى التخطيط
            form_layout.addRow("اسم المستخدم:", username)
            form_layout.addRow("الاسم الكامل:", full_name)
            form_layout.addRow("كلمة المرور:", password)
            form_layout.addRow("تأكيد كلمة المرور:", confirm_password)
            form_layout.addRow("الصلاحية:", role_combo)

            # أزرار الإجراءات
            button_box = QHBoxLayout()
            save_btn = QPushButton("إضافة المستخدم")
            save_btn.setObjectName("action_button")

            cancel_btn = QPushButton("إلغاء")
            cancel_btn.setObjectName("secondary_button")

            button_box.addWidget(save_btn)
            button_box.addWidget(cancel_btn)

            form_layout.addRow("", button_box)

            # ربط الأزرار بالوظائف
            cancel_btn.clicked.connect(dialog.reject)

            # وظيفة إضافة المستخدم
            def create_user():
                # التحقق من الإدخال
                if not username.text() or not full_name.text() or not password.text() or not confirm_password.text():
                    QMessageBox.warning(dialog, "بيانات غير مكتملة", "يرجى ملء جميع الحقول")
                    return

                # التحقق من تطابق كلمة المرور
                if password.text() != confirm_password.text():
                    QMessageBox.warning(dialog, "كلمات المرور غير متطابقة", "كلمة المرور وتأكيدها غير متطابقين")
                    return

                # إضافة المستخدم إلى قاعدة البيانات
                success, message = UserController.create_user(
                    username.text(),
                    full_name.text(),
                    password.text(),
                    role_combo.currentText()
                )

                if success:
                    # استخدام الرسالة النصية المعادة مباشرة من نموذج المستخدم
                    QMessageBox.information(dialog, "تمت الإضافة", message)
                    self.load_users_data()  # إعادة تحميل بيانات المستخدمين
                    dialog.accept()
                else:
                    QMessageBox.warning(dialog, "خطأ", message)

            save_btn.clicked.connect(create_user)

            # عرض الحوار
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة مستخدم جديد: {str(e)}")

    def edit_user(self):
        """تعديل المستخدم المحدد"""
        # التحقق من صلاحية تعديل مستخدم
        try:
            # الحصول على النافذة الرئيسية
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            user_id = None
            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')

            from controllers.user_controller import UserController
            has_edit_permission = user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "تعديل مستخدم")

            if not has_edit_permission:
                QMessageBox.warning(self, "غير مسموح", "ليس لديك صلاحية تعديل المستخدمين")
                return
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التحقق من الصلاحيات: {str(e)}")
            return

        try:
            # التحقق من اختيار صف في الجدول
            selected_rows = self.users_table.selectedItems()
            if not selected_rows:
                QMessageBox.warning(self, "تحذير", "الرجاء اختيار مستخدم لتعديله")
                return

            # الحصول على معرف المستخدم المحدد
            selected_row = self.users_table.currentRow()
            user_id = self.users_table.item(selected_row, 0).data(Qt.ItemDataRole.UserRole)

            # الحصول على معلومات المستخدم
            user_info = UserController.get_user_info(user_id=user_id)
            if not user_info:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على معلومات المستخدم")
                return

            # إنشاء نافذة الحوار لتعديل المستخدم
            dialog = QDialog(self)
            dialog.setWindowTitle("تعديل المستخدم")
            dialog.setMinimumWidth(400)

            # تخطيط النموذج
            form_layout = QFormLayout(dialog)

            # حقول المستخدم
            username = QLineEdit()
            username.setObjectName("search_input")
            username.setText(user_info['username'])
            if user_info['username'] == 'admin':  # منع تغيير اسم المستخدم للمدير الرئيسي
                username.setEnabled(False)

            full_name = QLineEdit()
            full_name.setObjectName("search_input")
            full_name.setText(user_info['full_name'])

            role_combo = RTLComboBox()
            role_combo.addItems(["مدير", "كاشير", "مدير مخزون"])
            role_combo.setCurrentText(user_info['role'])
            role_combo.setObjectName("search_input")
            if user_info['username'] == 'admin':  # منع تغيير صلاحية المدير الرئيسي
                role_combo.setEnabled(False)

            active_checkbox = QCheckBox("مستخدم نشط")
            active_checkbox.setChecked(user_info['is_active'])

            # إضافة الحقول إلى التخطيط
            form_layout.addRow("اسم المستخدم:", username)
            form_layout.addRow("الاسم الكامل:", full_name)
            form_layout.addRow("الصلاحية:", role_combo)
            form_layout.addRow("", active_checkbox)

            # أزرار الإجراءات
            button_box = QHBoxLayout()
            save_btn = QPushButton("حفظ التغييرات")
            save_btn.setObjectName("action_button")

            reset_password_btn = QPushButton("إعادة تعيين كلمة المرور")
            reset_password_btn.setObjectName("secondary_button")

            cancel_btn = QPushButton("إلغاء")
            cancel_btn.setObjectName("secondary_button")

            button_box.addWidget(save_btn)
            button_box.addWidget(reset_password_btn)
            button_box.addWidget(cancel_btn)

            form_layout.addRow("", button_box)

            # ربط الأزرار بالوظائف
            cancel_btn.clicked.connect(dialog.reject)

            # وظيفة تحديث المستخدم
            def update_user():
                # التحقق من الإدخال
                if not username.text() or not full_name.text():
                    QMessageBox.warning(dialog, "بيانات غير مكتملة", "يرجى ملء جميع الحقول")
                    return

                # تحديث معلومات المستخدم
                success, message = UserController.update_user(
                    user_id,
                    username.text(),
                    full_name.text(),
                    role_combo.currentText(),
                    active_checkbox.isChecked()
                )

                if success:
                    QMessageBox.information(dialog, "تم التحديث", message)
                    self.load_users_data()  # إعادة تحميل بيانات المستخدمين
                    dialog.accept()
                else:
                    QMessageBox.warning(dialog, "خطأ", message)

            save_btn.clicked.connect(update_user)

            # وظيفة إعادة تعيين كلمة المرور
            def reset_password():
                password_dialog = QDialog(dialog)
                password_dialog.setWindowTitle("إعادة تعيين كلمة المرور")
                password_dialog.setMinimumWidth(400)

                password_layout = QFormLayout(password_dialog)

                # حقول كلمة المرور
                new_password = QLineEdit()
                new_password.setObjectName("search_input")
                new_password.setPlaceholderText("كلمة المرور الجديدة")
                new_password.setEchoMode(QLineEdit.Password)

                confirm_new_password = QLineEdit()
                confirm_new_password.setObjectName("search_input")
                confirm_new_password.setPlaceholderText("تأكيد كلمة المرور الجديدة")
                confirm_new_password.setEchoMode(QLineEdit.Password)

                # أزرار الإجراءات
                password_buttons = QHBoxLayout()
                reset_btn = QPushButton("تغيير كلمة المرور")
                reset_btn.setObjectName("action_button")

                cancel_reset_btn = QPushButton("إلغاء")
                cancel_reset_btn.setObjectName("secondary_button")

                password_buttons.addWidget(reset_btn)
                password_buttons.addWidget(cancel_reset_btn)

                # إضافة الحقول والأزرار إلى التخطيط
                password_layout.addRow("كلمة المرور الجديدة:", new_password)
                password_layout.addRow("تأكيد كلمة المرور:", confirm_new_password)
                password_layout.addRow("", password_buttons)

                # ربط الأزرار بالوظائف
                cancel_reset_btn.clicked.connect(password_dialog.reject)

                # وظيفة تغيير كلمة المرور
                def do_reset_password():
                    # التحقق من الإدخال
                    if not new_password.text() or not confirm_new_password.text():
                        QMessageBox.warning(password_dialog, "بيانات غير مكتملة", "يرجى ملء جميع الحقول")
                        return

                    # التحقق من تطابق كلمة المرور
                    if new_password.text() != confirm_new_password.text():
                        QMessageBox.warning(password_dialog, "كلمات المرور غير متطابقة", "كلمة المرور وتأكيدها غير متطابقين")
                        return

                    # تغيير كلمة المرور
                    success, message = UserController.reset_password(user_id, new_password.text())

                    if success:
                        QMessageBox.information(password_dialog, "تم تغيير كلمة المرور", message)
                        password_dialog.accept()
                    else:
                        QMessageBox.warning(password_dialog, "خطأ", message)

                reset_btn.clicked.connect(do_reset_password)

                # عرض الحوار
                password_dialog.exec_()

            reset_password_btn.clicked.connect(reset_password)

            # عرض الحوار
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل المستخدم: {str(e)}")

    def delete_user(self):
        """حذف المستخدم المحدد"""
        # التحقق من صلاحية حذف مستخدم
        try:
            # الحصول على النافذة الرئيسية
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            user_id = None
            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')

            from controllers.user_controller import UserController
            has_delete_permission = user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "حذف مستخدم")

            if not has_delete_permission:
                QMessageBox.warning(self, "غير مسموح", "ليس لديك صلاحية حذف المستخدمين")
                return
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التحقق من الصلاحيات: {str(e)}")
            return

        try:
            # التحقق من اختيار صف في الجدول
            selected_rows = self.users_table.selectedItems()
            if not selected_rows:
                QMessageBox.warning(self, "تحذير", "الرجاء اختيار مستخدم لحذفه")
                return

            # الحصول على معلومات المستخدم المحدد
            selected_row = self.users_table.currentRow()
            user_id = self.users_table.item(selected_row, 0).data(Qt.ItemDataRole.UserRole)
            username = self.users_table.item(selected_row, 0).text()

            # منع حذف المستخدم admin
            if username == 'admin':
                QMessageBox.warning(self, "تحذير", "لا يمكن حذف المستخدم الرئيسي (admin)")
                return

            # رسالة تأكيد الحذف
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف المستخدم '{username}'؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # حذف المستخدم
                success, message = UserController.delete_user(user_id)

                if success:
                    QMessageBox.information(self, "تم الحذف", message)
                    self.load_users_data()  # إعادة تحميل بيانات المستخدمين
                else:
                    QMessageBox.warning(self, "خطأ", message)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف المستخدم: {str(e)}")

    def show_users_context_menu(self, position):
        """عرض قائمة السياق لجدول المستخدمين"""
        try:
            # التحقق من اختيار صف في الجدول
            selected_row = self.users_table.currentRow()
            if selected_row < 0:
                return

            # الحصول على معلومات المستخدم المحدد
            user_id = self.users_table.item(selected_row, 0).data(Qt.ItemDataRole.UserRole)
            username = self.users_table.item(selected_row, 0).text()

            # إنشاء قائمة السياق
            context_menu = QMenu(self)

            # إضافة عنوان القائمة
            title_action = QAction(f"المستخدم: {username}", self)
            title_action.setEnabled(False)
            title_font = title_action.font()
            title_font.setBold(True)
            title_action.setFont(title_font)
            context_menu.addAction(title_action)

            # إضافة فاصل
            context_menu.addSeparator()

            # إضافة خيارات القائمة مع التحقق من الصلاحيات
            
            # الحصول على صلاحيات المستخدم الحالي
            from controllers.user_controller import UserController
            current_user_id = None
            current_username = None
            
            # الحصول على النافذة الرئيسية
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()
                
            if main_window and main_window.current_user:
                current_user_id = main_window.current_user.get('id')
                current_username = main_window.current_user.get('username')
                
            # التحقق من الصلاحيات
            has_edit_permission = current_user_id is None or current_username == 'admin' or UserController.check_permission(current_user_id, "تعديل مستخدم")
            has_permissions_permission = current_user_id is None or current_username == 'admin' or UserController.check_permission(current_user_id, "إدارة صلاحيات المستخدمين")
            has_delete_permission = current_user_id is None or current_username == 'admin' or UserController.check_permission(current_user_id, "حذف مستخدم")

            # خيار تعديل المستخدم
            edit_action = QAction("✏️ تعديل المستخدم", self)
            if has_edit_permission:
                edit_action.triggered.connect(lambda: self.edit_user())
            else:
                edit_action.setEnabled(False)
                edit_action.setText("✏️ تعديل المستخدم (لا يوجد صلاحية)")
            context_menu.addAction(edit_action)

            # التحقق من أن المستخدم الحالي لديه صلاحية إدارة الصلاحيات
            if has_permissions_permission and username != 'admin':
                permissions_action = QAction("🔐 إدارة صلاحيات المستخدم", self)
                permissions_action.triggered.connect(lambda: self.manage_user_permissions(user_id, username))
                context_menu.addAction(permissions_action)

            # إضافة خيار حذف المستخدم (إلا إذا كان المستخدم هو admin)
            if username != 'admin':
                delete_action = QAction("❌ حذف المستخدم", self)
                if has_delete_permission:
                    delete_action.triggered.connect(lambda: self.delete_user())
                else:
                    delete_action.setEnabled(False)
                    delete_action.setText("❌ حذف المستخدم (لا يوجد صلاحية)")
                context_menu.addAction(delete_action)

            # عرض القائمة في الموضع المحدد
            context_menu.exec_(self.users_table.mapToGlobal(position))

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء عرض قائمة السياق: {str(e)}")

    def manage_user_permissions(self, user_id, username):
        """إدارة صلاحيات المستخدم المحدد"""
        try:
            # التحقق من صلاحية إدارة صلاحيات المستخدمين
            try:
                # الحصول على النافذة الرئيسية
                main_window = None
                parent = self.parent()
                while parent:
                    if hasattr(parent, 'current_user'):
                        main_window = parent
                        break
                    parent = parent.parent()

                current_user_id = None
                current_username = None
                if main_window and main_window.current_user:
                    current_user_id = main_window.current_user.get('id')
                    current_username = main_window.current_user.get('username')

                from controllers.user_controller import UserController
                has_permissions_permission = current_user_id is None or current_username == 'admin' or UserController.check_permission(current_user_id, "إدارة صلاحيات المستخدمين")

                if not has_permissions_permission:
                    QMessageBox.warning(self, "غير مسموح", "ليس لديك صلاحية إدارة صلاحيات المستخدمين")
                    return
            except Exception as e:
                print(f"خطأ في التحقق من المستخدم الحالي: {str(e)}")
                QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء التحقق من صلاحيات المستخدم")
                return

            # التحقق من أن المستخدم المراد تعديل صلاحياته ليس admin
            if username == 'admin':
                QMessageBox.warning(self, "غير مسموح", "لا يمكن تعديل صلاحيات المستخدم الرئيسي (admin)")
                return

            # الحصول على معلومات المستخدم
            user_info = UserController.get_user_info(user_id=user_id)
            if not user_info:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على معلومات المستخدم")
                return

            # الحصول على صلاحيات المستخدم الحالية
            user_permissions = UserController.get_user_permissions(user_id)
            print(f"تم استرجاع صلاحيات المستخدم: {user_id} - عدد الصلاحيات: {len(user_permissions) if user_permissions else 0}")

            # إنشاء نافذة حوار لإدارة الصلاحيات
            dialog = QDialog(self)
            dialog.setWindowTitle(f"إدارة صلاحيات المستخدم: {username}")
            dialog.setMinimumWidth(500)
            dialog.setMinimumHeight(600)

            # تخطيط النافذة
            layout = QVBoxLayout(dialog)

            # عنوان
            title_label = QLabel(f"تحديد صلاحيات المستخدم: {username}")
            title_label.setObjectName("page_title")
            title_label.setFont(QFont("Arial", 14, QFont.Bold))
            layout.addWidget(title_label)

            # معلومات المستخدم
            info_layout = QHBoxLayout()
            role_label = QLabel(f"الصلاحية: {user_info['role']}")
            role_label.setStyleSheet("color: #3b82f6; font-weight: bold;")
            info_layout.addWidget(role_label)

            status_label = QLabel(f"الحالة: {user_info['status']}")
            status_label.setStyleSheet(f"color: {'#27ae60' if user_info['is_active'] else '#e74c3c'}; font-weight: bold;")
            info_layout.addWidget(status_label)

            layout.addLayout(info_layout)

            # إضافة فاصل
            separator = QFrame()
            separator.setFrameShape(QFrame.HLine)
            separator.setFrameShadow(QFrame.Sunken)
            separator.setObjectName("content_separator")
            layout.addWidget(separator)

            # شرح مختصر
            info_label = QLabel("حدد الصلاحيات التي تريد منحها لهذا المستخدم:")
            info_label.setObjectName("hint_label")
            info_label.setWordWrap(True)
            layout.addWidget(info_label)

            # إنشاء شجرة الصلاحيات
            permissions_tree = QTreeWidget()
            permissions_tree.setHeaderLabels(["الصلاحية", "الحالة"])
            permissions_tree.setAlternatingRowColors(True)
            permissions_tree.setRootIsDecorated(True)
            permissions_tree.setColumnWidth(0, 350)

            # الصلاحيات المتاحة - مجموعة بحسب الوظائف
            available_permissions = {
                "المبيعات": [
                    "عرض المبيعات",
                    "إضافة عملية بيع",
                    "تعديل عملية بيع",
                    "حذف عملية بيع",
                    "تعديل سعر المنتج في الفاتورة",
                    "تعديل كمية المنتج في الفاتورة",
                    "طباعة فاتورة مبيعات"
                ],
                "المنتجات": [
                    "عرض المنتجات",
                    "إضافة منتج",
                    "تعديل منتج",
                    "حذف منتج",
                    "إدارة فئات المنتجات",
                    "تعديل أسعار المنتجات"
                ],
                "المخزون": [
                    "عرض المخزون",
                    "إضافة للمخزون",
                    "تعديل المخزون"
                ],
                "الفواتير": [
                    "عرض الفواتير",
                    "إلغاء فاتورة",
                    "حذف فاتورة",
                    "طباعة فاتورة",
                    "تعديل فاتورة"
                ],
                "التقارير": [
                    "عرض تقارير المبيعات",
                    "عرض تقارير المخزون",
                    "عرض تقارير الأرباح",
                    "عرض تقارير العملاء",
                    "عرض تقرير المنتجات الأكثر مبيعاً"
                ],
                "العملاء": [
                    "عرض العملاء",
                    "إضافة عميل",
                    "تعديل عميل",
                    "حذف عميل",
                    "إدارة ديون العملاء",
                    "عرض تفاصيل العميل"
                ],
                "الموردين": [
                    "عرض الموردين",
                    "إضافة مورد",
                    "تعديل مورد",
                    "حذف مورد",
                    "إدارة مدفوعات الموردين",
                    "عرض تفاصيل المورد"
                ],
                "المصروفات": [
                    "عرض المصروفات",
                    "إضافة مصروف",
                    "تعديل مصروف",
                    "حذف مصروف"
                ],
                "المستخدمين": [
                    "عرض المستخدمين",
                    "إضافة مستخدم",
                    "تعديل مستخدم",
                    "حذف مستخدم",
                    "إدارة صلاحيات المستخدمين"
                ],
                "الإعدادات": [
                    "عرض الإعدادات",
                    "تعديل إعدادات النظام",
                    "إدارة النسخ الاحتياطي",
                    "استعادة النسخ الاحتياطي"
                ]
            }

            # إضافة الصلاحيات إلى الشجرة
            category_items = {}

            for category, permissions in available_permissions.items():
                # إنشاء عنصر الفئة
                category_item = QTreeWidgetItem(permissions_tree, [category])
                # تعيين العلم لجعل العنصر قابل للتحديد فقط (بدون Qt.ItemIsTristate)
                category_item.setFlags(category_item.flags() | Qt.ItemFlag.ItemIsUserCheckable)
                category_item.setCheckState(0, Qt.CheckState.Unchecked)
                category_items[category] = category_item

                # إضافة الصلاحيات الفرعية
                all_checked = True  # للتحقق مما إذا كانت جميع الصلاحيات الفرعية محددة

                for permission in permissions:
                    child = QTreeWidgetItem(category_item, [permission])
                    child.setFlags(child.flags() | Qt.ItemFlag.ItemIsUserCheckable)

                    # تحديد حالة الصلاحية بناءً على صلاحيات المستخدم
                    is_checked = permission in user_permissions and user_permissions[permission]
                    child.setCheckState(0, Qt.CheckState.Checked if is_checked else Qt.CheckState.Unchecked)

                    # تحديث حالة التحقق من جميع الصلاحيات الفرعية
                    if not is_checked:
                        all_checked = False

                # تعيين حالة عنصر الفئة بناءً على حالة جميع الصلاحيات الفرعية
                if all_checked and len(permissions) > 0:  # تأكد من وجود صلاحيات فرعية
                    category_item.setCheckState(0, Qt.CheckState.Checked)

            # تحديث حالة عناصر الفئات بناءً على حالة العناصر الفرعية
            def update_parent_item(item):
                if item.childCount() > 0:
                    all_checked = True
                    any_checked = False

                    for i in range(item.childCount()):
                        child = item.child(i)
                        if child.checkState(0) == Qt.CheckState.Checked:
                            any_checked = True
                        else:
                            all_checked = False

                    if all_checked:
                        item.setCheckState(0, Qt.CheckState.Checked)
                    elif any_checked:
                        item.setCheckState(0, Qt.CheckState.PartiallyChecked)
                    else:
                        item.setCheckState(0, Qt.CheckState.Unchecked)

            # تحديث حالة عناصر الفئات الأولية
            for category_item in category_items.values():
                update_parent_item(category_item)

            # متغير لتتبع ما إذا كان التغيير يأتي من المستخدم أو من البرنامج
            is_updating = [False]

            # ربط حدث تغيير حالة العناصر
            def on_item_changed(item, column):
                # تجاهل التغييرات أثناء التحديث البرمجي
                if is_updating[0]:
                    return

                if column == 0:
                    # تعيين علامة التحديث لمنع التكرار
                    is_updating[0] = True

                    try:
                        # إذا كان العنصر فئة (له عناصر فرعية)
                        if item.childCount() > 0:
                            check_state = item.checkState(0)
                            # تحديث جميع العناصر الفرعية لتطابق حالة الفئة
                            for i in range(item.childCount()):
                                child = item.child(i)
                                child.setCheckState(0, check_state)

                        # إذا كان العنصر فرعي، تحديث حالة الفئة الأب فقط
                        elif item.parent():
                            parent = item.parent()

                            # حساب حالة الفئة الأب بناءً على العناصر الفرعية
                            checked_count = 0
                            total_children = parent.childCount()

                            for i in range(total_children):
                                if parent.child(i).checkState(0) == Qt.CheckState.Checked:
                                    checked_count += 1

                            # تحديث حالة الفئة الأب
                            if checked_count == 0:
                                parent.setCheckState(0, Qt.CheckState.Unchecked)
                            elif checked_count == total_children:
                                parent.setCheckState(0, Qt.CheckState.Checked)
                            else:
                                parent.setCheckState(0, Qt.CheckState.PartiallyChecked)
                    finally:
                        # إعادة تعيين علامة التحديث
                        is_updating[0] = False

            permissions_tree.itemChanged.connect(on_item_changed)

            layout.addWidget(permissions_tree)

            # أزرار الإجراءات
            buttons_layout = QHBoxLayout()

            save_btn = QPushButton("حفظ الصلاحيات")
            save_btn.setObjectName("action_button")

            cancel_btn = QPushButton("إلغاء")
            cancel_btn.setObjectName("secondary_button")

            buttons_layout.addWidget(save_btn)
            buttons_layout.addWidget(cancel_btn)
            buttons_layout.addStretch()

            layout.addLayout(buttons_layout)

            # ربط الأزرار بالوظائف
            cancel_btn.clicked.connect(dialog.reject)

            # وظيفة حفظ الصلاحيات
            def save_user_permissions():
                # جمع الصلاحيات المحددة
                permissions_data = {}

                # جمع الصلاحيات من جميع الفئات
                for _, category_item in category_items.items():
                    for i in range(category_item.childCount()):
                        child = category_item.child(i)
                        permission_name = child.text(0)
                        # التحقق من حالة العنصر الفرعي
                        is_allowed = child.checkState(0) == Qt.CheckState.Checked
                        permissions_data[permission_name] = is_allowed

                print(f"جاري حفظ {len(permissions_data)} صلاحية للمستخدم {user_id}")

                # طباعة الصلاحيات للتحقق
                for perm_name, is_allowed in permissions_data.items():
                    print(f"الصلاحية: {perm_name} = {is_allowed}")

                # حفظ الصلاحيات في قاعدة البيانات
                success, message = UserController.save_user_permissions(user_id, permissions_data)

                if success:
                    dialog.accept()

                    # عرض رسالة نجاح بسيطة
                    QMessageBox.information(
                        self,
                        "تم حفظ الصلاحيات",
                        f"{message}\n\nتم تطبيق التغييرات بنجاح!",
                        QMessageBox.Ok
                    )
                    
                    # تطبيق التغييرات فوراً
                    try:
                        # تحديث واجهة المستخدم
                        main_window = self.parent()
                        while main_window and not hasattr(main_window, 'refresh_user_permissions'):
                            main_window = main_window.parent()
                        if main_window and hasattr(main_window, 'refresh_user_permissions'):
                            self._safely_call_method(main_window, 'refresh_user_permissions')
                            
                    except Exception as e:
                        print(f"خطأ في تطبيق تغييرات الصلاحيات: {str(e)}")
                else:
                    QMessageBox.warning(dialog, "خطأ", message)

            save_btn.clicked.connect(save_user_permissions)

            # عرض الحوار
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إدارة صلاحيات المستخدم: {str(e)}")

    def on_permission_changed(self, row, col, permission_name, category, state):
        """تغيير صلاحيات المستخدم عند تغيير قيمة checkbox"""
        # تمييز أن هناك تغييرات لم يتم حفظها
        role_name = self.permissions_table.horizontalHeaderItem(col).text()

        print(f"تم تغيير صلاحية '{permission_name}' في فئة '{category}' للدور '{role_name}' إلى {state > 0}")

        # تحديث حالة checkbox الفئة بناءً على حالة جميع الصلاحيات الفرعية
        self.update_category_checkbox_state(category, col)

    def on_category_permission_changed(self, category, row, col, state):
        """تغيير جميع صلاحيات الفئة عند تغيير قيمة checkbox الفئة"""
        role_name = self.permissions_table.horizontalHeaderItem(col).text()
        print(f"تم تغيير جميع صلاحيات فئة '{category}' للدور '{role_name}' إلى {state > 0}")

        # تحديث جميع الصلاحيات الفرعية للفئة
        self.update_all_category_permissions(category, col, state > 0)

    def update_category_checkbox_state(self, category, col):
        """تحديث حالة checkbox الفئة بناءً على حالة جميع الصلاحيات الفرعية"""
        # البحث عن صف الفئة
        category_row = -1
        for row in range(self.permissions_table.rowCount()):
            item = self.permissions_table.item(row, 0)
            if item and item.text().strip() == category:
                category_row = row
                break

        if category_row == -1:
            return

        # البحث عن جميع الصلاحيات الفرعية للفئة
        all_checked = True
        any_checked = False

        row = category_row + 1
        while row < self.permissions_table.rowCount():
            item = self.permissions_table.item(row, 0)
            if not item or not item.text().startswith("    "):  # إذا وصلنا إلى فئة جديدة
                break

            # فحص حالة الـ checkbox
            cell_widget = self.permissions_table.cellWidget(row, col)
            if cell_widget:
                checkbox = cell_widget.layout().itemAt(0).widget()
                if checkbox.isChecked():
                    any_checked = True
                else:
                    all_checked = False

            row += 1

        # تحديث حالة checkbox الفئة
        category_cell_widget = self.permissions_table.cellWidget(category_row, col)
        if category_cell_widget:
            category_checkbox = category_cell_widget.layout().itemAt(0).widget()

            # تعيين حالة الـ checkbox بناءً على حالة الصلاحيات الفرعية
            # إذا كانت جميع الصلاحيات الفرعية محددة، يتم تحديد checkbox الفئة
            # إذا كانت بعض الصلاحيات الفرعية محددة، يتم تعيين حالة checkbox الفئة إلى Qt.CheckState.PartiallyChecked
            # إذا لم تكن أي صلاحية فرعية محددة، يتم إلغاء تحديد checkbox الفئة
            if all_checked:
                category_checkbox.setCheckState(Qt.CheckState.Checked)
            elif any_checked:
                category_checkbox.setCheckState(Qt.CheckState.PartiallyChecked)
            else:
                category_checkbox.setCheckState(Qt.CheckState.Unchecked)

    def update_all_category_permissions(self, category, col, checked):
        """تحديث جميع الصلاحيات الفرعية للفئة"""
        # البحث عن صف الفئة
        category_row = -1
        for row in range(self.permissions_table.rowCount()):
            item = self.permissions_table.item(row, 0)
            if item and item.text().strip() == category:
                category_row = row
                break

        if category_row == -1:
            return

        # تحديث جميع الصلاحيات الفرعية للفئة
        row = category_row + 1
        while row < self.permissions_table.rowCount():
            item = self.permissions_table.item(row, 0)
            if not item or not item.text().startswith("    "):  # إذا وصلنا إلى فئة جديدة
                break

            # تحديث حالة الـ checkbox
            cell_widget = self.permissions_table.cellWidget(row, col)
            if cell_widget:
                checkbox = cell_widget.layout().itemAt(0).widget()
                checkbox.setChecked(checked)

            row += 1

    def save_permissions(self):
        """حفظ تغييرات الصلاحيات في قاعدة البيانات"""
        try:
            # إنشاء قاموس لتخزين الصلاحيات
            permissions = {}

            # استرجاع الصلاحيات من الجدول
            for row in range(self.permissions_table.rowCount()):
                item = self.permissions_table.item(row, 0)
                if not item:
                    continue

                text = item.text().strip()

                # تخطي صفوف الفئات
                if not text.startswith("    "):
                    continue

                # استخراج اسم الصلاحية (إزالة المسافات البادئة)
                permission_name = text.strip()

                for col in range(1, 4):  # الأعمدة 1, 2, 3 (مدير، كاشير، مدير مخزون)
                    role_name = self.permissions_table.horizontalHeaderItem(col).text()

                    # الحصول على حالة الـ checkbox
                    cell_widget = self.permissions_table.cellWidget(row, col)
                    if not cell_widget:
                        continue

                    checkbox = cell_widget.layout().itemAt(0).widget()
                    is_allowed = checkbox.isChecked()

                    # إضافة الصلاحية إلى القاموس
                    if role_name not in permissions:
                        permissions[role_name] = {}

                    permissions[role_name][permission_name] = is_allowed

            # حفظ الصلاحيات في قاعدة البيانات
            success, message = UserController.save_role_permissions(permissions)

            if success:
                QMessageBox.information(self, "تم الحفظ", message)
                # إعادة تحميل الصلاحيات لتحديث الواجهة
                self.load_permissions_data()
            else:
                QMessageBox.warning(self, "خطأ", message)

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حفظ الصلاحيات: {str(e)}")

    def update_activation_status(self):
        """تحديث حالة التفعيل في الواجهة"""
        try:
            if self.license_manager.is_activated():
                license_info = self.license_manager.get_license_info()
                if license_info:
                    self.activation_status.setText("✅ مُفعل")
                    self.activation_status.setStyleSheet("color: #28a745; font-weight: bold;")

                    # عرض معلومات الترخيص
                    info_text = f"""📋 اسم العميل: {license_info['customer']}
🏷️ نوع الترخيص: {license_info['type']}
📅 تاريخ التفعيل: {license_info['activation_date']}
⏰ حالة الترخيص: {license_info['status']}"""

                    # عرض الأيام المتبقية أو ترخيص دائم
                    if isinstance(license_info['days_left'], str) and license_info['days_left'] == "ترخيص دائم":
                        info_text += "\n⏳ نوع الترخيص: ترخيص دائم"
                    elif license_info['days_left'] > 0:
                        info_text += f"\n⏳ الأيام المتبقية: {license_info['days_left']} يوم"

                    self.license_info.setText(info_text.strip())
                    self.license_info.setStyleSheet("color: #495057; background-color: #f8f9fa; padding: 10px; border-radius: 5px;")

                    self.activate_btn.setEnabled(False)
                    self.license_key_input.setEnabled(False)
                else:
                    self.activation_status.setText("❌ خطأ في الترخيص")
                    self.activation_status.setStyleSheet("color: #dc3545; font-weight: bold;")
            else:
                self.activation_status.setText("❌ غير مُفعل")
                self.activation_status.setStyleSheet("color: #dc3545; font-weight: bold;")
                self.license_info.setText("البرنامج غير مُفعل. يرجى إدخال مفتاح التفعيل.")
                self.license_info.setStyleSheet("color: #6c757d; font-style: italic;")

                self.activate_btn.setEnabled(True)
                self.license_key_input.setEnabled(True)

        except Exception as e:
            print(f"خطأ في تحديث حالة التفعيل: {str(e)}")

    def activate_license(self):
        """تفعيل البرنامج"""
        try:
            license_key = self.license_key_input.text().strip()

            if not license_key:
                QMessageBox.warning(self, "خطأ", "يرجى إدخال مفتاح التفعيل")
                return

            # محاولة تفعيل البرنامج
            result = self.license_manager.activate_license(license_key)

            if result['success']:
                # إعادة تحميل بيانات الترخيص فوراً بعد التفعيل
                self.license_manager.load_license()
                
                QMessageBox.information(self, "نجح التفعيل", result['message'])
                self.update_activation_status()
                self.license_key_input.clear()

                # إشعار النافذة الرئيسية بالتفعيل
                self.notify_main_window_activation()
            else:
                QMessageBox.critical(self, "فشل التفعيل", result['error'])

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التفعيل: {str(e)}")

    def deactivate_license(self):
        """إلغاء تفعيل البرنامج - هذه الوظيفة معطلة حسب الطلب"""
        QMessageBox.information(self, "غير متوفر", "تم تعطيل زر إلغاء التفعيل حسب الطلب.")

    def notify_main_window_activation(self):
        """إشعار النافذة الرئيسية بالتفعيل"""
        try:
            # البحث عن النافذة الرئيسية
            main_window = None
            parent = self.parent()

            while parent:
                if hasattr(parent, 'refresh_license_status'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window:
                # استخدام الطريقة الجديدة لإعادة فحص الترخيص
                main_window.refresh_license_status()
            else:
                # العودة للطريقة القديمة في حالة عدم وجود الطريقة الجديدة
                parent = self.parent()
                while parent:
                    if hasattr(parent, 'enable_all_tabs'):
                        main_window = parent
                        break
                    parent = parent.parent()
                    
                if main_window:
                    main_window.enable_all_tabs()

        except Exception as e:
            print(f"خطأ في إشعار النافذة الرئيسية: {str(e)}")

    def notify_main_window_deactivation(self):
        """إشعار النافذة الرئيسية بإلغاء التفعيل"""
        try:
            # البحث عن النافذة الرئيسية
            main_window = None
            parent = self.parent()

            while parent:
                if hasattr(parent, 'refresh_license_status'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window:
                # استخدام الطريقة الجديدة لإعادة فحص الترخيص
                main_window.refresh_license_status()
            else:
                # العودة للطريقة القديمة في حالة عدم وجود الطريقة الجديدة
                parent = self.parent()
                while parent:
                    if hasattr(parent, 'disable_all_tabs_except_settings'):
                        main_window = parent
                        break
                    parent = parent.parent()
                    
                if main_window:
                    main_window.disable_all_tabs_except_settings()

        except Exception as e:
            print(f"خطأ في إشعار النافذة الرئيسية: {str(e)}")

    def toggle_license_info(self):
        """إظهار/إخفاء معلومات التفعيل"""
        try:
            if self.license_info_visible:
                # إخفاء معلومات التفعيل
                self.license_info.hide()
                self.license_info_toggle_btn.setText("▶")
                self.license_info_toggle_btn.setToolTip("انقر لإظهار معلومات التفعيل")
                self.license_info_visible = False
            else:
                # إظهار معلومات التفعيل
                self.license_info.show()
                self.license_info_toggle_btn.setText("▼")
                self.license_info_toggle_btn.setToolTip("انقر لإخفاء معلومات التفعيل")
                self.license_info_visible = True

        except Exception as e:
            print(f"خطأ في تبديل عرض معلومات التفعيل: {str(e)}")

    # ===== دوال callback للتعامل مع تغييرات الإعدادات =====

    def on_company_settings_changed(self, new_values, old_values):
        """معالجة تغيير إعدادات الشركة"""
        try:
            print(f"تم تغيير إعدادات الشركة: {new_values}")
            
            # تحديث النافذة الرئيسية إذا تغير اسم الشركة
            if 'name' in new_values:
                main_window = self.parent()
                while main_window and not hasattr(main_window, 'setWindowTitle'):
                    main_window = main_window.parent()
                if main_window:
                    main_window.setWindowTitle(f"SMART MANAGER - {new_values['name']}")
                    print(f"تم تحديث عنوان النافذة إلى: {new_values['name']}")

            # تحديث أي مكونات أخرى تعتمد على معلومات الشركة
            self._update_company_dependent_components(new_values)
            
        except Exception as e:
            print(f"خطأ في معالجة تغيير إعدادات الشركة: {str(e)}")

    def on_currency_settings_changed(self, new_values, old_values):
        """معالجة تغيير إعدادات العملة"""
        try:
            print(f"تم تغيير إعدادات العملة: {new_values}")
            
            # تحديث جميع المكونات التي تعرض العملة
            self._update_currency_dependent_components(new_values)
            
        except Exception as e:
            print(f"خطأ في معالجة تغيير إعدادات العملة: {str(e)}")

    def on_printer_settings_changed(self, new_values, old_values):
        """معالجة تغيير إعدادات الطباعة"""
        try:
            print(f"تم تغيير إعدادات الطباعة: {new_values}")
            
            # تحديث إعدادات الطباعة في النظام
            self._update_printer_settings(new_values)
            
        except Exception as e:
            print(f"خطأ في معالجة تغيير إعدادات الطباعة: {str(e)}")

    def on_invoice_settings_changed(self, new_values, old_values):
        """معالجة تغيير إعدادات الفواتير"""
        try:
            print(f"تم تغيير إعدادات الفواتير: {new_values}")
            
            # تحديث إعدادات الفواتير في النظام
            self._update_invoice_settings(new_values)
            
        except Exception as e:
            print(f"خطأ في معالجة تغيير إعدادات الفواتير: {str(e)}")

    def on_backup_settings_changed(self, new_values, old_values):
        """معالجة تغيير إعدادات النسخ الاحتياطي"""
        try:
            print(f"تم تغيير إعدادات النسخ الاحتياطي: {new_values}")
            
            # تحديث إعدادات النسخ الاحتياطي
            self._update_backup_settings(new_values)
            
        except Exception as e:
            print(f"خطأ في معالجة تغيير إعدادات النسخ الاحتياطي: {str(e)}")

    def on_ui_settings_changed(self, new_values, old_values):
        """معالجة تغيير إعدادات الواجهة"""
        try:
            print(f"تم تغيير إعدادات الواجهة: {new_values}")
            
            # تحديث إعدادات الواجهة
            if 'theme' in new_values:
                self._apply_theme_change(new_values['theme'])
            if 'language' in new_values:
                self._apply_language_change(new_values['language'])
            if 'font_size' in new_values:
                self._apply_font_size_change(new_values['font_size'])
                
        except Exception as e:
            print(f"خطأ في معالجة تغيير إعدادات الواجهة: {str(e)}")

    # ===== دوال مساعدة لتطبيق التغييرات =====

    def _update_company_dependent_components(self, company_settings):
        """تحديث المكونات التي تعتمد على معلومات الشركة"""
        try:
            # تحديث أي تقارير أو فواتير مفتوحة
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'refresh_all_views'):
                main_window = main_window.parent()
            if main_window and hasattr(main_window, 'refresh_all_views'):
                self._safely_call_method(main_window, 'refresh_all_views')
                
        except Exception as e:
            print(f"خطأ في تحديث المكونات المعتمدة على الشركة: {str(e)}")

    def _update_currency_dependent_components(self, currency_settings):
        """تحديث المكونات التي تعتمد على العملة"""
        try:
            # تحديث عرض الأسعار في جميع النوافذ
            main_window = self.parent()
            while main_window and not hasattr(main_window, 'refresh_currency_display'):
                main_window = main_window.parent()
            if main_window and hasattr(main_window, 'refresh_currency_display'):
                self._safely_call_method(main_window, 'refresh_currency_display')
                
        except Exception as e:
            print(f"خطأ في تحديث المكونات المعتمدة على العملة: {str(e)}")

    def _update_printer_settings(self, printer_settings):
        """تحديث إعدادات الطباعة في النظام"""
        try:
            # تحديث إعدادات الطابعة في thermal_printer_helper
            if 'default_printer' in printer_settings:
                # يمكن إضافة كود لتحديث الطابعة الافتراضية
                pass
            if 'paper_width' in printer_settings:
                # يمكن إضافة كود لتحديث حجم الورق
                pass
                
        except Exception as e:
            print(f"خطأ في تحديث إعدادات الطباعة: {str(e)}")

    def _update_invoice_settings(self, invoice_settings):
        """تحديث إعدادات الفواتير في النظام"""
        try:
            # تحديث إعدادات الفواتير في النظام
            if 'notes' in invoice_settings:
                # يمكن إضافة كود لتحديث ملاحظات الفواتير
                pass
            if 'auto_print_invoice' in invoice_settings:
                # يمكن إضافة كود لتحديث الطباعة التلقائية
                pass
                
        except Exception as e:
            print(f"خطأ في تحديث إعدادات الفواتير: {str(e)}")

    def _update_backup_settings(self, backup_settings):
        """تحديث إعدادات النسخ الاحتياطي"""
        try:
            # تحديث مؤقت النسخ الاحتياطي التلقائي
            if 'auto_backup' in backup_settings or 'interval_days' in backup_settings:
                self.setup_auto_backup()
                
        except Exception as e:
            print(f"خطأ في تحديث إعدادات النسخ الاحتياطي: {str(e)}")

    def _apply_theme_change(self, new_theme):
        """تطبيق تغيير الثيم"""
        try:
            print(f"تطبيق ثيم جديد: {new_theme}")
            # يمكن إضافة كود لتطبيق الثيم الجديد
            
        except Exception as e:
            print(f"خطأ في تطبيق الثيم الجديد: {str(e)}")

    def _apply_language_change(self, new_language):
        """تطبيق تغيير اللغة"""
        try:
            print(f"تطبيق لغة جديدة: {new_language}")
            # يمكن إضافة كود لتطبيق اللغة الجديدة
            
        except Exception as e:
            print(f"خطأ في تطبيق اللغة الجديدة: {str(e)}")

    def _apply_font_size_change(self, new_font_size):
        """تطبيق تغيير حجم الخط"""
        try:
            print(f"تطبيق حجم خط جديد: {new_font_size}")
            # يمكن إضافة كود لتطبيق حجم الخط الجديد
            
        except Exception as e:
            print(f"خطأ في تطبيق حجم الخط الجديد: {str(e)}")

    def on_paper_size_changed(self, paper_size):
        """معالج تغيير حجم الورق"""
        try:
            size_descriptions = {
                "58mm": "ورق حراري صغير (58×200 مم)",
                "80mm": "ورق حراري عادي (80×250 مم)",
                "A4": "ورق A4 عادي (210×297 مم)"
            }
            
            description = size_descriptions.get(paper_size, paper_size)
            
            # عرض رسالة تأكيد بسيطة في شريط الحالة (إذا كان متاحاً)
            print(f"تم تغيير حجم الورق إلى: {description}")
            
            # يمكن إضافة المزيد من المعالجة هنا إذا لزم الأمر
            
        except Exception as e:
            print(f"خطأ في معالجة تغيير حجم الورق: {str(e)}")

    def test_print_invoice(self):
        """اختبار طباعة فاتورة تجريبية"""
        try:
            from PyQt5.QtWidgets import QMessageBox
            from utils.thermal_printer_helper import ESCPOSInvoicePrinter, PrinterDetector
            import datetime
            
            # التحقق من وجود طابعة
            printer_detector = PrinterDetector()
            selected_printer = self.default_printer_combo.currentText()
            
            if not selected_printer or selected_printer == "لا توجد طابعات متاحة":
                QMessageBox.warning(
                    self, 
                    "تحذير", 
                    "يرجى اختيار طابعة أولاً من القائمة المنسدلة."
                )
                return
            
            # التحقق من توفر الطابعة
            if not printer_detector.is_printer_available(selected_printer):
                QMessageBox.warning(
                    self, 
                    "تحذير", 
                    f"الطابعة '{selected_printer}' غير متاحة حالياً.\nيرجى التأكد من توصيل الطابعة وتشغيلها."
                )
                return
            
            # إنشاء بيانات فاتورة تجريبية
            test_invoice_data = {
                'reference_number': 'TEST-001',
                'date': datetime.datetime.now().strftime("%Y/%m/%d %H:%M"),
                'customer_name': 'عميل تجريبي',
                'total': 150.00
            }
            
            test_items_data = [
                {
                    'product_name': 'منتج تجريبي 1',
                    'quantity': 2,
                    'unit_price': 50.00,
                    'total_price': 100.00
                },
                {
                    'product_name': 'منتج تجريبي 2',
                    'quantity': 1,
                    'unit_price': 50.00,
                    'total_price': 50.00
                }
            ]
            
            # معلومات الشركة التجريبية
            test_company_info = {
                'name': 'شركة تجريبية',
                'phone': '01234567890',
                'address': 'عنوان تجريبي'
            }
            
            # محاولة الطباعة
            escpos_printer = ESCPOSInvoicePrinter()
            success = escpos_printer.print_invoice(
                test_invoice_data,
                test_items_data,
                test_company_info,
                selected_printer
            )
            
            if success:
                QMessageBox.information(
                    self,
                    "نجح الاختبار",
                    f"✅ تم طباعة الفاتورة التجريبية بنجاح على الطابعة:\n{selected_printer}\n\nإعدادات الطباعة تعمل بشكل صحيح!"
                )
            else:
                QMessageBox.warning(
                    self,
                    "فشل الاختبار",
                    f"❌ فشل في طباعة الفاتورة التجريبية على الطابعة:\n{selected_printer}\n\nيرجى التحقق من:\n• توصيل الطابعة\n• تشغيل الطابعة\n• توفر الورق والحبر"
                )
                
        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ في الاختبار",
                f"حدث خطأ أثناء اختبار الطباعة:\n{str(e)}\n\nيرجى التحقق من إعدادات الطابعة والمحاولة مرة أخرى."
            )
            print(f"خطأ في اختبار الطباعة: {str(e)}")

    def setup_settings_callbacks(self):
        """إعداد callbacks للاستماع لتغييرات الإعدادات"""
        try:
            # تسجيل هذا المكون للاستماع لجميع فئات الإعدادات
            settings_categories = ['company', 'currency', 'printer', 'invoice', 'backup', 'ui']
            
            success = self.settings_manager.register_component(
                self, 
                settings_categories
            )
            
            if success:
                print("تم تسجيل SettingsView للاستماع لتغييرات الإعدادات بنجاح")
            else:
                print("فشل في تسجيل SettingsView للاستماع لتغييرات الإعدادات")
                
        except Exception as e:
            print(f"خطأ في إعداد callbacks الإعدادات: {str(e)}")
