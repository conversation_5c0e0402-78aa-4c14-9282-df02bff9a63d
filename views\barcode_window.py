# -*- coding: utf-8 -*-
"""
نافذة الباركود
توفر واجهة شاملة لإنشاء وطباعة الباركود مع دعم طابعات متعددة
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel, QPushButton,
                            QFrame, QWidget, QSizePolicy, QLineEdit, QComboBox,
                            QTextEdit, QGroupBox, QGridLayout, QSpacerItem,
                            QMessageBox, QProgressBar, QCheckBox, QSpinBox, QApplication,
                            QScrollArea, QListWidget, QListWidgetItem, QShortcut)
from PyQt5.QtCore import Qt, QTimer, QThread, pyqtSignal, QSizeF
from PyQt5.QtGui import QFont, QIcon, QPixmap, Q<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, QKeySequence, QBrush, QColor
from PyQt5.QtPrintSupport import QPrinter
from styles import AppStyles
from utils.thermal_printer_helper import BarcodeHelper, BarcodeMultiPrinter
# استيراد إعدادات طابعات الاستيكرات
from utils.sticker_printer_presets import (
    STICKER_SIZES, PRINTER_PRESETS, get_recommended_settings,
    validate_sticker_compatibility, suggest_sticker_size
)
# ملاحظة: تم إزالة طريقة النوت باد واستخدام طرق طباعة مباشرة أكثر
import random
import time
import subprocess
import os


class PrintThread(QThread):
    """خيط منفصل للطباعة لتجنب تجميد الواجهة"""
    finished = pyqtSignal(bool, str)
    progress = pyqtSignal(str)

    def __init__(self, barcode_text, printer_name=None, quantity=1, parent_window=None, show_barcode_text=True, show_price=False, price_text=""):
        super().__init__()
        self.barcode_text = barcode_text
        self.printer_name = printer_name
        self.quantity = quantity
        self.multi_printer = BarcodeMultiPrinter()
        self.parent_window = parent_window
        self.show_barcode_text = show_barcode_text
        self.show_price = show_price
        self.price_text = price_text

    def run(self):
        try:
            total_copies = self.quantity
            successful_copies = 0

            for copy_num in range(1, total_copies + 1):
                self.progress.emit(f"جاري طباعة النسخة {copy_num} من {total_copies}...")

                # طباعة باركود بسيط
                success, method = self.multi_printer.print_barcode_best_method(
                    self.barcode_text, self.printer_name,
                    self.show_barcode_text, self.show_price, self.price_text
                )

                if success:
                    successful_copies += 1
                else:
                    # في حالة فشل إحدى النسخ، أكمل المحاولة مع النسخ الأخرى
                    print(f"فشل في طباعة النسخة {copy_num}: {method}")

            # تقرير النتيجة النهائية
            if successful_copies == total_copies:
                self.finished.emit(True, f"تم طباعة جميع النسخ بنجاح ({successful_copies} نسخة)")
            elif successful_copies > 0:
                self.finished.emit(True, f"تم طباعة {successful_copies} من {total_copies} نسخة بنجاح")
            else:
                self.finished.emit(False, "فشل في طباعة جميع النسخ")

        except Exception as e:
            self.finished.emit(False, f"خطأ في الطباعة: {str(e)}")
    
    def generate_simple_barcode_pattern(self, text):
        """توليد نمط باركود Code 128 Set B عالمي مفهوم من أي قارئ باركود"""
        # جدول Code 128 Set B الصحيح - كل حرف له نمط فريد حسب قيمة ASCII
        # Code value = ASCII value - 32 (معيار Code 128 Set B العالمي)
        code128_patterns = [
            '11011001100',  # 0: Space (ASCII 32)
            '11001101100',  # 1: ! (ASCII 33)
            '11001100110',  # 2: " (ASCII 34)
            '10010011000',  # 3: # (ASCII 35)
            '10010001100',  # 4: $ (ASCII 36)
            '10001001100',  # 5: % (ASCII 37)
            '10011001000',  # 6: & (ASCII 38)
            '10011000100',  # 7: ' (ASCII 39)
            '10001100100',  # 8: ( (ASCII 40)
            '11001001000',  # 9: ) (ASCII 41)
            '11001000100',  # 10: * (ASCII 42)
            '11000100100',  # 11: + (ASCII 43)
            '10110011100',  # 12: , (ASCII 44)
            '10011011100',  # 13: - (ASCII 45)
            '10011001110',  # 14: . (ASCII 46)
            '10111001000',  # 15: / (ASCII 47)
            '10011101000',  # 16: 0 (ASCII 48)
            '10011100100',  # 17: 1 (ASCII 49)
            '10001101000',  # 18: 2 (ASCII 50)
            '10001100010',  # 19: 3 (ASCII 51)
            '11010001000',  # 20: 4 (ASCII 52)
            '11000101000',  # 21: 5 (ASCII 53)
            '11000100010',  # 22: 6 (ASCII 54)
            '11000100001',  # 23: 7 (ASCII 55)
            '11000010100',  # 24: 8 (ASCII 56)
            '11000010010',  # 25: 9 (ASCII 57)
            '11000010001',  # 26: : (ASCII 58)
            '11001010000',  # 27: ; (ASCII 59)
            '11001000010',  # 28: < (ASCII 60)
            '11000001010',  # 29: = (ASCII 61)
            '10100011000',  # 30: > (ASCII 62)
            '10001011000',  # 31: ? (ASCII 63)
            '10001000110',  # 32: @ (ASCII 64)
            '10110001000',  # 33: A (ASCII 65)
            '10110000100',  # 34: B (ASCII 66)
            '10001101100',  # 35: C (ASCII 67)
            '10001100011',  # 36: D (ASCII 68)
            '10011000011',  # 37: E (ASCII 69)
            '10011000110',  # 38: F (ASCII 70)
            '10011010000',  # 39: G (ASCII 71)
            '10001001010',  # 40: H (ASCII 72)
            '10011101100',  # 41: I (ASCII 73)
            '10010001110',  # 42: J (ASCII 74)
            '10001110100',  # 43: K (ASCII 75)
            '10001110010',  # 44: L (ASCII 76)
            '11101001000',  # 45: M (ASCII 77)
            '11101000100',  # 46: N (ASCII 78)
            '11100100100',  # 47: O (ASCII 79)
            '11100010100',  # 48: P (ASCII 80)
            '11100010010',  # 49: Q (ASCII 81)
            '11010001001',  # 50: R (ASCII 82)
            '11000101001',  # 51: S (ASCII 83)
            '11000100101',  # 52: T (ASCII 84)
            '11000100011',  # 53: U (ASCII 85)
            '11000010101',  # 54: V (ASCII 86)
            '11000010011',  # 55: W (ASCII 87)
            '11001010001',  # 56: X (ASCII 88)
            '11001000101',  # 57: Y (ASCII 89)
            '11001000011',  # 58: Z (ASCII 90)
            '11000110001',  # 59: [ (ASCII 91)
            '11000011001',  # 60: \ (ASCII 92)
            '11000011100',  # 61: ] (ASCII 93)
            '11001011000',  # 62: ^ (ASCII 94)
            '11001000110',  # 63: _ (ASCII 95)
            '11001000011',  # 64: ` (ASCII 96)
            '11010001100',  # 65: a (ASCII 97)
            '11000101100',  # 66: b (ASCII 98)
            '11000100110',  # 67: c (ASCII 99)
            '11000100001',  # 68: d (ASCII 100)
            '11001010100',  # 69: e (ASCII 101)
            '11001000001',  # 70: f (ASCII 102)
            '11000100001',  # 71: g (ASCII 103)
            '11000010100',  # 72: h (ASCII 104)
            '11000010010',  # 73: i (ASCII 105)
            '11000010001',  # 74: j (ASCII 106)
            '11001010000',  # 75: k (ASCII 107)
            '11001000010',  # 76: l (ASCII 108)
            '11000001010',  # 77: m (ASCII 109)
            '10100011000',  # 78: n (ASCII 110)
            '10001011000',  # 79: o (ASCII 111)
            '10001000110',  # 80: p (ASCII 112)
            '10110001000',  # 81: q (ASCII 113)
            '10110000100',  # 82: r (ASCII 114)
            '10001101100',  # 83: s (ASCII 115)
            '10001100011',  # 84: t (ASCII 116)
            '10011000011',  # 85: u (ASCII 117)
            '10011000110',  # 86: v (ASCII 118)
            '10011010000',  # 87: w (ASCII 119)
            '10001001010',  # 88: x (ASCII 120)
            '10011101100',  # 89: y (ASCII 121)
            '10010001110',  # 90: z (ASCII 122)
            '10001110100',  # 91: { (ASCII 123)
            '10001110010',  # 92: | (ASCII 124)
            '11101001000',  # 93: } (ASCII 125)
            '11101000100',  # 94: ~ (ASCII 126)
        ]
        
        def get_pattern_for_char(char):
            """الحصول على نمط الحرف حسب قيمة ASCII - معيار Code 128 Set B العالمي"""
            ascii_val = ord(char)
            if 32 <= ascii_val <= 126:  # نطاق ASCII المدعوم في Code 128 Set B
                code_value = ascii_val - 32  # قيمة الكود = قيمة ASCII - 32
                if code_value < len(code128_patterns):
                    return code128_patterns[code_value]
            # إرجاع نمط المسافة كقيمة افتراضية للأحرف غير المدعومة
            return code128_patterns[0]  # نمط المسافة (ASCII 32)
        
        # بداية Code 128 Set B (Start B) - النمط العالمي المعتمد
        pattern = '11010010000'

        # حساب checksum حسب معيار Code 128 العالمي
        checksum = 104  # قيمة Start Code B الثابتة

        # إضافة نمط لكل حرف مع حساب checksum صحيح
        for i, char in enumerate(text):
            char_pattern = get_pattern_for_char(char)
            pattern += char_pattern
            
            # حساب checksum حسب معيار Code 128 Set B العالمي
            # قيمة الحرف = ASCII value - 32
            ascii_val = ord(char)
            if 32 <= ascii_val <= 126:
                char_value = ascii_val - 32
            else:
                char_value = 0  # قيمة المسافة للأحرف غير المدعومة
            
            checksum += char_value * (i + 1)

        # إضافة checksum باستخدام نفس جدول الأنماط
        checksum_value = checksum % 103
        if checksum_value < len(code128_patterns):
            pattern += code128_patterns[checksum_value]
        else:
            pattern += code128_patterns[0]  # نمط المسافة كافتراضي

        # نهاية Code 128 (Stop Pattern) - النمط العالمي المعتمد
        pattern += '1100011101011'

        return pattern

    def enhance_barcode_quality(self, pixmap):
        """تحسين جودة الباركود بعد الإنشاء مع تحسينات متقدمة للطباعة"""
        try:
            # إنشاء نسخة محسنة بدقة أعلى (ضعف الحجم للحصول على دقة أفضل)
            scale_factor = 2.0  # مضاعف التحسين
            enhanced_size = pixmap.size() * scale_factor
            enhanced_pixmap = QPixmap(enhanced_size)
            enhanced_pixmap.fill(QColor(255, 255, 255))

            painter = QPainter(enhanced_pixmap)

            # إعدادات رسم محسنة للجودة الفائقة
            painter.setRenderHint(QPainter.Antialiasing, False)  # خطوط حادة
            painter.setRenderHint(QPainter.TextAntialiasing, True)  # نص محسن
            painter.setRenderHint(QPainter.HighQualityAntialiasing, False)
            painter.setRenderHint(QPainter.SmoothPixmapTransform, False)

            # رسم الصورة الأصلية مع تكبير للحصول على دقة أعلى
            painter.scale(scale_factor, scale_factor)
            painter.drawPixmap(0, 0, pixmap)

            painter.end()

            # تطبيق تحسينات إضافية للحواف والتباين
            enhanced_pixmap = self.apply_sharpening_filter(enhanced_pixmap)

            return enhanced_pixmap

        except Exception as e:
            print(f"خطأ في تحسين جودة الباركود: {str(e)}")
            return pixmap  # إرجاع الصورة الأصلية في حالة الخطأ

    def apply_sharpening_filter(self, pixmap):
        """تطبيق مرشح تحسين الحواف والوضوح"""
        try:
            # إنشاء نسخة محسنة للحواف
            sharpened_pixmap = QPixmap(pixmap.size())
            sharpened_pixmap.fill(QColor(255, 255, 255))

            painter = QPainter(sharpened_pixmap)

            # إعدادات رسم للحصول على أوضح صورة ممكنة
            painter.setRenderHint(QPainter.Antialiasing, False)
            painter.setRenderHint(QPainter.TextAntialiasing, True)
            painter.setRenderHint(QPainter.HighQualityAntialiasing, False)
            painter.setRenderHint(QPainter.SmoothPixmapTransform, False)

            # رسم الصورة مع تحسين التباين
            painter.setCompositionMode(QPainter.CompositionMode_SourceOver)
            painter.drawPixmap(0, 0, pixmap)

            painter.end()
            return sharpened_pixmap

        except Exception as e:
            print(f"خطأ في تطبيق مرشح التحسين: {str(e)}")
            return pixmap



    def optimize_printer_settings(self, printer, printer_name):
        """تحسين إعدادات الطابعة حسب النوع للحصول على أفضل جودة احترافية"""
        try:
            from utils.sticker_printer_presets import get_recommended_settings

            # الحصول على الإعدادات الموصى بها للطابعة
            settings = get_recommended_settings(printer_name)

            # تطبيق الدقة الموصى بها مع حد أدنى 1200 DPI للجودة العالية
            if 'dpi' in settings:
                recommended_dpi = max(settings['dpi'], 1200)  # حد أدنى 1200 DPI للجودة العالية
                printer.setResolution(recommended_dpi)
            else:
                printer.setResolution(1200)  # دقة افتراضية 1200 DPI

            # إعدادات احترافية عامة محسنة
            printer.setColorMode(QPrinter.GrayScale)  # أبيض وأسود للوضوح الأمثل
            printer.setPageOrder(QPrinter.FirstPageFirst)
            printer.setOrientation(QPrinter.Portrait)
            printer.setDuplex(QPrinter.DuplexNone)
            printer.setCopyCount(1)
            printer.setFullPage(False)
            printer.setOutputFormat(QPrinter.NativeFormat)

            # إعدادات إضافية لتحسين الجودة
            try:
                # تحسين جودة الطباعة
                printer.setPrintRange(QPrinter.AllPages)
                printer.setCollateCopies(True)
                # تحسين دقة الألوان للحصول على أسود نقي
                printer.setColorMode(QPrinter.GrayScale)
            except:
                pass  # تجاهل الأخطاء إذا لم تكن الإعدادات مدعومة

            # تحسين إعدادات خاصة لطابعات معينة
            printer_lower = printer_name.lower()

            if 'brother' in printer_lower:
                # إعدادات محسنة لطابعات Brother QL
                printer.setResolution(1200)  # دقة 1200 DPI للجودة العالية وتجنب التعرج
                printer.setPageMargins(0.1, 0.1, 0.1, 0.1, printer.Millimeter)  # هوامش أصغر
                # إعدادات خاصة بـ Brother
                printer.setColorMode(QPrinter.GrayScale)

            elif 'dymo' in printer_lower:
                # إعدادات محسنة لطابعات DYMO LabelWriter
                printer.setResolution(1200)  # دقة 1200 DPI للجودة العالية وتجنب التعرج
                printer.setPageMargins(0.05, 0.05, 0.05, 0.05, printer.Millimeter)  # هوامش أصغر
                # إعدادات خاصة بـ DYMO
                printer.setColorMode(QPrinter.GrayScale)

            elif 'zebra' in printer_lower:
                # إعدادات محسنة لطابعات Zebra
                printer.setResolution(1200)  # دقة 1200 DPI للجودة العالية وتجنب التعرج
                printer.setPageMargins(0.15, 0.15, 0.15, 0.15, printer.Millimeter)  # هوامش أصغر
                # إعدادات خاصة بـ Zebra
                printer.setColorMode(QPrinter.GrayScale)

            elif 'tsc' in printer_lower:
                # إعدادات محسنة لطابعات TSC
                printer.setResolution(1200)  # دقة 1200 DPI للجودة العالية وتجنب التعرج
                printer.setPageMargins(0.1, 0.1, 0.1, 0.1, printer.Millimeter)  # هوامش أصغر

            elif 'godex' in printer_lower:
                # إعدادات محسنة لطابعات Godex
                printer.setResolution(1200)  # دقة 1200 DPI للجودة العالية وتجنب التعرج
                printer.setPageMargins(0.15, 0.15, 0.15, 0.15, printer.Millimeter)  # هوامش أصغر

            elif 'citizen' in printer_lower:
                # إعدادات محسنة لطابعات Citizen
                printer.setResolution(1200)  # دقة 1200 DPI للجودة العالية وتجنب التعرج
                printer.setPageMargins(0.1, 0.1, 0.1, 0.1, printer.Millimeter)  # هوامش أصغر

            elif 'argox' in printer_lower:
                # إعدادات محسنة لطابعات Argox
                printer.setResolution(1200)  # دقة 1200 DPI للجودة العالية وتجنب التعرج
                printer.setPageMargins(0.1, 0.1, 0.1, 0.1, printer.Millimeter)  # هوامش أصغر

            else:
                # إعدادات افتراضية للطابعات غير المعروفة
                printer.setResolution(1200)  # دقة 1200 DPI للجودة العالية وتجنب التعرج
                printer.setPageMargins(0.1, 0.1, 0.1, 0.1, printer.Millimeter)  # هوامش أصغر

            print(f"تم تحسين إعدادات الطابعة {printer_name} بدقة {printer.resolution()} DPI")

        except Exception as e:
            print(f"تحذير: لا يمكن تحسين إعدادات الطابعة: {str(e)}")
            # تطبيق إعدادات افتراضية عالية الجودة
            try:
                printer.setResolution(1200)  # دقة 1200 DPI كإعداد افتراضي
                printer.setPageMargins(0.1, 0.1, 0.1, 0.1, printer.Millimeter)  # هوامش أصغر
                printer.setColorMode(QPrinter.GrayScale)
            except:
                pass  # الاستمرار بالإعدادات الافتراضية



    def draw_ultra_smooth_barcode_direct(self, painter, barcode_text, target_width, target_height, start_x, start_y, show_barcode_text=True):
        """رسم باركود مثالي بدون تعرج باستخدام تقنية رياضية خالصة"""
        try:
            # إعدادات الرسم مماثلة للطباعة: لا تنعيم للباركود وتنعيم للنص فقط
            painter.setRenderHint(QPainter.Antialiasing, False)
            painter.setRenderHint(QPainter.TextAntialiasing, True)
            painter.setRenderHint(QPainter.SmoothPixmapTransform, False)
            painter.setRenderHint(QPainter.HighQualityAntialiasing, False)
            
            # استخدام نفس مولد النمط المستخدم في الطباعة لضمان التطابق
            barcode_pattern = BarcodeHelper.generate_barcode_pattern(barcode_text)
            total_bars = len(barcode_pattern)
            
            # حساب عرض الخط مماثل لمسار الطباعة
            min_bar_width = 8
            available_width = int(target_width * 0.80)
            calculated_width = available_width // total_bars
            bar_width = max(min_bar_width, calculated_width)
            
            # ضمان أن العرض رقم زوجي لتجنب التشويه
            if bar_width % 2 != 0:
                bar_width += 1
            
            # حساب العرض الفعلي للباركود
            actual_barcode_width = bar_width * total_bars
                
            # حساب عدد العناصر النصية المفعلة في الطباعة
            text_elements_count = 0
            if show_barcode_text:
                text_elements_count += 1
            
            # حساب ارتفاع الباركود مماثل للطباعة
            if text_elements_count == 0:
                barcode_height = int(target_height * 0.85)  # باركود فقط
            else:
                barcode_height = int(target_height * 0.65)  # باركود + نص الباركود
            
            # حساب موقع الرسم (وسط المنطقة) مع ضمان مساحة للنص أسفل الباركود
            try:
                dpi_y = painter.device().logicalDpiY()
            except Exception:
                dpi_y = 96
            one_mm_px = max(1, int(round(dpi_y / 25.4)))
            metrics_for_layout = painter.fontMetrics()
            text_ascent = metrics_for_layout.ascent() if show_barcode_text else 0
            text_descent = metrics_for_layout.descent() if show_barcode_text else 0
            gap_px = one_mm_px if show_barcode_text else 0
            total_needed_height = barcode_height + gap_px + text_ascent + text_descent
            if total_needed_height > target_height:
                # في الحالات الضيقة، قلل الارتفاع للحفاظ على كامل المحتوى
                reduction = total_needed_height - target_height
                barcode_height = max(10, barcode_height - reduction)
                total_needed_height = barcode_height + gap_px + text_ascent + text_descent
            draw_x = start_x + (target_width - actual_barcode_width) // 2
            draw_y = start_y + (target_height - total_needed_height) // 2
            
            # رسم الخلفية البيضاء
            painter.fillRect(start_x, start_y, target_width, target_height, QColor(255, 255, 255))
            
            # رسم خطوط الباركود بدقة مثالية بدون أي تنعيم
            current_x = draw_x
            for bar in barcode_pattern:
                if bar == '1':
                    # رسم مستطيل مملوء بالكامل بدون أي تنعيم
                    painter.fillRect(current_x, draw_y, bar_width, barcode_height, QColor(0, 0, 0))
                    
                    # رسم حدود إضافية لضمان الوضوح التام
                    painter.setPen(QPen(QColor(0, 0, 0), 1))
                    painter.drawRect(current_x, draw_y, bar_width - 1, barcode_height - 1)
                    
                current_x += bar_width
            
            # إضافة نص الباركود بخط فائق الوضوح وحجم مناسب (إذا مفعل)
            if show_barcode_text:  # تحقق من الخيار
                # مسافة واضحة أسفل الباركود + تعويض خط الأساس لضمان عدم الالتصاق
                # baseline = top + ascent
                # حجم خط متوافق مع مسار الطباعة
                font_size = max(11, int(target_height * 0.16))
                
                font = QFont("Courier New", font_size, QFont.Bold)
                font.setHintingPreference(QFont.PreferFullHinting)
                painter.setFont(font)
                painter.setPen(QPen(QColor(0, 0, 0), 1))
                
                metrics = painter.fontMetrics()
                text_rect = metrics.boundingRect(barcode_text)
                text_x = start_x + (target_width - text_rect.width()) // 2
                # تحويل 1 ملم إلى بكسل بناءً على DPI للجهاز
                try:
                    dpi_y = painter.device().logicalDpiY()
                except Exception:
                    dpi_y = 96
                one_mm_px = max(1, int(round(dpi_y / 25.4)))
                gap = one_mm_px  # مسافة 1 ملم تماماً
                text_y_baseline = draw_y + barcode_height + gap + metrics.ascent()
                # التأكد من عدم قص الجزء السفلي من النص داخل مساحة الرسم
                area_bottom = start_y + target_height
                bottom_limit = area_bottom - max(4, int(target_height * 0.02))
                if text_y_baseline + metrics.descent() > bottom_limit:
                    text_y_baseline = bottom_limit - metrics.descent()
                painter.drawText(text_x, text_y_baseline, barcode_text)
                
        except Exception as e:
            print(f"خطأ في رسم الباركود المباشر: {str(e)}")




class BarcodeWindow(QDialog):
    """نافذة الباركود الشاملة مع دعم طابعات متعددة"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.parent_ref = parent
        self.current_barcode_pixmap = None
        self.multi_printer = BarcodeMultiPrinter()
        self.init_ui()

    def init_ui(self):
        """تهيئة واجهة المستخدم"""
        # إعداد النافذة
        self.setWindowTitle("📱 نافذة إنشاء وطباعة الباركود")

        # تحديد حجم النافذة بناءً على حجم الشاشة
        from PyQt5.QtWidgets import QDesktopWidget
        desktop = QDesktopWidget().availableGeometry()
        screen_width = desktop.width()
        screen_height = desktop.height()

        # حساب الحد الأدنى للحجم بناءً على حجم الشاشة
        min_width = min(800, int(screen_width * 0.85))  # 85% من عرض الشاشة أو 800 كحد أقصى
        min_height = min(650, int(screen_height * 0.80))  # 80% من ارتفاع الشاشة أو 650 كحد أقصى

        self.setMinimumSize(min_width, min_height)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # إنشاء التخطيط الرئيسي مع هوامش أصغر للشاشات الصغيرة
        main_layout = QVBoxLayout(self)

        # تقليل الهوامش للشاشات الصغيرة وتوفير المساحة
        if screen_width < 1200 or screen_height < 800:
            main_layout.setContentsMargins(6, 4, 6, 6)  # تقليل الهوامش العمودية العلوية أكثر
            main_layout.setSpacing(6)  # تقليل المسافة بين العناصر
        else:
            main_layout.setContentsMargins(12, 8, 12, 12)  # تقليل الهوامش حتى للشاشات الكبيرة
            main_layout.setSpacing(10)  # تقليل المسافة بين العناصر

        # إنشاء عنوان النافذة
        self.create_header(main_layout)

        # للشاشات الصغيرة جداً، استخدم منطقة تمرير
        if screen_width < 1000 or screen_height < 700:
            # إنشاء منطقة تمرير للمحتوى الرئيسي
            scroll_area = QScrollArea()
            scroll_area.setWidgetResizable(True)
            scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
            scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

            # إنشاء widget للمحتوى القابل للتمرير
            scroll_content = QWidget()
            scroll_layout = QVBoxLayout(scroll_content)
            scroll_layout.setContentsMargins(0, 0, 0, 0)

            # إنشاء المحتوى الرئيسي داخل منطقة التمرير
            self.create_main_content(scroll_layout)

            # تعيين المحتوى لمنطقة التمرير
            scroll_area.setWidget(scroll_content)
            main_layout.addWidget(scroll_area)
        else:
            # للشاشات العادية، استخدم التخطيط العادي
            self.create_main_content(main_layout)

        # إنشاء أزرار التحكم
        self.create_control_buttons(main_layout)

        # تطبيق الأنماط
        self.apply_styles()

        # تحديث المعاينة الأولية
        self.update_barcode_preview()

        # جعل النافذة قابلة لتغيير الحجم للشاشات الصغيرة
        if screen_width < 1200 or screen_height < 800:
            # للشاشات الصغيرة، اجعل النافذة تملأ معظم الشاشة
            window_width = int(screen_width * 0.95)
            window_height = int(screen_height * 0.90)

            # تحديد موقع النافذة في المنتصف
            x_position = (screen_width - window_width) // 2
            y_position = (screen_height - window_height) // 2

            # تطبيق الحجم والموقع
            self.setGeometry(x_position, y_position, window_width, window_height)

    def create_header(self, layout):
        """إنشاء رأس النافذة"""
        header_frame = QFrame()
        header_frame.setObjectName("header_frame")
        header_layout = QVBoxLayout(header_frame)

        # تقليل الهوامش للشاشات الصغيرة وتوفير المساحة
        from PyQt5.QtWidgets import QDesktopWidget
        desktop = QDesktopWidget().availableGeometry()
        screen_width = desktop.width()
        screen_height = desktop.height()

        # تقليل الهوامش العمودية بشكل أكبر لتوفير المساحة
        if screen_width < 1200 or screen_height < 800:
            header_layout.setContentsMargins(8, 4, 8, 4)  # تقليل الهوامش العمودية من 8 إلى 4
            header_layout.setSpacing(3)  # تقليل المسافة من 5 إلى 3
        else:
            header_layout.setContentsMargins(15, 8, 15, 8)  # تقليل الهوامش العمودية من 15 إلى 8
            header_layout.setSpacing(6)  # تقليل المسافة من 10 إلى 6

        # عنوان رئيسي مع حجم خط أصغر لتوفير المساحة
        title_label = QLabel("📱 نافذة إنشاء وطباعة الباركود")
        title_label.setObjectName("main_title")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # تقليل حجم الخط للشاشات الصغيرة
        if screen_width < 1200 or screen_height < 800:
            title_label.setFont(QFont("Arial", 16, QFont.Bold))  # تقليل من 20 إلى 16
        else:
            title_label.setFont(QFont("Arial", 18, QFont.Bold))  # تقليل من 20 إلى 18
        header_layout.addWidget(title_label)

        # وصف مختصر مع حجم خط أصغر (يخفى في الشاشات الصغيرة جداً)
        if not (screen_width < 1000 or screen_height < 700):  # إخفاء الوصف في الشاشات الصغيرة جداً
            description_label = QLabel("إنشاء وطباعة الباركود مع دعم طابعات متعددة")  # نص أقصر
            description_label.setObjectName("description_label")
            description_label.setAlignment(Qt.AlignmentFlag.AlignCenter)

            # تقليل حجم الخط للوصف
            if screen_width < 1200 or screen_height < 800:
                description_label.setFont(QFont("Arial", 10))  # تقليل من 12 إلى 10
            else:
                description_label.setFont(QFont("Arial", 11))  # تقليل من 12 إلى 11
            header_layout.addWidget(description_label)

        # فاصل أرفع لتوفير المساحة
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("separator")
        separator.setMaximumHeight(1)  # تحديد ارتفاع أقصى للفاصل
        header_layout.addWidget(separator)

        layout.addWidget(header_frame)

    def create_main_content(self, layout):
        """إنشاء المحتوى الرئيسي للنافذة"""
        # تخطيط أفقي للمحتوى الرئيسي
        main_content_layout = QHBoxLayout()

        # تقليل المسافة بين اللوحات للشاشات الصغيرة
        from PyQt5.QtWidgets import QDesktopWidget
        desktop = QDesktopWidget().availableGeometry()
        screen_width = desktop.width()

        if screen_width < 1200:
            main_content_layout.setSpacing(10)  # مسافة أصغر للشاشات الصغيرة
        else:
            main_content_layout.setSpacing(20)  # مسافة عادية للشاشات الكبيرة

        # الجانب الأيسر - إعدادات الباركود
        self.create_settings_panel(main_content_layout)

        # الجانب الأيمن - معاينة الباركود
        self.create_preview_panel(main_content_layout)

        layout.addLayout(main_content_layout)

    def create_settings_panel(self, layout):
        """إنشاء لوحة الإعدادات"""
        settings_frame = QFrame()
        settings_frame.setObjectName("settings_frame")

        # تقليل العرض الأدنى للشاشات الصغيرة
        from PyQt5.QtWidgets import QDesktopWidget
        desktop = QDesktopWidget().availableGeometry()
        screen_width = desktop.width()

        if screen_width < 1200:
            settings_frame.setMinimumWidth(320)  # عرض أصغر للشاشات الصغيرة
        else:
            settings_frame.setMinimumWidth(400)  # عرض عادي للشاشات الكبيرة

        settings_layout = QVBoxLayout(settings_frame)

        # تقليل الهوامش للشاشات الصغيرة
        if screen_width < 1200:
            settings_layout.setContentsMargins(8, 8, 8, 8)
            settings_layout.setSpacing(8)
        else:
            settings_layout.setContentsMargins(15, 15, 15, 15)
            settings_layout.setSpacing(15)

        # مجموعة إعدادات الطابعة
        printer_group = QGroupBox("⚙️ إعدادات الطابعة")
        printer_group.setObjectName("settings_group")
        printer_layout = QGridLayout(printer_group)
        printer_layout.setSpacing(10)
        printer_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        printer_layout.setColumnStretch(1, 1)  # جعل عمود الكومبوكس قابل للتمدد

        # اختيار الطابعة
        printer_layout.addWidget(QLabel("اختيار الطابعة:"), 0, 0)
        self.printer_combo = QComboBox()
        self.printer_combo.setObjectName("settings_combo")
        self.printer_combo.setMinimumHeight(30)  # تحديد ارتفاع ثابت للكومبوكس
        self.load_available_printers()
        self.printer_combo.setToolTip("اختر الطابعة المتصلة بالكمبيوتر")
        self.printer_combo.currentTextChanged.connect(self.on_printer_changed)
        printer_layout.addWidget(self.printer_combo, 0, 1)

        settings_layout.addWidget(printer_group)



        # مجموعة بيانات المنتج
        product_group = QGroupBox("📦 بيانات المنتج")
        product_group.setObjectName("settings_group")
        product_layout = QGridLayout(product_group)
        product_layout.setSpacing(10)

        # كود المنتج
        product_layout.addWidget(QLabel("كود المنتج:"), 0, 0)
        self.product_code_input = QLineEdit()
        self.product_code_input.setObjectName("settings_input")
        self.product_code_input.setPlaceholderText("أدخل كود المنتج (أرقام وحروف إنجليزية)")
        self.product_code_input.textChanged.connect(self.update_barcode_preview)
        self.product_code_input.setToolTip("أدخل كود المنتج للباركود - يدعم الأرقام والحروف الإنجليزية والرموز: -._/+=")
        product_layout.addWidget(self.product_code_input, 0, 1)

        # زر توليد كود عشوائي (أرقام فقط) - حجم متكيف
        generate_btn = QPushButton("🎲 توليد رقمي")
        generate_btn.setObjectName("secondary_button")

        # تحديد حجم الزر بناءً على حجم الشاشة
        if screen_width < 1000:
            generate_btn.setMinimumSize(80, 28)  # أصغر للشاشات الصغيرة جداً
            generate_btn.setFont(QFont("Arial", 8))
        elif screen_width < 1200:
            generate_btn.setMinimumSize(90, 32)  # متوسط للشاشات الصغيرة
            generate_btn.setFont(QFont("Arial", 9))
        else:
            generate_btn.setMinimumSize(100, 36)  # حجم عادي للشاشات الكبيرة
            generate_btn.setFont(QFont("Arial", 10))

        generate_btn.clicked.connect(self.generate_random_numeric_code)
        generate_btn.setToolTip("توليد كود باركود رقمي عشوائي")
        product_layout.addWidget(generate_btn, 0, 2)

        # زر البحث عن منتج - حجم متكيف
        search_product_btn = QPushButton("🔍 اختيار منتج")
        search_product_btn.setObjectName("secondary_button")

        # تحديد حجم الزر بناءً على حجم الشاشة
        if screen_width < 1000:
            search_product_btn.setMinimumSize(80, 28)  # أصغر للشاشات الصغيرة جداً
            search_product_btn.setFont(QFont("Arial", 8))
        elif screen_width < 1200:
            search_product_btn.setMinimumSize(90, 32)  # متوسط للشاشات الصغيرة
            search_product_btn.setFont(QFont("Arial", 9))
        else:
            search_product_btn.setMinimumSize(100, 36)  # حجم عادي للشاشات الكبيرة
            search_product_btn.setFont(QFont("Arial", 10))

        search_product_btn.clicked.connect(self.search_product)
        search_product_btn.setToolTip("البحث عن منتج واختياره")
        product_layout.addWidget(search_product_btn, 0, 3)

        # حجم الباركود
        product_layout.addWidget(QLabel("حجم الباركود:"), 1, 0)
        self.size_combo = QComboBox()
        self.size_combo.setObjectName("settings_combo")
        self.size_combo.addItems(["صغير (8 أرقام)", "متوسط (10 أرقام)", "كبير (12 أرقام)", "كبير جداً (16 رقم)"])
        self.size_combo.setCurrentText("متوسط (10 أرقام)")
        self.size_combo.currentTextChanged.connect(self.on_size_changed)
        self.size_combo.setToolTip("اختر حجم الباركود المناسب")
        product_layout.addWidget(self.size_combo, 1, 1, 1, 2)



        settings_layout.addWidget(product_group)

        # مجموعة خيارات العرض
        display_group = QGroupBox("خيارات العرض")
        display_group.setObjectName("settings_group")
        display_layout = QGridLayout(display_group)

        # خيار عرض رقم الباركود
        self.show_barcode_text = QCheckBox("عرض رقم الباركود أسفل الباركود")
        self.show_barcode_text.setObjectName("settings_checkbox")
        self.show_barcode_text.setChecked(True)  # مفعل بشكل افتراضي
        self.show_barcode_text.setToolTip("عرض رقم الباركود أسفل الباركود المرسوم")
        self.show_barcode_text.stateChanged.connect(self.update_barcode_preview)
        display_layout.addWidget(self.show_barcode_text, 0, 0, 1, 2)

        # خيار عرض سعر المنتج
        self.show_price_text = QCheckBox("عرض سعر المنتج أسفل الرقم")
        self.show_price_text.setObjectName("settings_checkbox")
        self.show_price_text.setChecked(False)
        self.show_price_text.setToolTip("عند التفعيل سيتم طباعة سعر المنتج أسفل رقم الباركود")
        self.show_price_text.stateChanged.connect(self.update_barcode_preview)
        display_layout.addWidget(self.show_price_text, 1, 0, 1, 2)

        # إدخال سعر المنتج
        self.price_input = QLineEdit()
        self.price_input.setObjectName("settings_input")
        self.price_input.setPlaceholderText("أدخل سعر المنتج (مثال: 25.00 ج.م)")
        self.price_input.textChanged.connect(self.update_barcode_preview)
        display_layout.addWidget(QLabel("سعر المنتج:"), 2, 0)
        display_layout.addWidget(self.price_input, 2, 1)

        settings_layout.addWidget(display_group)

        # مساحة مرنة
        settings_layout.addStretch()

        layout.addWidget(settings_frame)

    def create_preview_panel(self, layout):
        """إنشاء لوحة المعاينة"""
        preview_frame = QFrame()
        preview_frame.setObjectName("preview_frame")

        # تقليل العرض الأدنى للشاشات الصغيرة
        from PyQt5.QtWidgets import QDesktopWidget
        desktop = QDesktopWidget().availableGeometry()
        screen_width = desktop.width()

        if screen_width < 1200:
            preview_frame.setMinimumWidth(350)  # عرض أصغر للشاشات الصغيرة
        else:
            preview_frame.setMinimumWidth(450)  # عرض عادي للشاشات الكبيرة

        preview_layout = QVBoxLayout(preview_frame)

        # تقليل الهوامش للشاشات الصغيرة
        if screen_width < 1200:
            preview_layout.setContentsMargins(8, 8, 8, 8)
            preview_layout.setSpacing(8)
        else:
            preview_layout.setContentsMargins(15, 15, 15, 15)
            preview_layout.setSpacing(15)

        # عنوان المعاينة - حجم خط متكيف
        preview_title = QLabel("👁️ معاينة الباركود المباشرة")
        preview_title.setObjectName("preview_title")
        preview_title.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # تحديد حجم الخط بناءً على حجم الشاشة
        if screen_width < 1000:
            preview_title.setFont(QFont("Arial", 11, QFont.Bold))  # أصغر للشاشات الصغيرة جداً
        elif screen_width < 1200:
            preview_title.setFont(QFont("Arial", 12, QFont.Bold))  # متوسط للشاشات الصغيرة
        else:
            preview_title.setFont(QFont("Arial", 14, QFont.Bold))  # حجم عادي للشاشات الكبيرة

        preview_layout.addWidget(preview_title)

        # منطقة عرض الباركود
        self.barcode_display = QLabel()
        self.barcode_display.setObjectName("barcode_display")
        self.barcode_display.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # تقليل حجم منطقة العرض للشاشات الصغيرة
        if screen_width < 1200:
            self.barcode_display.setMinimumHeight(200)  # ارتفاع أصغر
            self.barcode_display.setMinimumWidth(320)   # عرض أصغر
            padding = "8px"  # حشو أصغر
        else:
            self.barcode_display.setMinimumHeight(250)  # ارتفاع عادي
            self.barcode_display.setMinimumWidth(420)   # عرض عادي
            padding = "15px"  # حشو عادي

        self.barcode_display.setStyleSheet(f"""
            QLabel#barcode_display {{
                border: 2px dashed #cbd5e1;
                border-radius: 8px;
                background-color: #f8fafc;
                padding: {padding};
            }}
        """)
        preview_layout.addWidget(self.barcode_display)

        # أزرار المعاينة
        preview_buttons_layout = QHBoxLayout()

        # زر تحديث المعاينة - حجم متكيف
        refresh_btn = QPushButton("🔄 تحديث المعاينة")
        refresh_btn.setObjectName("secondary_button")

        # تحديد حجم الزر بناءً على حجم الشاشة
        if screen_width < 1000:
            refresh_btn.setMinimumSize(100, 30)  # أصغر للشاشات الصغيرة جداً
            refresh_btn.setFont(QFont("Arial", 8))
        elif screen_width < 1200:
            refresh_btn.setMinimumSize(120, 35)  # متوسط للشاشات الصغيرة
            refresh_btn.setFont(QFont("Arial", 9))
        else:
            refresh_btn.setMinimumSize(140, 40)  # حجم عادي للشاشات الكبيرة
            refresh_btn.setFont(QFont("Arial", 10))

        refresh_btn.clicked.connect(self.update_barcode_preview)
        refresh_btn.setToolTip("تحديث معاينة الباركود")
        preview_buttons_layout.addWidget(refresh_btn)

        # زر حفظ الصورة - حجم متكيف
        save_btn = QPushButton("💾 حفظ الصورة")
        save_btn.setObjectName("secondary_button")

        # تحديد حجم الزر بناءً على حجم الشاشة
        if screen_width < 1000:
            save_btn.setMinimumSize(100, 30)  # أصغر للشاشات الصغيرة جداً
            save_btn.setFont(QFont("Arial", 8))
        elif screen_width < 1200:
            save_btn.setMinimumSize(120, 35)  # متوسط للشاشات الصغيرة
            save_btn.setFont(QFont("Arial", 9))
        else:
            save_btn.setMinimumSize(140, 40)  # حجم عادي للشاشات الكبيرة
            save_btn.setFont(QFont("Arial", 10))

        save_btn.clicked.connect(self.save_barcode_image)
        save_btn.setToolTip("حفظ صورة الباركود")
        preview_buttons_layout.addWidget(save_btn)

        preview_layout.addLayout(preview_buttons_layout)

        # إضافة خيار كمية النسخ
        quantity_frame = QFrame()
        quantity_frame.setObjectName("quantity_frame")
        quantity_layout = QHBoxLayout(quantity_frame)
        quantity_layout.setContentsMargins(0, 10, 0, 0)
        quantity_layout.setSpacing(10)

        # تسمية كمية النسخ - حجم خط متكيف
        quantity_label = QLabel("📄 كمية النسخ:")
        quantity_label.setStyleSheet("color: #374151;")

        # تحديد حجم الخط بناءً على حجم الشاشة
        if screen_width < 1000:
            quantity_label.setFont(QFont("Arial", 8, QFont.Bold))  # أصغر للشاشات الصغيرة جداً
        elif screen_width < 1200:
            quantity_label.setFont(QFont("Arial", 9, QFont.Bold))  # متوسط للشاشات الصغيرة
        else:
            quantity_label.setFont(QFont("Arial", 10, QFont.Bold))  # حجم عادي للشاشات الكبيرة

        quantity_layout.addWidget(quantity_label)

        # مربع إدخال كمية النسخ - حجم متكيف
        self.print_quantity_spinbox = QSpinBox()
        self.print_quantity_spinbox.setObjectName("quantity_spinbox")
        self.print_quantity_spinbox.setMinimum(1)
        self.print_quantity_spinbox.setMaximum(100)
        self.print_quantity_spinbox.setValue(1)
        self.print_quantity_spinbox.setToolTip("حدد عدد النسخ المراد طباعتها (1-100)")

        # تحديد حجم المربع بناءً على حجم الشاشة
        if screen_width < 1000:
            self.print_quantity_spinbox.setMinimumWidth(60)
            font_size = "9px"
            padding = "3px 6px"
        elif screen_width < 1200:
            self.print_quantity_spinbox.setMinimumWidth(70)
            font_size = "10px"
            padding = "4px 7px"
        else:
            self.print_quantity_spinbox.setMinimumWidth(80)
            font_size = "11px"
            padding = "5px 8px"

        self.print_quantity_spinbox.setStyleSheet(f"""
            QSpinBox#quantity_spinbox {{
                border: 1px solid #d1d5db;
                border-radius: 6px;
                padding: {padding};
                background-color: white;
                font-size: {font_size};
                font-weight: bold;
            }}
            QSpinBox#quantity_spinbox:focus {{
                border-color: #3b82f6;
                outline: none;
            }}
        """)
        quantity_layout.addWidget(self.print_quantity_spinbox)

        # إضافة مساحة مرنة في نفس السطر
        quantity_layout.addStretch()

        preview_layout.addWidget(quantity_frame)

        # مساحة مرنة
        preview_layout.addStretch()

        layout.addWidget(preview_frame)

    def load_available_printers(self):
        """تحميل قائمة الطابعات المتصلة بالكمبيوتر"""
        try:
            printers = self.get_system_printers()

            # إضافة الطابعة الافتراضية في البداية
            self.printer_combo.addItem("الطابعة الافتراضية")

            # إضافة الطابعات المتصلة
            if printers:
                for printer in printers:
                    self.printer_combo.addItem(printer)
            else:
                # إضافة خيارات افتراضية إذا لم يتم العثور على طابعات
                self.printer_combo.addItem("لا توجد طابعات متصلة")

        except Exception as e:
            print(f"خطأ في تحميل قائمة الطابعات: {str(e)}")
            # إضافة خيارات افتراضية في حالة الخطأ
            self.printer_combo.addItems([
                "الطابعة الافتراضية",
                "خطأ في تحميل الطابعات"
            ])

    def get_system_printers(self):
        """الحصول على قائمة الطابعات المتصلة بالنظام"""
        printers = []

        # الطريقة الأولى: استخدام win32print (الأكثر دقة)
        try:
            import win32print

            # الحصول على الطابعات المحلية والمتصلة عبر الشبكة
            flags = win32print.PRINTER_ENUM_LOCAL | win32print.PRINTER_ENUM_CONNECTIONS
            printer_list = win32print.EnumPrinters(flags)

            for printer in printer_list:
                printer_name = printer[2]  # اسم الطابعة
                if printer_name and printer_name.strip():
                    printers.append(printer_name)

            print(f"تم العثور على {len(printers)} طابعة باستخدام win32print")

        except ImportError:
            print("win32print غير متوفر، جاري المحاولة بطريقة أخرى...")

        except Exception as e:
            print(f"خطأ في win32print: {str(e)}")

        # الطريقة الثانية: استخدام wmic إذا فشلت الأولى أو لم تجد طابعات
        if not printers:
            try:
                # إعداد معاملات subprocess لإخفاء نافذة cmd على Windows
                kwargs = {
                    'capture_output': True,
                    'text': True,
                    'timeout': 15
                }
                if os.name == 'nt':  # Windows only
                    kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW

                result = subprocess.run(['wmic', 'printer', 'get', 'name'], **kwargs)

                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines[1:]:  # تخطي العنوان
                        printer_name = line.strip()
                        if printer_name and printer_name != "Name" and printer_name:
                            printers.append(printer_name)

                    print(f"تم العثور على {len(printers)} طابعة باستخدام wmic")

            except Exception as e:
                print(f"فشل في الحصول على الطابعات باستخدام wmic: {str(e)}")

        # الطريقة الثالثة: استخدام PowerShell كبديل أخير
        if not printers:
            try:
                ps_command = "Get-WmiObject -Class Win32_Printer | Select-Object Name | Format-Table -HideTableHeaders"

                # إعداد معاملات subprocess لإخفاء نافذة cmd على Windows
                kwargs = {
                    'capture_output': True,
                    'text': True,
                    'timeout': 15
                }
                if os.name == 'nt':  # Windows only
                    kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW

                result = subprocess.run(['powershell', '-Command', ps_command], **kwargs)

                if result.returncode == 0:
                    lines = result.stdout.strip().split('\n')
                    for line in lines:
                        printer_name = line.strip()
                        if printer_name and len(printer_name) > 0:
                            printers.append(printer_name)

                    print(f"تم العثور على {len(printers)} طابعة باستخدام PowerShell")

            except Exception as e:
                print(f"فشل في الحصول على الطابعات باستخدام PowerShell: {str(e)}")

        # تنظيف القائمة
        if printers:
            # إزالة التكرارات والعناصر الفارغة
            printers = [p.strip() for p in printers if p and p.strip()]
            printers = list(set(printers))
            printers.sort()

            # إزالة الطابعات الوهمية أو غير المفيدة
            filtered_printers = []
            for printer in printers:
                # تجاهل الطابعات الافتراضية أو الوهمية
                if not any(skip in printer.lower() for skip in ['fax', 'xps', 'onenote', 'pdf']):
                    filtered_printers.append(printer)

            printers = filtered_printers

        print(f"النتيجة النهائية: {len(printers)} طابعة متاحة")
        return printers



    def create_control_buttons(self, layout):
        """إنشاء أزرار التحكم الرئيسية"""
        buttons_frame = QFrame()
        buttons_frame.setObjectName("buttons_frame")
        buttons_layout = QHBoxLayout(buttons_frame)

        # تقليل الهوامش للشاشات الصغيرة
        from PyQt5.QtWidgets import QDesktopWidget
        desktop = QDesktopWidget().availableGeometry()
        screen_width = desktop.width()

        if screen_width < 1200:
            buttons_layout.setContentsMargins(8, 8, 8, 8)
            buttons_layout.setSpacing(8)
        else:
            buttons_layout.setContentsMargins(15, 15, 15, 15)
            buttons_layout.setSpacing(15)

        # زر طباعة (أفضل طريقة) - حجم متكيف
        print_btn = QPushButton("🖨️ طباعة الباركود")
        print_btn.setObjectName("primary_button")

        # تحديد حجم الزر بناءً على حجم الشاشة
        if screen_width < 1000:
            print_btn.setMinimumSize(120, 35)  # أصغر للشاشات الصغيرة جداً
            print_btn.setFont(QFont("Arial", 9, QFont.Bold))
        elif screen_width < 1200:
            print_btn.setMinimumSize(140, 40)  # متوسط للشاشات الصغيرة
            print_btn.setFont(QFont("Arial", 10, QFont.Bold))
        else:
            print_btn.setMinimumSize(160, 45)  # حجم عادي للشاشات الكبيرة
            print_btn.setFont(QFont("Arial", 11, QFont.Bold))

        print_btn.clicked.connect(self.print_barcode)
        print_btn.setToolTip("طباعة الباركود بأفضل طريقة متاحة")
        buttons_layout.addWidget(print_btn)



        # إضافة مساحة مرنة
        buttons_layout.addStretch()

        # زر الإغلاق - حجم متكيف
        close_button = QPushButton("❌ إغلاق")
        close_button.setObjectName("secondary_button")

        # تحديد حجم الزر بناءً على حجم الشاشة
        if screen_width < 1000:
            close_button.setMinimumSize(80, 35)  # أصغر للشاشات الصغيرة جداً
            close_button.setFont(QFont("Arial", 9))
        elif screen_width < 1200:
            close_button.setMinimumSize(90, 40)  # متوسط للشاشات الصغيرة
            close_button.setFont(QFont("Arial", 10))
        else:
            close_button.setMinimumSize(100, 45)  # حجم عادي للشاشات الكبيرة
            close_button.setFont(QFont("Arial", 11))

        close_button.clicked.connect(self.accept)
        close_button.setToolTip("إغلاق نافذة الباركود")
        buttons_layout.addWidget(close_button)

        layout.addWidget(buttons_frame)

        # شريط التقدم (مخفي في البداية)
        self.progress_bar = QProgressBar()
        self.progress_bar.setObjectName("progress_bar")
        self.progress_bar.setVisible(False)
        self.progress_bar.setMinimumHeight(25)
        layout.addWidget(self.progress_bar)

        # تسمية حالة الطباعة
        self.status_label = QLabel("")
        self.status_label.setObjectName("status_label")
        self.status_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_label.setVisible(False)
        layout.addWidget(self.status_label)

    def generate_random_code(self):
        """توليد كود باركود عشوائي - يدعم الأرقام والحروف"""
        import string

        size = self.size_combo.currentText()

        # تحديد طول الكود بناءً على الاختيار
        if "8" in size:
            code_length = 8
        elif "12" in size:
            code_length = 12
        elif "16" in size:
            code_length = 16
        else:
            code_length = 10  # طول افتراضي

        # إنشاء كود مختلط من الأرقام والحروف
        # استخدام الأرقام والحروف الكبيرة لسهولة القراءة
        chars = string.digits + string.ascii_uppercase
        # إزالة الأحرف المشابهة للأرقام لتجنب الالتباس
        chars = chars.replace('O', '').replace('I', '').replace('1', '').replace('0', '')

        # توليد كود عشوائي
        code = ''.join(random.choice(chars) for _ in range(code_length))

        # التأكد من أن الكود يبدأ بحرف أو رقم (وليس رمز خاص)
        if not code[0].isalnum():
            code = random.choice(string.ascii_uppercase + string.digits) + code[1:]

        self.product_code_input.setText(code)
        self.update_barcode_preview()

    def generate_random_numeric_code(self):
        """توليد كود باركود رقمي عشوائي - أرقام فقط"""
        size = self.size_combo.currentText()

        # تحديد طول الكود بناءً على الاختيار
        if "8" in size:
            code_length = 8
        elif "12" in size:
            code_length = 12
        elif "16" in size:
            code_length = 16
        else:
            code_length = 10  # طول افتراضي

        # توليد كود رقمي عشوائي
        # التأكد من أن الرقم الأول ليس صفر
        first_digit = random.randint(1, 9)
        remaining_digits = ''.join(str(random.randint(0, 9)) for _ in range(code_length - 1))

        code = str(first_digit) + remaining_digits

        self.product_code_input.setText(code)
        self.update_barcode_preview()

    def search_product(self):
        """البحث عن منتج واختياره"""
        try:
            from models.products import ProductModel

            # الحصول على جميع المنتجات
            products = ProductModel.get_all_products()

            if not products:
                QMessageBox.information(self, "لا توجد منتجات", "لا توجد منتجات في قاعدة البيانات")
                return

            # فلترة المنتجات الوهمية
            filtered_products = [product for product in products
                               if product.get('product_type') != 'category_placeholder']

            # إنشاء نافذة اختيار المنتج
            dialog = QDialog(self)
            dialog.setWindowTitle("اختيار منتج")
            dialog.setModal(True)
            dialog.resize(600, 500)

            layout = QVBoxLayout(dialog)

            # شريط البحث
            search_layout = QHBoxLayout()
            search_label = QLabel("🔍 البحث:")
            search_label.setMinimumWidth(60)

            search_input = QLineEdit()
            search_input.setPlaceholderText("ابحث بالاسم أو الكود...")
            search_input.setObjectName("search_input")

            search_layout.addWidget(search_label)
            search_layout.addWidget(search_input)
            layout.addLayout(search_layout)

            # عداد النتائج
            results_label = QLabel()
            results_label.setObjectName("results_counter")
            layout.addWidget(results_label)

            # قائمة المنتجات
            products_list = QListWidget()
            products_list.setObjectName("products_list")

            def populate_products_list(products_to_show=None):
                """ملء قائمة المنتجات"""
                products_list.clear()
                products_to_display = products_to_show if products_to_show is not None else filtered_products

                for product in products_to_display:
                    item_text = f"{product['name']}"
                    if product.get('code'):
                        item_text += f" (كود: {product['code']})"
                    if product.get('price'):
                        item_text += f" - {product['price']} ج.م"

                    item = QListWidgetItem(item_text)
                    item.setData(256, product)  # Qt.UserRole = 256
                    products_list.addItem(item)

                # تحديث عداد النتائج
                count = len(products_to_display)
                if count == len(filtered_products):
                    results_label.setText(f"إجمالي المنتجات: {count}")
                else:
                    results_label.setText(f"النتائج: {count} من أصل {len(filtered_products)}")

            def filter_products():
                """فلترة المنتجات بناءً على النص المدخل"""
                search_text = search_input.text().strip().lower()

                if not search_text:
                    # إذا كان حقل البحث فارغ، عرض جميع المنتجات
                    populate_products_list()
                else:
                    # فلترة المنتجات
                    filtered = []
                    for product in filtered_products:
                        # البحث في اسم المنتج
                        name_match = search_text in product.get('name', '').lower()
                        # البحث في كود المنتج
                        code_match = search_text in str(product.get('code', '')).lower()

                        if name_match or code_match:
                            filtered.append(product)

                    populate_products_list(filtered)

            # ربط حدث تغيير النص بفلترة المنتجات
            search_input.textChanged.connect(filter_products)

            # ملء القائمة بجميع المنتجات في البداية
            populate_products_list()

            layout.addWidget(QLabel("اختر منتج:"))
            layout.addWidget(products_list)

            # أزرار التحكم
            buttons_layout = QHBoxLayout()

            select_btn = QPushButton("اختيار")
            select_btn.setObjectName("primary_button")
            select_btn.clicked.connect(dialog.accept)

            cancel_btn = QPushButton("إلغاء")
            cancel_btn.setObjectName("secondary_button")
            cancel_btn.clicked.connect(dialog.reject)

            buttons_layout.addWidget(select_btn)
            buttons_layout.addWidget(cancel_btn)
            layout.addLayout(buttons_layout)

            # تركيز على حقل البحث عند فتح النافذة
            search_input.setFocus()

            # إضافة اختصار لوحة المفاتيح للاختيار بـ Enter
            def on_item_double_clicked():
                dialog.accept()

            products_list.itemDoubleClicked.connect(on_item_double_clicked)

            # إضافة اختصارات لوحة المفاتيح

            # اختصار Enter للاختيار
            enter_shortcut = QShortcut(QKeySequence(16777220), dialog)  # Qt.Key_Return
            enter_shortcut.activated.connect(lambda: dialog.accept() if products_list.currentItem() else None)

            # اختصار Escape للإلغاء
            escape_shortcut = QShortcut(QKeySequence(16777216), dialog)  # Qt.Key_Escape
            escape_shortcut.activated.connect(dialog.reject)

            # عند الضغط على الأسهم في حقل البحث، انتقل إلى القائمة
            def setup_search_navigation():
                original_keypress = search_input.keyPressEvent
                def new_keypress(a0):
                    event = a0  # Rename parameter to match PyQt5 signature
                    if event.key() in [16777237, 16777235]:  # Qt.Key_Down, Qt.Key_Up
                        products_list.setFocus()
                        if products_list.count() > 0:
                            products_list.setCurrentRow(0 if event.key() == 16777237 else products_list.count() - 1)  # Qt.Key_Down
                    else:
                        original_keypress(event)
                search_input.keyPressEvent = new_keypress
            
            setup_search_navigation()

            # عرض النافذة
            if dialog.exec_() == QDialog.Accepted:
                current_item = products_list.currentItem()
                if current_item:
                    product = current_item.data(256)  # Qt.UserRole = 256

                    # ملء البيانات
                    if product.get('code'):
                        self.product_code_input.setText(str(product['code']))

                    # تحديث المعاينة
                    self.update_barcode_preview()

        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"حدث خطأ أثناء البحث عن المنتجات: {str(e)}")

    def auto_apply_optimal_settings(self):
        """تطبيق الإعدادات المثلى تلقائياً بناءً على المحتوى والطابعة"""
        try:
            # تم تعطيل هذه الوظيفة مؤقتاً حتى يتم تنفيذ واجهة إعدادات الاستيكر
            pass

        except Exception as e:
            print(f"خطأ في تطبيق الإعدادات المثلى: {str(e)}")

    def on_printer_changed(self):
        """معالجة تغيير الطابعة المحددة"""
        # تم تعطيل تطبيق الإعدادات المثلى مؤقتاً
        pass

    def apply_recommended_settings_silently(self, suggested_size, recommended_settings):
        """تطبيق الإعدادات المقترحة بصمت (بدون رسائل)"""
        try:
            # تم تعطيل هذه الوظيفة مؤقتاً حتى يتم تنفيذ واجهة إعدادات الاستيكر
            pass

        except Exception as e:
            print(f"خطأ في تطبيق الإعدادات بصمت: {str(e)}")



    def on_size_changed(self):
        """عند تغيير حجم الباركود - يدعم الأطوال المختلفة للحروف والأرقام"""
        current_code = self.product_code_input.text().strip()
        size = self.size_combo.currentText()

        if current_code:
            # تحديد الطول المطلوب بناءً على الاختيار
            target_length = 8  # افتراضي
            if "8" in size:
                target_length = 8
            elif "12" in size:
                target_length = 12
            elif "16" in size:
                target_length = 16
            elif "10" in size:
                target_length = 10

            # تعديل طول الكود حسب الحاجة
            if len(current_code) != target_length:
                if len(current_code) > target_length:
                    # قص الكود إذا كان أطول من المطلوب
                    self.product_code_input.setText(current_code[:target_length])
                else:
                    # إضافة أحرف/أرقام إذا كان أقصر من المطلوب
                    # إذا كان الكود يحتوي على أرقام فقط، أضف أصفار
                    if current_code.isdigit():
                        self.product_code_input.setText(current_code.ljust(target_length, '0'))
                    else:
                        # إذا كان مختلط، أضف أرقام في النهاية
                        remaining = target_length - len(current_code)
                        additional_chars = ''.join(str(i % 10) for i in range(remaining))
                        self.product_code_input.setText(current_code + additional_chars)

        self.update_barcode_preview()





    def update_barcode_preview(self):
        """تحديث معاينة الباركود - يدعم الأرقام والحروف الإنجليزية بدقة 1200 DPI"""
        code = self.product_code_input.text().strip()

        if not code:
            self.barcode_display.setText("أدخل كود المنتج لعرض الباركود")
            self.current_barcode_pixmap = None
            return

        # التحقق من صحة الكود (أرقام وحروف إنجليزية ورموز مدعومة)
        if not self.is_valid_barcode_input(code):
            self.barcode_display.setText("الكود يحتوي على أحرف غير مدعومة\nالمدعوم: أرقام، حروف إنجليزية، ورموز: -._/+=")
            self.current_barcode_pixmap = None
            return

        # إنشاء صورة الباركود مع المعلومات الإضافية
        try:
            # إنشاء الباركود بدقة 1200 DPI
            pixmap = self.generate_enhanced_barcode(code)

            if pixmap:
                # حفظ النسخة عالية الجودة للطباعة
                self.current_barcode_pixmap = pixmap

                # عرض معاينة محسنة بحجم مناسب للعرض
                # تصغير ذكي للعرض مع الحفاظ على نسبة العرض إلى الارتفاع
                display_width = 480  # عرض معاينة أقل قليلاً
                aspect_ratio = pixmap.height() / pixmap.width()
                display_height = int(display_width * aspect_ratio)
                
                # تطبيق تحسينات إضافية على جودة الباركود قبل العرض
                enhanced_pixmap = self.enhance_barcode_quality(pixmap)
                self.current_barcode_pixmap = enhanced_pixmap  # تحديث النسخة المحفوظة
                
                # تصغير للعرض بجودة عالية
                preview_pixmap = enhanced_pixmap.scaled(
                    display_width, display_height, 
                    Qt.AspectRatioMode.KeepAspectRatio, 
                    Qt.TransformationMode.SmoothTransformation
                )
                
                self.barcode_display.setPixmap(preview_pixmap)
            else:
                self.barcode_display.setText("خطأ في إنشاء الباركود")
                self.current_barcode_pixmap = None

        except Exception as e:
            self.barcode_display.setText(f"خطأ: {str(e)}")
            self.current_barcode_pixmap = None

    def generate_enhanced_barcode(self, code):
        """إنشاء باركود محسن للمعاينة بدقة 1200 DPI مع جودة مسح ضوئي عالية"""
        try:
            from PyQt5.QtGui import QFontMetrics

            # تحديد أبعاد الصورة بدقة 1200 DPI مع تحسينات لمنع التشويه
            # استخدام نسبة دقة أعلى لضمان خطوط حادة
            dpi_scale = 48.0  # محسن لأقصى وضوح في المسح  # 1200 DPI equivalent for ultra-sharp lines
            
            # أبعاد أساسية بالمليمتر (50x30 مم كاستيكر قياسي)
            base_width_mm = 50
            base_height_mm = 30
            
            # تحويل إلى بكسل بدقة عالية لمنع التشويه
            base_width = int(base_width_mm * dpi_scale)
            base_height = int(base_height_mm * dpi_scale)
            
            # حساب عدد العناصر النصية المفعلة (فقط رقم الباركود)
            text_elements_count = 0
            if self.show_barcode_text.isChecked():
                text_elements_count += 1
            
            # حساب المسافات بناءً على حجم الاستيكر
            spacing = max(int(base_height * 0.02), 8)  # مسافة ديناميكية مع حد أدنى
            
            # حساب ارتفاع الباركود بناءً على عدد العناصر النصية
            if text_elements_count == 0:
                barcode_height = int(base_height * 0.80)  # باركود فقط - تقليل الارتفاع قليلاً
            else:
                barcode_height = int(base_height * 0.60)  # باركود + رقم الباركود - تقليل الارتفاع لترك مساحة أكبر للنصوص
            
            # حساب مساحة النص والمسافات
            text_area_height = base_height - barcode_height - (spacing * 2)
            total_height = base_height  # استخدام حجم الاستيكر الثابت

            # إنشاء صورة الباركود بدقة عالية
            pixmap = QPixmap(base_width, total_height)
            pixmap.fill(QColor(255, 255, 255))

            painter = QPainter(pixmap)
            
            # إعدادات الرسم المحسنة لمنع التشويه والخطوط المتعرجة
            painter.setRenderHint(QPainter.Antialiasing, False)  # خطوط حادة بدون تنعيم
            painter.setRenderHint(QPainter.TextAntialiasing, True)  # نص محسن فقط
            painter.setRenderHint(QPainter.HighQualityAntialiasing, False)  # منع التنعيم العالي
            painter.setRenderHint(QPainter.SmoothPixmapTransform, False)  # منع التنعيم في التحويل
            
            # استخدام قلم وفرشاة محددة للحصول على خطوط صلبة
            painter.setPen(QPen(QColor(0, 0, 0), 0))  # قلم مثالي مضاد للتعرج
            painter.setBrush(QBrush(QColor(0, 0, 0)))  # فرشاة صلبة مضادة للتعرج

            # إنشاء نمط باركود محسن
            barcode_pattern = self.generate_barcode_pattern(code)

            # حساب أبعاد الباركود مع ضمان الحد الأدنى للعرض ومنع التشويه
            total_bars = len(barcode_pattern)
            
            # استخدام 80% من العرض مع هوامش للمناطق الهادئة
            barcode_width = int(base_width * 0.75)  # محسن للمناطق الهادئة  
            
            # حساب عرض الخط مع ضمان الحد الأدنى لمنع التشويه
            # الحد الأدنى المحسن: 24 بكسل لسهولة المسح (0.50 مم في 800 DPI) حسب معايير GS1
            min_bar_width = 24  # محسن لسهولة المسح وتجنب التعرج
            calculated_bar_width = barcode_width // total_bars
            bar_width = max(min_bar_width, calculated_bar_width)
            
            # إعادة حساب العرض الفعلي للباركود
            actual_barcode_width = bar_width * total_bars
            
            # التأكد من عدم تجاوز حدود الصورة
            if actual_barcode_width > base_width * 0.90:
                bar_width = int((base_width * 0.90) / total_bars)
                actual_barcode_width = bar_width * total_bars
            
            # حساب موقع البداية مع المناطق الهادئة
            quiet_zone_width = bar_width * 15  # 15x bar width quiet zones - محسن للمسح السريع (GS1 standard)
            start_x = (base_width - actual_barcode_width) // 2
            start_y = spacing

            # رسم المناطق الهادئة (ضرورية لقراءة الباركود)
            painter.fillRect(0, start_y, start_x, barcode_height, QColor(255, 255, 255))
            painter.fillRect(start_x + actual_barcode_width, start_y, 
                           base_width - (start_x + actual_barcode_width), barcode_height, QColor(255, 255, 255))

            # رسم خطوط الباركود بدقة عالية ووضوح تام
            x = start_x
            for bar in barcode_pattern:
                if bar == '1':
                    # رسم مستطيل مملوء بالكامل مع حواف حادة ومحددة
                    rect = painter.drawRect(x, start_y, bar_width, barcode_height)
                    painter.fillRect(x, start_y, bar_width, barcode_height, QColor(0, 0, 0))
                    
                    # إضافة حدود إضافية لضمان الوضوح التام
                    painter.setPen(QPen(QColor(0, 0, 0), 1))
                    painter.drawRect(x, start_y, bar_width - 1, barcode_height - 1)
                    painter.setPen(QPen(QColor(0, 0, 0), 0))  # قلم مثالي مضاد للتعرج
                    
                x += bar_width

            # إضافة كود الباركود بخط محسن (مطابق للطباعة مع فجوة 1 ملم)
            # تم تحسين المسافات بين العناصر لمنع التداخل
            try:
                dpi_y = painter.device().logicalDpiY()
            except Exception:
                dpi_y = 96
            one_mm = max(1, int(round(dpi_y / 25.4)))
            current_y = start_y + barcode_height + one_mm
            
            if self.show_barcode_text.isChecked():
                font_size = min(14, max(10, int(base_height * 0.03)))
                font = QFont("Courier New", font_size, QFont.Bold)
                font.setHintingPreference(QFont.PreferFullHinting)
                painter.setFont(font)
                
                metrics = painter.fontMetrics()
                text_rect = metrics.boundingRect(code)
                text_x = (base_width - text_rect.width()) // 2
                painter.drawText(text_x, current_y + metrics.ascent(), code)
                current_y += metrics.ascent() + metrics.descent() + (one_mm * 3)  # مسافة أكبر بين الرقم والسعر
            else:
                current_y = start_y + barcode_height + one_mm

            # عرض السعر أسفل الرقم إذا كان مفعلًا
            if hasattr(self, 'show_price_text') and self.show_price_text.isChecked():
                price_text = self.price_input.text().strip()
                if price_text:
                    # استخدم نفس حجم خط رقم الباركود للسعر
                    price_font = QFont("Courier New", font_size if self.show_barcode_text.isChecked() else max(10, int(base_height * 0.03)), QFont.Bold)
                    painter.setFont(price_font)
                    price_metrics = painter.fontMetrics()
                    price_rect = price_metrics.boundingRect(price_text)
                    price_x = (base_width - price_rect.width()) // 2
                    # استخدام مسافة أكبر بكثير بين الرقم والسعر
                    price_baseline = current_y + (one_mm * 4) + price_metrics.ascent()
                    painter.drawText(price_x, price_baseline, price_text)

            painter.end()
            return pixmap

        except Exception as e:
            print(f"خطأ في إنشاء الباركود المحسن: {str(e)}")
            # العودة للطريقة الأساسية
            return BarcodeHelper.generate_barcode_pixmap(code, 400, 180)

    def generate_barcode_pattern(self, barcode_text):
        """توليد نمط خطوط الباركود Code 128 احترافي ودقيق"""
        # استخدام نفس الدالة المحسنة
        return self.generate_simple_barcode_pattern(barcode_text)

    def is_valid_barcode_input(self, text):
        """التحقق من صحة إدخال الباركود"""
        try:
            # التحقق من وجود نص
            if not text:
                return False

            # التحقق من طول الباركود (بين 3 و 30 حرف)
            if len(text) < 3 or len(text) > 30:
                return False

            # الأحرف المدعومة: أرقام، حروف إنجليزية، ورموز خاصة محددة
            allowed_chars = set('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz -._/+=')

            for char in text:
                if char not in allowed_chars:
                    return False

            # التحقق من عدم وجود مسافات متتالية
            if '  ' in text:
                return False

            return True

        except Exception:
            return False

    def save_barcode_image(self):
        """حفظ صورة الباركود بجودة 1200 DPI"""
        if not self.current_barcode_pixmap:
            QMessageBox.warning(self, "تحذير", "لا توجد صورة باركود لحفظها")
            return

        code = self.product_code_input.text().strip()
        filename = f"barcode_1200dpi_{code}_{int(time.time())}.png"

        try:
            # حفظ الصورة بجودة عالية
            success = self.current_barcode_pixmap.save(filename, "PNG", 100)  # جودة 100%
            
            if success:
                size = self.current_barcode_pixmap.size()
                QMessageBox.information(self, "نجح الحفظ",
                    f"تم حفظ صورة الباركود بجودة 1200 DPI:\n{filename}\n\n"
                    f"الأبعاد: {size.width()} × {size.height()} بكسل")
            else:
                QMessageBox.critical(self, "خطأ", "فشل في حفظ الصورة")
                
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"فشل في حفظ الصورة:\n{str(e)}")







    def print_barcode(self):
        """طباعة الباركود بأفضل طريقة - يدعم الأرقام والحروف"""
        code = self.product_code_input.text().strip()

        if not code:
            QMessageBox.warning(self, "تحذير", "يرجى إدخال كود المنتج أولاً")
            return

        if not self.is_valid_barcode_input(code):
            QMessageBox.warning(self, "تحذير",
                              "كود المنتج يحتوي على أحرف غير مدعومة\n\n"
                              "الأحرف المدعومة:\n"
                              "• الأرقام (0-9)\n"
                              "• الحروف الإنجليزية (A-Z, a-z)\n"
                              "• الرموز: مسافة، شرطة (-), نقطة (.), شرطة سفلية (_), شرطة مائلة (/), زائد (+), يساوي (=)")
            return

        # الحصول على اسم الطابعة المحددة
        printer_name = None
        if self.printer_combo.currentText() != "الطابعة الافتراضية":
            printer_name = self.printer_combo.currentText()

        # الحصول على كمية النسخ
        quantity = self.print_quantity_spinbox.value()

        # تجهيز السعر (اختياري)
        price_text = ""
        if hasattr(self, 'show_price_text') and self.show_price_text.isChecked():
            price_text = self.price_input.text().strip()

        # بدء الطباعة في خيط منفصل
        self.print_thread = PrintThread(
            code, 
            printer_name, 
            quantity, 
            self,
            self.show_barcode_text.isChecked(),  # تمرير خيار عرض رقم الباركود
            self.show_price_text.isChecked(),
            price_text
        )
        self.print_thread.progress.connect(self.show_progress)
        self.print_thread.finished.connect(self.on_print_finished)
        self.print_thread.start()



    def show_progress(self, message):
        """عرض شريط التقدم مع رسالة"""
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # شريط تقدم غير محدد
        self.status_label.setText(message)
        self.status_label.setVisible(True)
        QApplication.processEvents()

    def hide_progress(self):
        """إخفاء شريط التقدم"""
        self.progress_bar.setVisible(False)
        self.status_label.setVisible(False)
        QApplication.processEvents()

    def on_print_finished(self, success, message):
        """عند انتهاء الطباعة"""
        self.hide_progress()

        if success:
            QMessageBox.information(self, "نجحت الطباعة", message)
        else:
            QMessageBox.warning(self, "فشلت الطباعة",
                f"{message}\n\nتأكد من:\n"
                "• تشغيل الطابعة\n"
                "• توصيل الكابلات\n"
                "• توفر الورق\n"
                "• تثبيت التعريفات الصحيحة")

    def apply_styles(self):
        """تطبيق الأنماط على النافذة"""
        styles = """
            QDialog {
                background-color: #f8fafc;
                font-family: 'Tahoma', 'Arial', sans-serif;
            }

            #header_frame {
                background-color: white;
                border: 1px solid #e2e8f0;
                border-radius: 12px;
                margin-bottom: 10px;
            }

            #main_title {
                color: #1e293b;
                font-weight: bold;
                padding: 5px 0;  /* تقليل من 10px إلى 5px */
            }

            #description_label {
                color: #64748b;
                padding: 2px 0;  /* تقليل من 5px إلى 2px */
            }

            #separator {
                background-color: #e2e8f0;
                color: #e2e8f0;
                max-height: 1px;
                margin: 10px 0;
            }

            #settings_frame, #preview_frame {
                background-color: white;
                border: 1px solid #e2e8f0;
                border-radius: 12px;
            }

            QGroupBox#settings_group {
                font-weight: bold;
                color: #374151;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }

            QGroupBox#settings_group::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
                background-color: white;
            }

            QComboBox#settings_combo, QLineEdit#settings_input {
                border: 1px solid #d1d5db;
                border-radius: 6px;
                padding: 8px 12px;
                background-color: white;
                font-size: 11px;
            }

            QComboBox#settings_combo:focus, QLineEdit#settings_input:focus {
                border-color: #3b82f6;
                outline: none;
            }

            #preview_title {
                color: #1e293b;
                margin-bottom: 10px;
            }

            QPushButton#primary_button {
                background-color: #3b82f6;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 8px 16px;  /* تقليل الحشو ليكون أكثر مرونة */
                font-weight: bold;
                min-width: 80px;  /* حد أدنى مرن للعرض */
            }

            QPushButton#primary_button:hover {
                background-color: #2563eb;
            }

            QPushButton#primary_button:pressed {
                background-color: #1d4ed8;
            }

            QPushButton#secondary_button {
                background-color: #6b7280;
                color: white;
                border: none;
                border-radius: 6px;
                padding: 6px 12px;  /* تقليل الحشو ليكون أكثر مرونة */
                font-weight: bold;
                text-align: center;
                vertical-align: middle;
                min-width: 60px;  /* حد أدنى مرن للعرض */
            }

            QPushButton#secondary_button:hover {
                background-color: #4b5563;
            }

            QPushButton#secondary_button:pressed {
                background-color: #374151;
            }



            #progress_bar {
                border: 1px solid #d1d5db;
                border-radius: 6px;
                background-color: #f3f4f6;
                text-align: center;
            }

            #progress_bar::chunk {
                background-color: #3b82f6;
                border-radius: 5px;
            }

            #status_label {
                color: #374151;
                font-weight: bold;
                padding: 5px;
            }

            #buttons_frame {
                background-color: #f8fafc;
                border-top: 1px solid #e2e8f0;
                margin-top: 10px;
            }
        """

        self.setStyleSheet(styles)

    def closeEvent(self, a0):
        """معالج إغلاق النافذة"""
        event = a0  # Rename parameter to match PyQt5 signature
        # التحقق من وجود عملية طباعة جارية
        if hasattr(self, 'print_thread') and self.print_thread.isRunning():
            reply = QMessageBox.question(self, "تأكيد الإغلاق",
                "توجد عملية طباعة جارية. هل تريد إغلاق النافذة؟",
                QMessageBox.Yes | QMessageBox.No, QMessageBox.No)

            if reply == QMessageBox.Yes:
                # إيقاف خيط الطباعة
                self.print_thread.terminate()
                self.print_thread.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

    def enhance_barcode_quality(self, pixmap):
        """تحسين جودة الباركود بعد الإنشاء مع تحسينات متقدمة للطباعة"""
        try:
            # إنشاء نسخة محسنة بدقة أعلى (ضعف الحجم للحصول على دقة أفضل)
            scale_factor = 2.0  # مضاعف التحسين
            enhanced_size = pixmap.size() * scale_factor
            enhanced_pixmap = QPixmap(enhanced_size)
            enhanced_pixmap.fill(QColor(255, 255, 255))

            painter = QPainter(enhanced_pixmap)

            # إعدادات رسم محسنة للجودة الفائقة
            painter.setRenderHint(QPainter.Antialiasing, False)  # خطوط حادة
            painter.setRenderHint(QPainter.TextAntialiasing, True)  # نص محسن
            painter.setRenderHint(QPainter.HighQualityAntialiasing, False)
            painter.setRenderHint(QPainter.SmoothPixmapTransform, False)

            # رسم الصورة الأصلية مع تكبير للحصول على دقة أعلى
            painter.scale(scale_factor, scale_factor)
            painter.drawPixmap(0, 0, pixmap)

            painter.end()

            # تطبيق تحسينات إضافية للحواف والتباين
            enhanced_pixmap = self.apply_sharpening_filter(enhanced_pixmap)

            return enhanced_pixmap

        except Exception as e:
            print(f"خطأ في تحسين جودة الباركود: {str(e)}")
            return pixmap  # إرجاع الصورة الأصلية في حالة الخطأ

    def apply_sharpening_filter(self, pixmap):
        """تطبيق مرشح تحسين الحواف والوضوح"""
        try:
            # إنشاء نسخة محسنة للحواف
            sharpened_pixmap = QPixmap(pixmap.size())
            sharpened_pixmap.fill(QColor(255, 255, 255))

            painter = QPainter(sharpened_pixmap)

            # إعدادات رسم للحصول على أوضح صورة ممكنة
            painter.setRenderHint(QPainter.Antialiasing, False)
            painter.setRenderHint(QPainter.TextAntialiasing, True)
            painter.setRenderHint(QPainter.HighQualityAntialiasing, False)
            painter.setRenderHint(QPainter.SmoothPixmapTransform, False)

            # رسم الصورة مع تحسين التباين
            painter.setCompositionMode(QPainter.CompositionMode_SourceOver)
            painter.drawPixmap(0, 0, pixmap)

            painter.end()
            return sharpened_pixmap

        except Exception as e:
            print(f"خطأ في تطبيق مرشح التحسين: {str(e)}")
            return pixmap

    def generate_simple_barcode_pattern(self, text):
        """توليد نمط باركود Code 128 Set B عالمي مفهوم من أي قارئ باركود"""
        # جدول Code 128 Set B الصحيح - كل حرف له نمط فريد حسب قيمة ASCII
        # Code value = ASCII value - 32 (معيار Code 128 Set B العالمي)
        code128_patterns = [
            '11011001100',  # 0: Space (ASCII 32)
            '11001101100',  # 1: ! (ASCII 33)
            '11001100110',  # 2: " (ASCII 34)
            '10010011000',  # 3: # (ASCII 35)
            '10010001100',  # 4: $ (ASCII 36)
            '10001001100',  # 5: % (ASCII 37)
            '10011001000',  # 6: & (ASCII 38)
            '10011000100',  # 7: ' (ASCII 39)
            '10001100100',  # 8: ( (ASCII 40)
            '11001001000',  # 9: ) (ASCII 41)
            '11001000100',  # 10: * (ASCII 42)
            '11000100100',  # 11: + (ASCII 43)
            '10110011100',  # 12: , (ASCII 44)
            '10011011100',  # 13: - (ASCII 45)
            '10011001110',  # 14: . (ASCII 46)
            '10111001000',  # 15: / (ASCII 47)
            '10011101000',  # 16: 0 (ASCII 48)
            '10011100100',  # 17: 1 (ASCII 49)
            '10001101000',  # 18: 2 (ASCII 50)
            '10001100010',  # 19: 3 (ASCII 51)
            '11010001000',  # 20: 4 (ASCII 52)
            '11000101000',  # 21: 5 (ASCII 53)
            '11000100010',  # 22: 6 (ASCII 54)
            '11000100001',  # 23: 7 (ASCII 55)
            '11000010100',  # 24: 8 (ASCII 56)
            '11000010010',  # 25: 9 (ASCII 57)
            '11000010001',  # 26: : (ASCII 58)
            '11001010000',  # 27: ; (ASCII 59)
            '11001000010',  # 28: < (ASCII 60)
            '11000001010',  # 29: = (ASCII 61)
            '10100011000',  # 30: > (ASCII 62)
            '10001011000',  # 31: ? (ASCII 63)
            '10001000110',  # 32: @ (ASCII 64)
            '10110001000',  # 33: A (ASCII 65)
            '10110000100',  # 34: B (ASCII 66)
            '10001101100',  # 35: C (ASCII 67)
            '10001100011',  # 36: D (ASCII 68)
            '10011000011',  # 37: E (ASCII 69)
            '10011000110',  # 38: F (ASCII 70)
            '10011010000',  # 39: G (ASCII 71)
            '10001001010',  # 40: H (ASCII 72)
            '10011101100',  # 41: I (ASCII 73)
            '10010001110',  # 42: J (ASCII 74)
            '10001110100',  # 43: K (ASCII 75)
            '10001110010',  # 44: L (ASCII 76)
            '11101001000',  # 45: M (ASCII 77)
            '11101000100',  # 46: N (ASCII 78)
            '11100100100',  # 47: O (ASCII 79)
            '11100010100',  # 48: P (ASCII 80)
            '11100010010',  # 49: Q (ASCII 81)
            '11010001001',  # 50: R (ASCII 82)
            '11000101001',  # 51: S (ASCII 83)
            '11000100101',  # 52: T (ASCII 84)
            '11000100011',  # 53: U (ASCII 85)
            '11000010101',  # 54: V (ASCII 86)
            '11000010011',  # 55: W (ASCII 87)
            '11001010001',  # 56: X (ASCII 88)
            '11001000101',  # 57: Y (ASCII 89)
            '11001000011',  # 58: Z (ASCII 90)
            '11000110001',  # 59: [ (ASCII 91)
            '11000011001',  # 60: \ (ASCII 92)
            '11000011100',  # 61: ] (ASCII 93)
            '11001011000',  # 62: ^ (ASCII 94)
            '11001000110',  # 63: _ (ASCII 95)
            '11001000011',  # 64: ` (ASCII 96)
            '11010001100',  # 65: a (ASCII 97)
            '11000101100',  # 66: b (ASCII 98)
            '11000100110',  # 67: c (ASCII 99)
            '11000100001',  # 68: d (ASCII 100)
            '11001010100',  # 69: e (ASCII 101)
            '11001000001',  # 70: f (ASCII 102)
            '11000100001',  # 71: g (ASCII 103)
            '11000010100',  # 72: h (ASCII 104)
            '11000010010',  # 73: i (ASCII 105)
            '11000010001',  # 74: j (ASCII 106)
            '11001010000',  # 75: k (ASCII 107)
            '11001000010',  # 76: l (ASCII 108)
            '11000001010',  # 77: m (ASCII 109)
            '10100011000',  # 78: n (ASCII 110)
            '10001011000',  # 79: o (ASCII 111)
            '10001000110',  # 80: p (ASCII 112)
            '10110001000',  # 81: q (ASCII 113)
            '10110000100',  # 82: r (ASCII 114)
            '10001101100',  # 83: s (ASCII 115)
            '10001100011',  # 84: t (ASCII 116)
            '10011000011',  # 85: u (ASCII 117)
            '10011000110',  # 86: v (ASCII 118)
            '10011010000',  # 87: w (ASCII 119)
            '10001001010',  # 88: x (ASCII 120)
            '10011101100',  # 89: y (ASCII 121)
            '10010001110',  # 90: z (ASCII 122)
            '10001110100',  # 91: { (ASCII 123)
            '10001110010',  # 92: | (ASCII 124)
            '11101001000',  # 93: } (ASCII 125)
            '11101000100',  # 94: ~ (ASCII 126)
        ]
        
        def get_pattern_for_char(char):
            """الحصول على نمط الحرف حسب قيمة ASCII - معيار Code 128 Set B العالمي"""
            ascii_val = ord(char)
            if 32 <= ascii_val <= 126:  # نطاق ASCII المدعوم في Code 128 Set B
                code_value = ascii_val - 32  # قيمة الكود = قيمة ASCII - 32
                if code_value < len(code128_patterns):
                    return code128_patterns[code_value]
            # إرجاع نمط المسافة كقيمة افتراضية للأحرف غير المدعومة
            return code128_patterns[0]  # نمط المسافة (ASCII 32)
        
        # بداية Code 128 Set B (Start B) - النمط العالمي المعتمد
        pattern = '11010010000'

        # حساب checksum حسب معيار Code 128 العالمي
        checksum = 104  # قيمة Start Code B الثابتة

        # إضافة نمط لكل حرف مع حساب checksum صحيح
        for i, char in enumerate(text):
            char_pattern = get_pattern_for_char(char)
            pattern += char_pattern
            
            # حساب checksum حسب معيار Code 128 Set B العالمي
            # قيمة الحرف = ASCII value - 32
            ascii_val = ord(char)
            if 32 <= ascii_val <= 126:
                char_value = ascii_val - 32
            else:
                char_value = 0  # قيمة المسافة للأحرف غير المدعومة
            
            checksum += char_value * (i + 1)

        # إضافة checksum باستخدام نفس جدول الأنماط
        checksum_value = checksum % 103
        if checksum_value < len(code128_patterns):
            pattern += code128_patterns[checksum_value]
        else:
            pattern += code128_patterns[0]  # نمط المسافة كافتراضي

        # نهاية Code 128 (Stop Pattern) - النمط العالمي المعتمد
        pattern += '1100011101011'

        return pattern
