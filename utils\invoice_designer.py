#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
مصمم الفواتير - يوفر معاينة وتصميم الفواتير
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QTextEdit, QFrame, QScrollArea,
                             QWidget, QGridLayout, QSpacerItem, QSizePolicy)
from PyQt5.QtCore import Qt, QSize, QSizeF, QSettings
from PyQt5.QtGui import QFont, QPixmap, QPainter, QTextDocument
from PyQt5.QtPrintSupport import QPrintPreviewDialog, QPrintDialog, QPrinter
import datetime
import re
import os
from PyQt5.QtCore import QSizeF
from PyQt5.QtGui import QTextDocument
from PyQt5.QtPrintSupport import QPrinter, QPrintPreviewDialog, QPrinterInfo


class TextWrapper:
    """فئة لتقسيم النصوص الطويلة بذكاء"""
    
    def __init__(self, max_width_chars, max_lines=3):
        """
        تهيئة TextWrapper
        
        Args:
            max_width_chars (int): عدد الأحرف الأقصى في السطر الواحد
            max_lines (int): عدد الأسطر الأقصى المسموح
        """
        self.max_width_chars = max_width_chars
        self.max_lines = max_lines
    
    def wrap_text(self, text):
        """
        تقسيم النص إلى أسطر متعددة
        
        Args:
            text (str): النص المراد تقسيمه
            
        Returns:
            list: قائمة بالأسطر المقسمة
        """
        try:
            if not text or not isinstance(text, str):
                return [""]
            
            text = text.strip()
            if len(text) <= self.max_width_chars:
                return [text]
            
            lines = []
            remaining_text = text
            
            # تقسيم النص إلى أسطر بناءً على الحد الأقصى للعرض
            while remaining_text and len(lines) < self.max_lines:
                # إذا كان النص المتبقي أقل من الحد الأقصى، أضفه كسطر كامل
                if len(remaining_text) <= self.max_width_chars:
                    lines.append(remaining_text)
                    break
                
                # البحث عن نقطة انقطاع مناسبة (مسافة أو علامة ترقيم)
                cut_point = min(self.max_width_chars, len(remaining_text) - 1)
                while cut_point > 0 and remaining_text[cut_point] not in ' .,;:-':
                    cut_point -= 1
                
                # إذا لم نجد نقطة انقطاع مناسبة، نقطع عند الحد الأقصى
                if cut_point == 0:
                    cut_point = min(self.max_width_chars, len(remaining_text))
                
                # إضافة السطر وتحديث النص المتبقي
                lines.append(remaining_text[:cut_point].strip())
                remaining_text = remaining_text[cut_point:].strip()
            
            # إذا كان هناك نص متبقي ووصلنا للحد الأقصى من الأسطر، نضيف علامة القطع
            if remaining_text and len(lines) >= self.max_lines:
                last_line = lines[-1]
                if len(last_line) > 3:  # تأكد من أن السطر طويل بما يكفي
                    lines[-1] = last_line[:-3] + "..."
            
            return lines
        except Exception as e:
            print(f"خطأ في تقسيم النص: {str(e)}")
            return [text if isinstance(text, str) else ""]


class InvoiceDesigner:
    """مصمم الفواتير - يوفر معاينة وتصميم الفواتير"""
    
    def __init__(self, company_info=None, paper_size="80mm"):
        """
        تهيئة مصمم الفواتير
        
        Args:
            company_info (dict, optional): معلومات الشركة. Defaults to None.
            paper_size (str, optional): حجم الورق. Defaults to "80mm".
        """
        self.company_info = company_info or {
            "name": "اسم الشركة",
            "phone": "هاتف: 0123456789",
            "address": "العنوان: الشارع، المدينة"
        }
        self.paper_size = paper_size
        self.invoice_notes = ""
        
    def _generate_invoice_html_safe(self, invoice_data=None, items_data=None):
        """
        توليد HTML للفاتورة بطريقة آمنة
        
        Args:
            invoice_data (dict, optional): بيانات الفاتورة. Defaults to None.
            items_data (list, optional): قائمة بعناصر الفاتورة. Defaults to None.
            
        Returns:
            str: HTML للفاتورة
        """
        # التحقق من صحة البيانات
        if invoice_data is None:
            invoice_data = {
                "invoice_number": "INV-001",
                "date": datetime.datetime.now().strftime("%Y/%m/%d %H:%M"),
                "customer_name": "عميل نقدي",
                "total": 0.0,
                "discount": 0.0,
                "final_total": 0.0,
                "paid": 0.0,
                "remaining": 0.0
            }
        
        if items_data is None:
            items_data = []
            
        try:
            # التحقق من صحة البيانات
            if invoice_data is not None and not isinstance(invoice_data, dict):
                print("خطأ: بيانات الفاتورة غير صالحة")
                invoice_data = {
                    "invoice_number": "INV-001",
                    "date": datetime.datetime.now().strftime("%Y/%m/%d %H:%M"),
                    "customer_name": "عميل نقدي",
                    "total": 0.0,
                    "discount": 0.0,
                    "final_total": 0.0,
                    "paid": 0.0,
                    "remaining": 0.0
                }
            
            if items_data is not None and not isinstance(items_data, list):
                print("خطأ: بيانات العناصر غير صالحة")
                items_data = []
                
            return self._generate_invoice_html_safe(invoice_data, items_data)
        except Exception as e:
            print(f"خطأ في توليد HTML للفاتورة: {str(e)}")
            # إرجاع HTML بسيط في حالة حدوث خطأ
            return f"""
            <html dir="rtl">
            <body>
                <h1 style="text-align: center;">خطأ في توليد الفاتورة</h1>
                <p style="text-align: center;">{str(e)}</p>
            </body>
            </html>
            """
    
    def _generate_invoice_html_safe(self, invoice_data=None, items_data=None):
        """الدالة الداخلية الآمنة لتوليد HTML للفاتورة"""
        
        try:
            # استخدام بيانات تجريبية إذا لم يتم توفير بيانات
            if not invoice_data:
                invoice_data = {
                    "invoice_number": "INV-001",
                    "date": datetime.datetime.now().strftime("%Y/%m/%d %H:%M"),
                    "customer_name": "عميل نقدي",
                    "total": 100.0,
                    "discount": 0.0,
                    "final_total": 100.0,
                    "paid": 100.0,
                    "remaining": 0.0
                }
            
            if not items_data:
                items_data = [
                    {
                        "product_name": "منتج تجريبي",
                        "price": 50.0,
                        "quantity": 2,
                        "total": 100.0
                    }
                ]
                
            # تعيين خيار التوسيط للفاتورة
            from utils.settings_manager import SettingsManager
            settings_manager = SettingsManager()
            settings_manager.set_setting("invoice_design", "center_content", True)
            
            # تحديد إعدادات الصفحة حسب حجم الورق
            if self.paper_size == "58mm":
                paper_settings = {
                    "page_size": "58mm 200mm",
                    "margin": "5mm",
                    "page_width": "58mm",
                    "font_family": "'Readex Pro', 'Tahoma', sans-serif",
                    "font_size": "8px",
                    "header_font": "10px",
                    "table_font": "7px",
                    "body_padding": "2px",
                    "line_height": "1.2"
                }
                max_product_name_chars = 12
                max_product_name_lines = 2
            elif self.paper_size == "A4":
                paper_settings = {
                    "page_size": "A4",
                    "margin": "10mm",
                    "page_width": "210mm",
                    "font_family": "'Readex Pro', 'Tahoma', sans-serif",
                    "font_size": "14px",
                    "header_font": "20px",
                    "table_font": "12px",
                    "body_padding": "10px",
                    "line_height": "1.5"
                }
                max_product_name_chars = 40
                max_product_name_lines = 3
            else:  # 80mm
                paper_settings = {
                    "page_size": "80mm 200mm",
                    "margin": "5mm",
                    "page_width": "80mm",
                    "font_family": "'Readex Pro', 'Tahoma', sans-serif",
                    "font_size": "9px",
                    "header_font": "11px",
                    "table_font": "8px",
                    "body_padding": "3px",
                    "line_height": "1.3"
                }
                max_product_name_chars = 20
                max_product_name_lines = 2
            
            # إعدادات المسافات
            header_spacing = "10px"
            info_spacing = "6px"
            total_spacing = "8px"
            
            # تنسيق أسماء المنتجات
            text_wrapper = TextWrapper(max_product_name_chars, max_product_name_lines)
            
            # بناء صفوف الجدول
            table_rows = ""
            for item in items_data:
                product_name = item.get("product_name", "")
                price = item.get("price", 0)
                quantity = item.get("quantity", 0)
                total = item.get("total", 0)
                
                # تقسيم اسم المنتج إلى أسطر متعددة إذا كان طويلاً
                if not isinstance(product_name, str):
                    product_name = str(product_name) if product_name is not None else ""
                
                try:
                    text_wrapper = TextWrapper(max_product_name_chars, max_product_name_lines)
                    product_lines = text_wrapper.wrap_text(product_name)
                    product_html = "<br>".join(product_lines)
                except Exception as e:
                    print(f"خطأ في تنسيق اسم المنتج: {str(e)}")
                    product_html = product_name
                
                # إضافة صف الجدول
                table_rows += f"""
                <tr>
                    <td class="product-name-cell">{product_html}</td>
                    <td>{price}</td>
                    <td>{quantity}</td>
                    <td>{total}</td>
                </tr>
                """
            
            # بناء HTML للفاتورة
            html = f"""
            <!DOCTYPE html>
            <html dir="rtl">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>فاتورة</title>
                <style>
                    @page {{
                        size: {paper_settings['page_size']};
                        margin: {paper_settings['margin']};
                        text-align: center;
                    }}
                    body {{
                        font-family: {paper_settings['font_family']};
                        font-size: {paper_settings['font_size']};
                        margin: 0 auto !important;
                        padding: {paper_settings['body_padding']};
                        direction: rtl;
                        width: {paper_settings['page_width']};
                        max-width: 100%;
                        text-align: center !important;
                    }}
                    .header {{
                        text-align: center !important;
                        margin: 0 auto !important;
                        padding: {header_spacing};
                        width: 100%;
                        margin-bottom: 15px !important;
                    }}
                    .company-name {
                        text-align: center !important;
                        margin: 0 auto !important;
                        margin-bottom: 8px !important;
                        font-weight: bold;
                        font-size: {paper_settings['header_font']};
                    }
                    .company-info {
                        text-align: center !important;
                        margin: 0 auto !important;
                        margin-bottom: 5px !important;
                        font-size: {paper_settings['font_size']};
                        line-height: 1.5;
                    }
                    .invoice-info {
                        margin: {header_spacing} auto !important;
                        padding: {info_spacing};
                        text-align: center !important;
                        margin-bottom: 15px !important;
                    }
                    .invoice-info div {
                        margin: {info_spacing} auto !important;
                        padding: {info_spacing};
                        text-align: center !important;
                        margin-bottom: 8px !important;
                        line-height: 1.5;
                    }
                    table {{
                        width: 100%;
                        border-collapse: collapse;
                        margin: {header_spacing} auto !important;
                        text-align: center !important;
                    }}
                    th, td {{
                        border: 1px solid #000;
                        padding: {info_spacing};
                        text-align: center !important;
                        font-size: {paper_settings['table_font']};
                    }}
                    .product-name-cell {{
                        text-align: center !important;
                        word-break: break-word;
                        white-space: normal;
                        vertical-align: top;
                        line-height: {paper_settings['line_height']};
                    }}
                    .product-line {{
                        margin: 0;
                        padding: 0;
                        line-height: {paper_settings['line_height']};
                        font-size: {paper_settings['font_size']};
                    }}
                    .total-section {{
                        margin-top: {total_spacing};
                        text-align: center !important;
                    }}
                    .total-box {{
                        border: 1px solid #000;
                        padding: {total_spacing};
                        margin: {total_spacing} auto !important;
                        width: 85%;
                        font-weight: bold;
                        font-size: {paper_settings['header_font']};
                        line-height: 1.3;
                        min-height: {total_spacing};
                        text-align: center !important;
                    }}
                    .notes {{
                        margin-top: {total_spacing};
                        text-align: center !important;
                    }}
                </style>
            </head>
            <body>
                <div class="header">
                    <center>
                        <div class="company-name">{self.company_info.get('name', '')}</div>
                        <div class="company-info">{self.company_info.get('phone', '')}</div>
                        <div class="company-info">{self.company_info.get('address', '')}</div>
                    </center>
                </div>
                
                <div class="invoice-info">
                    <center>
                        <div>رقم الفاتورة: {invoice_data.get('invoice_number', '')}</div>
                        <div>التاريخ: {invoice_data.get('date', '')}</div>
                        <div>العميل: {invoice_data.get('customer_name', '')}</div>
                    </center>
                </div>
                
                <center>
                    <table>
                        <tr>
                            <th>المنتج</th>
                            <th>السعر</th>
                            <th>الكمية</th>
                            <th>الإجمالي</th>
                        </tr>
                        {table_rows}
                    </table>
                </center>
                
                <div class="total-section">
                    <center>
                        <div class="total-box">
                            الإجمالي: {invoice_data.get('total', 0)}<br>
                            الخصم: {invoice_data.get('discount', 0)}<br>
                            المجموع النهائي: {invoice_data.get('final_total', 0)}<br>
                            المدفوع: {invoice_data.get('paid', 0)}<br>
                            المتبقي: {invoice_data.get('remaining', 0)}
                        </div>
                    </center>
                </div>
                
                <div class="notes">
                    <center>{self.invoice_notes}</center>
                </div>
            </body>
            </html>
            """
            
            return html
            
        except Exception as e:
            print(f"خطأ في توليد HTML للفاتورة: {str(e)}")
            # إرجاع HTML بسيط في حالة حدوث خطأ
            return f"""
            <!DOCTYPE html>
            <html dir="rtl">
            <head>
                <meta charset="UTF-8">
                <title>فاتورة</title>
                <style>
                    body {{ text-align: center; font-family: 'Tahoma', sans-serif; }}
                </style>
            </head>
            <body>
                <h1>خطأ في توليد الفاتورة</h1>
                <p>{str(e)}</p>
            </body>
            </html>
            """
    
    def preview_invoice(self, invoice_data=None, items_data=None):
        """
        معاينة الفاتورة
        
        Args:
            invoice_data (dict, optional): بيانات الفاتورة. Defaults to None.
            items_data (list, optional): قائمة بعناصر الفاتورة. Defaults to None.
            
        Returns:
            bool: نجاح المعاينة
        """
        try:
            # توليد HTML للفاتورة
            html = self.generate_invoice_html(invoice_data, items_data)
            
            # إنشاء مستند HTML
            document = QTextDocument()
            document.setHtml(html)
            
            # إنشاء طابعة للمعاينة
            printer = QPrinter(QPrinter.HighResolution)
            
            # تعيين حجم الورق
            if self.paper_size == "58mm":
                printer.setPageSize(QPrinter.Custom)
                printer.setPaperSize(QSizeF(58, 200), QPrinter.Millimeter)
            elif self.paper_size == "A4":
                printer.setPageSize(QPrinter.A4)
            else:  # 80mm
                printer.setPageSize(QPrinter.Custom)
                printer.setPaperSize(QSizeF(80, 200), QPrinter.Millimeter)
            
            # إنشاء نافذة المعاينة
            preview = QPrintPreviewDialog(printer)
            preview.paintRequested.connect(lambda p: document.print_(p))
            
            # عرض نافذة المعاينة
            preview.exec_()
            
            return True
            
        except Exception as e:
            print(f"خطأ في معاينة الفاتورة: {str(e)}")
            return False
    
    def print_invoice(self, invoice_data=None, items_data=None, printer_name=None):
        """
        طباعة الفاتورة
        
        Args:
            invoice_data (dict, optional): بيانات الفاتورة. Defaults to None.
            items_data (list, optional): قائمة بعناصر الفاتورة. Defaults to None.
            printer_name (str, optional): اسم الطابعة. Defaults to None.
            
        Returns:
            bool: نجاح الطباعة
        """
        try:
            # التحقق من صحة البيانات
            if invoice_data is None:
                invoice_data = {
                    "invoice_number": "INV-001",
                    "date": datetime.datetime.now().strftime("%Y/%m/%d %H:%M"),
                    "customer_name": "عميل نقدي",
                    "total": 100.0,
                    "discount": 0.0,
                    "final_total": 100.0,
                    "paid": 100.0,
                    "remaining": 0.0
                }
            
            if items_data is None:
                items_data = [
                    {
                        "product_name": "منتج تجريبي",
                        "price": 50.0,
                        "quantity": 2,
                        "total": 100.0
                    }
                ]
            
            # توليد HTML للفاتورة
            html = self.generate_invoice_html(invoice_data, items_data)
            
            # إنشاء مستند HTML
            document = QTextDocument()
            document.setHtml(html)
            
            # إنشاء طابعة
            printer = QPrinter(QPrinter.HighResolution)
            
            # تعيين حجم الورق
            if self.paper_size == "58mm":
                printer.setPageSize(QPrinter.Custom)
                printer.setPaperSize(QSizeF(58, 200), QPrinter.Millimeter)
            elif self.paper_size == "A4":
                printer.setPageSize(QPrinter.A4)
            else:  # 80mm
                printer.setPageSize(QPrinter.Custom)
                printer.setPaperSize(QSizeF(80, 200), QPrinter.Millimeter)
            
            # تعيين الطابعة المحددة
            available_printers = QPrinterInfo.availablePrinterNames()
            print(f"الطابعات المتاحة: {available_printers}")
            
            # التحقق من وجود طابعات متاحة
            if not available_printers:
                from PyQt5.QtWidgets import QMessageBox
                QMessageBox.critical(None, "خطأ في الطباعة", "لا توجد طابعات متاحة في النظام. يرجى التحقق من توصيل وتثبيت الطابعة.")
                return False
                
            if printer_name:
                # محاولة استخدام الطابعة المحددة
                if printer_name in available_printers:
                    printer.setPrinterName(printer_name)
                    print(f"تم اختيار الطابعة: {printer_name}")
                else:
                    # استخدام الطابعة الافتراضية
                    default_printer = QPrinterInfo.defaultPrinterName()
                    if default_printer:
                        printer.setPrinterName(default_printer)
                        print(f"تم اختيار الطابعة الافتراضية: {default_printer}")
                    else:
                        # استخدام الطابعة الافتراضية
                        default_printer = QPrinterInfo.defaultPrinterName()
                        if default_printer:
                            printer.setPrinterName(default_printer)
                            print(f"تم اختيار الطابعة الافتراضية: {default_printer}")
                        else:
                            print("لا توجد طابعة افتراضية، سيتم استخدام أول طابعة متاحة")
                            if available_printers:
                                printer.setPrinterName(available_printers[0])
                                print(f"تم اختيار الطابعة: {available_printers[0]}")
            else:
                # إذا لم يتم تحديد طابعة، استخدم الطابعة الافتراضية
                default_printer = QPrinterInfo.defaultPrinterName()
                if default_printer:
                    printer.setPrinterName(default_printer)
                    print(f"تم اختيار الطابعة الافتراضية: {default_printer}")
                elif available_printers:
                    printer.setPrinterName(available_printers[0])
                    print(f"تم اختيار أول طابعة متاحة: {available_printers[0]}")
            
            # طباعة المستند
            try:
                document.print_(printer)
                print("تمت الطباعة بنجاح")
                return True
            except Exception as e:
                from PyQt5.QtWidgets import QMessageBox
                error_message = f"خطأ في طباعة الفاتورة: {str(e)}"
                print(error_message)
                QMessageBox.critical(None, "خطأ في الطباعة", 
                                    f"فشل في طباعة الفاتورة.\n\n"
                                    f"الخطأ: {str(e)}\n\n"
                                    f"يرجى التحقق من:\n"
                                    f"• توصيل الطابعة\n"
                                    f"• تشغيل الطابعة\n"
                                    f"• توفر الورق والحبر\n"
                                    f"• تثبيت تعريف الطابعة بشكل صحيح")
                return False
        except Exception as e:
            from PyQt5.QtWidgets import QMessageBox
            error_message = f"خطأ غير متوقع: {str(e)}"
            print(error_message)
            QMessageBox.critical(None, "خطأ في الطباعة", 
                                f"حدث خطأ غير متوقع أثناء إعداد الطباعة.\n\n"
                                f"الخطأ: {str(e)}")
            return False
