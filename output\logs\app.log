استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'amount', 'payment_method', 'payment_date', 'notes', 'created_at', 'updated_at']
تم إضافة عمود user_id إلى جدول المدفوعات
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
تطبيق تحديث قاعدة البيانات: الإصدار 1
تم حفظ التغييرات في قاعدة البيانات
تطبيق تحديث قاعدة البيانات: الإصدار 2
تم حفظ التغييرات في قاعدة البيانات
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'amount', 'payment_method', 'payment_date', 'notes', 'created_at', 'updated_at', 'user_id']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'amount', 'payment_method', 'payment_date', 'notes', 'created_at', 'updated_at', 'user_id']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
تم إنشاء المستخدم الرئيسي بنجاح
تم حفظ التغييرات في قاعدة البيانات
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'amount', 'payment_method', 'payment_date', 'notes', 'created_at', 'updated_at', 'user_id']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'amount', 'payment_method', 'payment_date', 'notes', 'created_at', 'updated_at', 'user_id']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم حفظ التغييرات في قاعدة البيانات
ملف الترخيص غير موجود في المحاولة 1
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
فلتر تاريخ البدء: 2025/08/31
فلتر تاريخ الانتهاء: 2025/08/31 (قبل 2025/09/01)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount,
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status,
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/08/31', '2025/08/31%', '2025/09/01']
تم العثور على 0 فاتورة
Invoices after initial filtering (status, customer): 0
تم استرجاع 0 مورد من قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
تم إنشاء جدول فئات المصروفات بنجاح
خطأ في جلب عدد الفئات: 0
ملف الترخيص غير موجود في المحاولة 1
تم العثور على 4 طابعة باستخدام win32print
النتيجة النهائية: 1 طابعة متاحة
تم تحميل 1 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تغيير حجم الورق إلى: ورق A4 عادي (210×297 مم)
تم تحميل الإعدادات بنجاح باستخدام SettingsManager
ملف الترخيص غير موجود في المحاولة 1
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تسجيل مكون للاستماع لتغييرات company
تم تسجيل مكون للاستماع لتغييرات currency
تم تسجيل مكون للاستماع لتغييرات printer
تم تسجيل مكون للاستماع لتغييرات invoice
تم تسجيل مكون للاستماع لتغييرات backup
تم تسجيل مكون للاستماع لتغييرات ui
تم تسجيل SettingsView للاستماع لتغييرات الإعدادات بنجاح
ملف الترخيص غير موجود في المحاولة 1
حالة التفعيل: غير مفعل
ملف الترخيص غير موجود في المحاولة 1
تم تعطيل جميع التبويبات عدا تاب الإعدادات
تم قطع الاتصال بقاعدة البيانات
تم تحميل الإعدادات بنجاح باستخدام SettingsManager
تم تحديث صفحة الإعدادات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
تم تحميل الإعدادات بنجاح باستخدام SettingsManager
تم تحديث صفحة الإعدادات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
تم بدء مؤقت التحقق من التفعيل دورياً
استخدام الخط المحمل من الملف: Readex Pro
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم إنشاء الجداول بنجاح
تم حفظ التغييرات في قاعدة البيانات
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'amount', 'payment_method', 'payment_date', 'notes', 'created_at', 'updated_at', 'user_id']
تم حفظ التغييرات في قاعدة البيانات
تم حفظ التغييرات في قاعدة البيانات
لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.
أعمدة جدول المدفوعات الحالية: ['id', 'invoice_id', 'customer_id', 'amount', 'payment_method', 'payment_date', 'notes', 'created_at', 'updated_at', 'user_id']
تم حفظ التغييرات في قاعدة البيانات
المستخدم الرئيسي موجود بالفعل
تم قطع الاتصال بقاعدة البيانات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم حفظ التغييرات في قاعدة البيانات
ملف الترخيص غير موجود في المحاولة 1
خطأ في معالجة المنتجات المفضلة: 'SalesView' object has no attribute 'product_buttons'
فلتر تاريخ البدء: 2025/09/02
فلتر تاريخ الانتهاء: 2025/09/02 (قبل 2025/09/03)
استعلام البحث: 
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount,
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status,
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
         AND (i.date >= ? OR i.date LIKE ?) AND i.date < ?
معلمات البحث: ['2025/09/02', '2025/09/02%', '2025/09/03']
تم العثور على 0 فاتورة
Invoices after initial filtering (status, customer): 0
تم استرجاع 2 مورد من قاعدة البيانات
أول مورد: {'id': 1, 'name': 'احمد ربيع', 'phone': '', 'email': '', 'address': '', 'notes': '', 'contact_person': None, 'total_purchases': 9015.5, 'last_purchase_date': '2025/07/21 17:27:02', 'purchase_count': 4, 'total_debt': 0}
تاريخ آخر عملية شراء للمورد احمد ربيع: 2025/07/21 17:27:02
تاريخ آخر عملية شراء للمورد القيصر: 2025/08/26 18:41:27
خطأ في جلب عدد الفئات: 0
ملف الترخيص غير موجود في المحاولة 1
تم العثور على 3 طابعة باستخدام win32print
النتيجة النهائية: 1 طابعة متاحة
تم تحميل 1 مستخدم من قاعدة البيانات
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تغيير حجم الورق إلى: ورق حراري عادي (80×250 مم)
تم تحميل الإعدادات بنجاح باستخدام SettingsManager
ملف الترخيص غير موجود في المحاولة 1
تم تفعيل النسخ الاحتياطي التلقائي: أسبوعياً (168.0 ساعة)
تم تسجيل مكون للاستماع لتغييرات company
تم تسجيل مكون للاستماع لتغييرات currency
تم تسجيل مكون للاستماع لتغييرات printer
تم تسجيل مكون للاستماع لتغييرات invoice
تم تسجيل مكون للاستماع لتغييرات backup
تم تسجيل مكون للاستماع لتغييرات ui
تم تسجيل SettingsView للاستماع لتغييرات الإعدادات بنجاح
ملف الترخيص غير موجود في المحاولة 1
حالة التفعيل: غير مفعل
ملف الترخيص غير موجود في المحاولة 1
تم تعطيل جميع التبويبات عدا تاب الإعدادات
تم قطع الاتصال بقاعدة البيانات
تم تحميل الإعدادات بنجاح باستخدام SettingsManager
تم تحديث صفحة الإعدادات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
تم تحميل الإعدادات بنجاح باستخدام SettingsManager
تم تحديث صفحة الإعدادات
تم الاتصال بقاعدة البيانات بنجاح
الإصدار الحالي لقاعدة البيانات: 2
تم قطع الاتصال بقاعدة البيانات
تم بدء مؤقت التحقق من التفعيل دورياً
2025-09-02 12:29:01,972 - ThermalPrinter - INFO - تم تحميل win32print بنجاح
تم اكتشاف 3 طابعة: ['Xprinter XP-235B', 'Microsoft Print to PDF', 'Fax']
تم اكتشاف 3 طابعة: ['Xprinter XP-235B', 'Microsoft Print to PDF', 'Fax']
قائمة الطابعات المتاحة: ['Xprinter XP-235B', 'Microsoft Print to PDF', 'Fax']
لم يتم العثور على الطابعة الطابعة الافتراضية باستخدام win32print: function missing required argument 'DesiredAccess' (pos 1)
نتيجة التحقق من الطابعة الطابعة الافتراضية: غير متاحة
