#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
سكريبت لإنشاء فاتورة تجريبية وحفظها كملف PDF
"""

import os
import sys
import datetime
from PyQt5.QtWidgets import QApplication
from PyQt5.QtCore import QSettings
from PyQt5.QtGui import QPageSize
from PyQt5.QtPrintSupport import QPrinter

# إضافة المجلد الحالي إلى مسار البحث
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# استيراد الوحدات المطلوبة
from utils.invoice_designer import InvoiceDesigner
from utils.settings_manager import SettingsManager

def create_sample_invoice():
    """إنشاء فاتورة تجريبية وحفظها كملف PDF"""
    
    # إنشاء بيانات تجريبية للفاتورة
    invoice_data = {
        "invoice_number": "INV-TEST-001",
        "date": datetime.datetime.now().strftime("%Y/%m/%d %H:%M"),
        "customer_name": "عميل تجريبي",
        "total": 250.0,
        "discount": 20.0,
        "final_total": 230.0,
        "paid": 230.0,
        "remaining": 0.0,
        "reference_number": "TEST-123456"
    }
    
    # إنشاء بيانات تجريبية للمنتجات
    items_data = [
        {
            "product_name": "منتج تجريبي 1",
            "price": 50.0,
            "quantity": 2,
            "total": 100.0
        },
        {
            "product_name": "منتج تجريبي 2",
            "price": 75.0,
            "quantity": 2,
            "total": 150.0
        }
    ]
    
    # معلومات الشركة
    company_info = {
        "name": "شركة نموذجية للتجارة",
        "phone": "هاتف: 0123456789",
        "address": "العنوان: شارع النموذج، المدينة النموذجية"
    }
    
    # إنشاء مسار لحفظ الملف
    documents_path = os.path.expanduser("~/Documents")
    if not os.path.exists(documents_path):
        documents_path = os.path.expanduser("~")
    
    # إنشاء اسم ملف مناسب
    timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
    file_path = os.path.join(documents_path, f"فاتورة_تجريبية_{timestamp}.pdf")
    
    # تأكد من أن إعدادات توسيط المحتوى مفعلة
    settings_manager = SettingsManager()
    settings_manager.set_setting("invoice_design", "center_content", True)
    
    # إنشاء مصمم الفاتورة
    invoice_designer = InvoiceDesigner()
    
    # إنشاء HTML للفاتورة
    html_content = invoice_designer.generate_invoice_html(
        invoice_data,
        items_data,
        company_info
    )
    
    # إنشاء طابعة PDF
    printer = QPrinter(QPrinter.HighResolution)
    printer.setOutputFormat(QPrinter.PdfFormat)
    printer.setOutputFileName(file_path)
    printer.setPageSize(QPageSize(QPageSize.A4))
    
    # إنشاء مستند HTML
    from PyQt5.QtGui import QTextDocument
    document = QTextDocument()
    document.setHtml(html_content)
    
    # طباعة المستند إلى ملف PDF
    success = document.print_(printer)
    
    if success:
        print(f"✅ تم إنشاء الفاتورة التجريبية بنجاح وحفظها في: {file_path}")
        return file_path
    else:
        print("❌ فشل في إنشاء ملف PDF للفاتورة التجريبية")
        return None

if __name__ == "__main__":
    # إنشاء تطبيق PyQt
    app = QApplication(sys.argv)
    
    # إنشاء الفاتورة التجريبية
    pdf_path = create_sample_invoice()
    
    # فتح الملف إذا تم إنشاؤه بنجاح
    if pdf_path and os.path.exists(pdf_path):
        # فتح الملف باستخدام التطبيق الافتراضي
        if sys.platform == 'win32':
            os.startfile(pdf_path)
        elif sys.platform == 'darwin':  # macOS
            os.system(f'open "{pdf_path}"')
        else:  # Linux
            os.system(f'xdg-open "{pdf_path}"')
    
    # إنهاء التطبيق
    sys.exit(0)