# -*- coding: utf-8 -*-
"""
إعدادات مسبقة لطابعات الاستيكرات الشائعة
يحتوي على أحجام الاستيكرات المعيارية والإعدادات المناسبة لكل نوع طابعة
"""

# أحجام الاستيكرات الشائعة (بالمليمتر)
STICKER_SIZES = {
    "صغير جداً": {
        "width": 25,
        "height": 15,
        "description": "مناسب للمنتجات الصغيرة جداً",
        "barcode_only": True,
        "max_text_length": 8
    },
    "صغير": {
        "width": 30,
        "height": 20,
        "description": "مناسب للمنتجات الصغيرة",
        "barcode_only": False,
        "max_text_length": 10
    },
    "متوسط": {
        "width": 40,
        "height": 25,
        "description": "الحجم الأكثر شيوعاً",
        "barcode_only": False,
        "max_text_length": 12
    },
    "كبير": {
        "width": 50,
        "height": 30,
        "description": "مناسب للمنتجات الكبيرة",
        "barcode_only": False,
        "max_text_length": 16
    },
    "كبير جداً": {
        "width": 60,
        "height": 40,
        "description": "للمنتجات الكبيرة جداً",
        "barcode_only": False,
        "max_text_length": 20
    }
}

# إعدادات طابعات الاستيكرات الشائعة - محسنة للجودة الفائقة
PRINTER_PRESETS = {
    "Brother QL": {
        "supported_sizes": ["صغير", "متوسط", "كبير"],
        "recommended_size": "متوسط",
        "dpi": 2400,  # دقة فائقة محسنة
        "margins": {"top": 0.1, "bottom": 0.1, "left": 0.1, "right": 0.1}  # هوامش أصغر للاستفادة القصوى من المساحة
    },
    "DYMO LabelWriter": {
        "supported_sizes": ["صغير جداً", "صغير", "متوسط"],
        "recommended_size": "صغير",
        "dpi": 2400,  # دقة فائقة محسنة
        "margins": {"top": 0.05, "bottom": 0.05, "left": 0.05, "right": 0.05}  # هوامش أصغر للاستفادة القصوى من المساحة
    },
    "Zebra ZD": {
        "supported_sizes": ["صغير", "متوسط", "كبير", "كبير جداً"],
        "recommended_size": "متوسط",
        "dpi": 2400,  # دقة فائقة محسنة
        "margins": {"top": 0.15, "bottom": 0.15, "left": 0.15, "right": 0.15}  # هوامش أصغر للاستفادة القصوى من المساحة
    },
    "TSC": {
        "supported_sizes": ["صغير", "متوسط", "كبير"],
        "recommended_size": "متوسط",
        "dpi": 2400,  # دقة فائقة محسنة
        "margins": {"top": 0.1, "bottom": 0.1, "left": 0.1, "right": 0.1}  # هوامش أصغر للاستفادة القصوى من المساحة
    },
    "Godex": {
        "supported_sizes": ["صغير", "متوسط", "كبير"],
        "recommended_size": "متوسط",
        "dpi": 2400,  # دقة فائقة محسنة
        "margins": {"top": 0.15, "bottom": 0.15, "left": 0.15, "right": 0.15}  # هوامش أصغر للاستفادة القصوى من المساحة
    }
}

def get_recommended_settings(printer_name=None, sticker_size=None):
    """
    الحصول على الإعدادات الموصى بها لطابعة معينة أو حجم استيكر معين
    """
    settings = {
        "width": 40,
        "height": 25,
        "dpi": 2400,  # دقة افتراضية فائقة محسنة
        "margins": {"top": 0.1, "bottom": 0.1, "left": 0.1, "right": 0.1},  # هوامش أصغر للاستفادة القصوى من المساحة
        "font_size": 14,  # خط أكبر للوضوح المحسن
        "barcode_height": 70  # ارتفاع مُحسن للباركود - زيادة من 60 للوضوح الأمثل
    }
    
    # إذا تم تحديد حجم الاستيكر
    if sticker_size and sticker_size in STICKER_SIZES:
        size_info = STICKER_SIZES[sticker_size]
        settings["width"] = size_info["width"]
        settings["height"] = size_info["height"]
        
        # تعديل الإعدادات بناءً على الحجم - قيم مُحسنة لارتفاع الباركود
        if size_info["width"] <= 25:
            settings["font_size"] = 8
            settings["barcode_height"] = 50  # زيادة من 40 لتحسين وضوح الباركود
        elif size_info["width"] <= 40:
            settings["font_size"] = 10
            settings["barcode_height"] = 70  # زيادة من 60 لتحسين وضوح الباركود
        else:
            settings["font_size"] = 12
            settings["barcode_height"] = 95  # زيادة من 80 لتحسين وضوح الباركود
    
    # إذا تم تحديد نوع الطابعة
    if printer_name:
        for preset_name, preset_info in PRINTER_PRESETS.items():
            if preset_name.lower() in printer_name.lower():
                settings["dpi"] = preset_info["dpi"]
                settings["margins"] = preset_info["margins"]
                
                # استخدام الحجم الموصى به للطابعة
                if not sticker_size:
                    recommended_size = preset_info["recommended_size"]
                    if recommended_size in STICKER_SIZES:
                        size_info = STICKER_SIZES[recommended_size]
                        settings["width"] = size_info["width"]
                        settings["height"] = size_info["height"]
                break
    
    return settings

def validate_sticker_compatibility(width, height, has_name=False, has_price=False, code_length=10):
    """
    التحقق من توافق حجم الاستيكر مع المحتوى المطلوب
    """
    issues = []
    warnings = []
    recommendations = []
    
    # فحص العرض الأدنى
    min_width = 20 + (code_length * 1.5)  # تقدير تقريبي
    if width < min_width:
        issues.append(f"العرض صغير جداً للباركود ({code_length} أحرف)")
        recommendations.append("قلل طول كود الباركود أو استخدم استيكر أعرض")
    
    # فحص الارتفاع
    required_height = 15  # الحد الأدنى للباركود
    if has_name:
        required_height += 8
    if has_price:
        required_height += 8
    
    if height < required_height:
        if height >= 15:
            warnings.append("الارتفاع كافي للباركود فقط")
            recommendations.append("أزل اسم المنتج أو السعر لتوفير مساحة")
        else:
            issues.append("الارتفاع صغير جداً حتى للباركود")
            recommendations.append("استخدم استيكر أطول")
    
    # توصيات عامة
    if width <= 30:
        recommendations.append("استخدم خط صغير للنص")
        recommendations.append("تجنب النصوص الطويلة")
    
    return {
        "compatible": len(issues) == 0,
        "issues": issues,
        "warnings": warnings,
        "recommendations": recommendations
    }

def get_optimal_font_size(sticker_width, sticker_height):
    """
    حساب حجم الخط الأمثل بناءً على أبعاد الاستيكر
    """
    if sticker_width <= 25:
        return 6
    elif sticker_width <= 30:
        return 8
    elif sticker_width <= 40:
        return 10
    elif sticker_width <= 50:
        return 12
    else:
        return 14

def get_optimal_barcode_height(sticker_width, sticker_height):
    """
    حساب ارتفاع الباركود الأمثل بناءً على أبعاد الاستيكر
    """
    # نسبة الباركود من الارتفاع الكلي - مصغرة للطباعة في ورقة واحدة
    barcode_ratio = 0.4

    if sticker_height <= 15:
        return min(30, int(sticker_height * barcode_ratio * 3.78))  # تحويل لبكسل
    elif sticker_height <= 25:
        return min(40, int(sticker_height * barcode_ratio * 3.78))
    elif sticker_height <= 35:
        return min(50, int(sticker_height * barcode_ratio * 3.78))
    else:
        return min(60, int(sticker_height * barcode_ratio * 3.78))

def suggest_sticker_size(content_requirements):
    """
    اقتراح حجم الاستيكر المناسب بناءً على متطلبات المحتوى
    """
    has_name = content_requirements.get("has_name", False)
    has_price = content_requirements.get("has_price", False)
    code_length = content_requirements.get("code_length", 10)
    
    # حساب المساحة المطلوبة
    required_height = 15  # الحد الأدنى للباركود
    if has_name:
        required_height += 8
    if has_price:
        required_height += 8
    
    required_width = 20 + (code_length * 1.5)
    
    # البحث عن أصغر حجم مناسب
    for size_name, size_info in STICKER_SIZES.items():
        if (size_info["width"] >= required_width and 
            size_info["height"] >= required_height):
            return size_name
    
    # إذا لم يوجد حجم مناسب، اقترح الحجم الكبير
    return "كبير جداً"
