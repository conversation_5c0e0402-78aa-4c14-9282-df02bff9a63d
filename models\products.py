"""
وحدة إدارة المنتجات - توفر وظائف للتعامل مع بيانات المنتجات في قاعدة البيانات
"""

from models.database import db
import datetime

class ProductModel:
    """نموذج التعامل مع بيانات المنتجات"""

    @staticmethod
    def get_all_products():
        """استرجاع جميع المنتجات من قاعدة البيانات"""
        query = """
            SELECT id, code, name, description, category, price, cost, quantity as stock,
                   min_quantity, image_path, is_favorite, product_type
            FROM products
            ORDER BY id
        """
        return db.fetch_all(query)

    @staticmethod
    def get_product_by_id(product_id):
        """استرجاع منتج بواسطة المعرف"""
        query = """
            SELECT id, code, name, description, category, price, cost, quantity as stock,
                   min_quantity, image_path, is_favorite, product_type
            FROM products
            WHERE id = ?
        """
        return db.fetch_one(query, (product_id,))

    @staticmethod
    def get_product_by_code(code):
        """استرجاع منتج بواسطة الكود"""
        query = """
            SELECT id, code, name, description, category, price, cost, quantity as stock,
                   min_quantity, image_path, is_favorite, product_type
            FROM products
            WHERE code = ?
        """
        return db.fetch_one(query, (code,))

    @staticmethod
    def search_products(search_text, category=None, product_type=None):
        """البحث عن منتجات بواسطة النص والفئة ونوع المنتج"""
        search_pattern = f"%{search_text}%"
        params = [search_pattern, search_pattern, search_pattern]

        # بناء الاستعلام حسب معايير البحث
        query = """
            SELECT id, code, name, description, category, price, cost, quantity as stock,
                   min_quantity, image_path, is_favorite, product_type
            FROM products
            WHERE (name LIKE ? OR code LIKE ? OR description LIKE ?)
        """

        # إضافة شرط الفئة إذا تم تحديدها
        if category and category != "الكل":
            query += " AND category = ?"
            params.append(category)

        # إضافة شرط نوع المنتج إذا تم تحديده
        if product_type:
            query += " AND product_type = ?"
            params.append(product_type)

        query += " ORDER BY id"

        return db.fetch_all(query, tuple(params))

    @staticmethod
    def get_product_categories():
        """استرجاع جميع فئات المنتجات المتوفرة"""
        query = """
            SELECT DISTINCT category
            FROM products
            WHERE category IS NOT NULL AND category <> ''
            ORDER BY category
        """
        categories = db.fetch_all(query)
        return [category['category'] for category in categories]

    @staticmethod
    def add_product(product_data):
        """إضافة منتج جديد إلى قاعدة البيانات"""
        query = """
            INSERT INTO products (
                code, name, description, category, price, cost,
                quantity, min_quantity, image_path, is_favorite, product_type
            )
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        params = (
            product_data.get('code'),
            product_data.get('name'),
            product_data.get('description'),
            product_data.get('category'),
            product_data.get('price'),
            product_data.get('cost', 0),
            product_data.get('stock', 0),
            product_data.get('min_quantity', 1),
            product_data.get('image_path'),
            product_data.get('favorite', 0),
            product_data.get('product_type', 'physical')  # القيمة الافتراضية هي منتج عادي
        )

        success = db.execute(query, params)
        if success:
            db.commit()
            return db.get_last_insert_id()
        return None

    @staticmethod
    def update_product(product_id, product_data):
        """تحديث بيانات منتج موجود"""
        # جلب المنتج الحالي من قاعدة البيانات
        current_product = ProductModel.get_product_by_id(product_id)
        # تحديد قيمة is_favorite بشكل صحيح
        is_favorite = (
            product_data.get('is_favorite')
            if 'is_favorite' in product_data else
            product_data.get('favorite')
            if 'favorite' in product_data else
            (current_product.get('is_favorite', 0) if current_product else 0)
        )
        query = """
            UPDATE products
            SET code = ?,
                name = ?,
                description = ?,
                category = ?,
                price = ?,
                cost = ?,
                quantity = ?,
                min_quantity = ?,
                image_path = ?,
                is_favorite = ?,
                product_type = ?,
                updated_at = ?
            WHERE id = ?
        """
        params = (
            product_data.get('code'),
            product_data.get('name'),
            product_data.get('description'),
            product_data.get('category'),
            product_data.get('price'),
            product_data.get('cost', 0),
            product_data.get('stock', 0),
            product_data.get('min_quantity', 1),
            product_data.get('image_path'),
            is_favorite,
            product_data.get('product_type', 'physical'),
            datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            product_id
        )

        success = db.execute(query, params)
        if success:
            db.commit()
            return True
        return False

    @staticmethod
    def delete_product(product_id):
        """حذف منتج من قاعدة البيانات"""
        query = "DELETE FROM products WHERE id = ?"
        success = db.execute(query, (product_id,))
        if success:
            db.commit()
            return True
        return False

    @staticmethod
    def update_product_quantity(product_id, new_quantity):
        """تحديث كمية المنتج في المخزون"""
        try:
            # الحصول على المنتج للتحقق من نوعه
            product = ProductModel.get_product_by_id(product_id)
            if not product:
                db.log_error(f"المنتج غير موجود بالمعرف: {product_id}")
                return False

            # التحقق من نوع المنتج - لا يمكن تحديث كمية الخدمات
            if product.get('product_type') == 'service':
                db.log_error(f"لا يمكن تحديث كمية الخدمة بالمعرف: {product_id}")
                return False

            # التأكد من الاتصال بقاعدة البيانات
            if not db.conn:
                if not db.connect():
                    db.log_error("فشل الاتصال بقاعدة البيانات عند تحديث كمية المنتج")
                    return False

            # منع الكميات السالبة
            if new_quantity < 0:
                new_quantity = 0

            # تنفيذ استعلام تحديث الكمية
            query = """
                UPDATE products
                SET quantity = ?,
                    updated_at = ?
                WHERE id = ?
            """
            params = (
                new_quantity,
                datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                product_id
            )

            # تنفيذ الاستعلام
            success = db.execute(query, params)

            # التأكد من حفظ التغييرات بشكل صريح
            if success:
                db.commit()
                # طباعة رسالة تأكيد لأغراض التصحيح
                print(f"تم تحديث كمية المنتج {product_id} إلى {new_quantity} بنجاح")
                return True
            else:
                db.log_error(f"فشل تنفيذ استعلام تحديث كمية المنتج {product_id}")
                return False
        except Exception as e:
            # التراجع عن التغييرات في حالة حدوث خطأ
            if db.conn:
                db.rollback()
            db.log_error(f"خطأ في تحديث كمية المنتج {product_id}: {str(e)}")
            print(f"خطأ في تحديث كمية المنتج: {str(e)}")
            return False

    @staticmethod
    def update_stock(product_code, quantity, operation='add'):
        """تحديث مخزون المنتج باستخدام رمز المنتج"""
        try:
            # التأكد من الاتصال بقاعدة البيانات
            if not db.conn:
                if not db.connect():
                    db.log_error("فشل الاتصال بقاعدة البيانات عند تحديث كمية المنتج")
                    return False

            # الحصول على المنتج
            product = ProductModel.get_product_by_code(product_code)
            if not product:
                db.log_error(f"المنتج غير موجود برمز: {product_code}")
                return False

            product_id = product.get('id')
            current_quantity = product.get('stock', 0)

            # حساب الكمية الجديدة
            if operation == 'add':
                new_quantity = current_quantity + quantity
            elif operation == 'subtract':
                new_quantity = current_quantity - quantity
                # منع الكميات السالبة
                if new_quantity < 0:
                    new_quantity = 0
            else:
                db.log_error(f"عملية غير صالحة: {operation}")
                return False

            # تحديث المخزون
            return ProductModel.update_product_quantity(product_id, new_quantity)

        except Exception as e:
            # التراجع عن التغييرات في حالة حدوث خطأ
            if db.conn:
                db.rollback()
            db.log_error(f"خطأ في تحديث مخزون المنتج {product_code}: {str(e)}")
            print(f"خطأ في تحديث مخزون المنتج: {str(e)}")
            return False

    @staticmethod
    def get_low_stock_products():
        """استرجاع المنتجات ذات المخزون المنخفض"""
        query = """
            SELECT id, code, name, category, quantity, min_quantity, price, cost
            FROM products
            WHERE quantity <= min_quantity
            ORDER BY quantity
        """
        return db.fetch_all(query)

    @staticmethod
    def get_favorite_products():
        """استرجاع المنتجات المفضلة (الحد الأقصى 12 منتج)"""
        query = """
            SELECT id, code, name, description, category, price, cost, quantity, product_type, custom_order, button_color
            FROM products
            WHERE is_favorite = 1 AND product_type != 'category_placeholder'
            ORDER BY custom_order ASC, name
            LIMIT 12
        """
        return db.fetch_all(query)

    @staticmethod
    def get_favorites_count():
        """الحصول على عدد المنتجات المفضلة الحالية"""
        query = """
            SELECT COUNT(*) as count
            FROM products
            WHERE is_favorite = 1 AND product_type != 'category_placeholder'
        """
        result = db.fetch_one(query)
        return result.get('count', 0) if result else 0

    @staticmethod
    def update_product_order(product_id, order_position):
        """تحديث ترتيب المنتج المفضل"""
        query = """
            UPDATE products
            SET custom_order = ?,
                updated_at = ?
            WHERE id = ?
        """
        params = (
            order_position,
            datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            product_id
        )

        success = db.execute(query, params)
        if success:
            db.commit()
            return True
        return False

    @staticmethod
    def toggle_favorite(product_id, is_favorite):
        """تبديل حالة المفضلة للمنتج"""
        try:
            # إذا كان يتم تحديد المنتج كمفضل، نحتاج لتعيين ترتيبه
            if is_favorite:
                # التحقق من عدد المنتجات المفضلة الحالية
                count_query = """
                    SELECT COUNT(*) as count
                    FROM products
                    WHERE is_favorite = 1 AND product_type != 'category_placeholder'
                """
                count_result = db.fetch_one(count_query)
                current_count = count_result.get('count', 0) if count_result else 0

                # منع إضافة منتج مفضل إذا وصل العدد إلى 12
                if current_count >= 12:
                    return False

                # الحصول على أعلى قيمة ترتيب حالية
                max_order_query = """
                    SELECT MAX(custom_order) as max_order
                    FROM products
                    WHERE is_favorite = 1
                """
                max_order_result = db.fetch_one(max_order_query)
                max_order = max_order_result.get('max_order', 0) if max_order_result and max_order_result.get('max_order') is not None else 0

                # تعيين القيمة الجديدة للترتيب (أكبر قيمة + 1)
                new_order = max_order + 1

                query = """
                    UPDATE products
                    SET is_favorite = ?,
                        custom_order = ?,
                        updated_at = ?
                    WHERE id = ?
                """
                params = (
                    1 if is_favorite else 0,
                    new_order if is_favorite else 0,
                    datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    product_id
                )
            else:
                # عند إلغاء التفضيل، نعيد ترتيب المنتج إلى 0
                query = """
                    UPDATE products
                    SET is_favorite = ?,
                        custom_order = 0,
                        updated_at = ?
                    WHERE id = ?
                """
                params = (
                    0,  # عند إلغاء التفضيل، نضع القيمة 0
                    datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    product_id
                )

            success = db.execute(query, params)
            if success:
                db.commit()
                return True
            return False
        except Exception as e:
            print(f"خطأ في تبديل حالة المفضلة: {str(e)}")
            if db.conn:
                db.rollback()
            return False

    @staticmethod
    def get_product_stats():
        """استرجاع إحصائيات المنتجات"""
        # إجمالي عدد المنتجات
        total_query = "SELECT COUNT(*) as total FROM products"
        total_result = db.fetch_one(total_query)
        total_products = total_result.get('total', 0) if total_result else 0

        # إجمالي القيمة
        value_query = "SELECT SUM(price * quantity) as total_value FROM products"
        value_result = db.fetch_one(value_query)
        total_value = value_result.get('total_value', 0) if value_result else 0

        # عدد المنتجات منخفضة المخزون
        low_stock_query = "SELECT COUNT(*) as count FROM products WHERE quantity <= min_quantity"
        low_stock_result = db.fetch_one(low_stock_query)
        low_stock_count = low_stock_result.get('count', 0) if low_stock_result else 0

        return {
            "total_products": total_products,
            "total_value": total_value,
            "low_stock_count": low_stock_count
        }

    @staticmethod
    def add_product_if_not_exists(product_data):
        """إضافة منتج جديد إذا لم يكن موجودًا بالفعل (استخدام في عمليات الاستيراد)"""
        # التحقق من وجود المنتج بالفعل بواسطة الكود
        existing_product = ProductModel.get_product_by_code(product_data.get('code'))

        if existing_product:
            # تحديث الكمية والسعر إذا لزم الأمر
            if 'update_if_exists' in product_data and product_data['update_if_exists']:
                # تحديث الكمية إذا تم تحديدها
                if 'quantity' in product_data:
                    current_quantity = existing_product.get('stock', 0)
                    new_quantity = current_quantity + product_data.get('quantity', 0)
                    ProductModel.update_product_quantity(existing_product['id'], new_quantity)
                return existing_product['id']
            else:
                return existing_product['id']
        else:
            # إضافة منتج جديد
            return ProductModel.add_product(product_data)

    @staticmethod
    def get_product_by_name(product_name):
        """استرجاع منتج بواسطة الاسم"""
        query = """
            SELECT id, code, name, description, category, price, cost, quantity as stock,
                   min_quantity, image_path, is_favorite, product_type
            FROM products
            WHERE name = ?
        """
        return db.fetch_one(query, (product_name,))

    @staticmethod
    def get_products_count_by_category(category):
        """استرجاع عدد المنتجات في تصنيف معين"""
        query = """
            SELECT COUNT(*) as count
            FROM products
            WHERE category = ?
        """
        result = db.fetch_one(query, (category,))
        return result.get('count', 0) if result else 0

    @staticmethod
    def add_category(category_name):
        """إضافة تصنيف جديد إلى جدول التصنيفات إذا كان موجوداً، وإلا يتم إضافته كتصنيف افتراضي"""
        try:
            # التحقق من وجود جدول للتصنيفات (قد لا يكون موجوداً)
            # مهم: هذا التنفيذ يفترض أن التصنيفات مخزنة فقط في حقول المنتجات وليس في جدول منفصل
            # يمكن تعديل هذا لإنشاء جدول تصنيفات منفصل في المستقبل إذا لزم الأمر

            # إضافة منتج وهمي مؤقت بهذا التصنيف فقط لإظهاره في قائمة التصنيفات
            # نضع قيمة كمية 0 وسعر 0 وننشئه كمنتج من نوع خاص "category_placeholder"
            query = """
                INSERT INTO products (
                    name, code, category, price, cost, quantity,
                    min_quantity, is_favorite, product_type, description
                )
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                f"_تصنيف_{category_name}",  # اسم مميز لتجنب التضارب
                f"CAT{category_name}",  # كود مميز
                category_name,  # التصنيف
                0,  # السعر
                0,  # التكلفة
                0,  # الكمية
                0,  # الحد الأدنى
                0,  # المفضلة
                "category_placeholder",  # نوع خاص
                "هذا منتج وهمي يستخدم فقط لتمثيل التصنيف"  # وصف
            )

            success = db.execute(query, params)
            if success:
                db.commit()
                return True
            return False
        except Exception as e:
            db.log_error(f"خطأ في إضافة تصنيف جديد: {str(e)}")
            print(f"خطأ في إضافة تصنيف جديد: {str(e)}")
            if db.conn:
                db.rollback()
            return False

    @staticmethod
    def update_category(old_category, new_category):
        """تحديث اسم التصنيف لجميع المنتجات المرتبطة به"""
        try:
            query = """
                UPDATE products
                SET category = ?,
                    updated_at = ?
                WHERE category = ?
            """
            params = (
                new_category,
                datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                old_category
            )

            success = db.execute(query, params)
            if success:
                db.commit()
                return True
            return False
        except Exception as e:
            db.log_error(f"خطأ في تحديث اسم التصنيف: {str(e)}")
            print(f"خطأ في تحديث اسم التصنيف: {str(e)}")
            if db.conn:
                db.rollback()
            return False

    @staticmethod
    def delete_category(category):
        """حذف التصنيف من قاعدة البيانات (يتم حذف المنتجات الوهمية المرتبطة بالتصنيف فقط)"""
        try:
            # حذف المنتجات الوهمية المرتبطة بهذا التصنيف
            query = """
                DELETE FROM products
                WHERE category = ? AND product_type = 'category_placeholder'
            """

            success = db.execute(query, (category,))
            if success:
                db.commit()
                return True
            return False
        except Exception as e:
            db.log_error(f"خطأ في حذف التصنيف: {str(e)}")
            print(f"خطأ في حذف التصنيف: {str(e)}")
            if db.conn:
                db.rollback()
            return False

    @staticmethod
    def update_product_button_color(product_id, color_code):
        """تحديث لون زر المنتج المفضل"""
        query = """
            UPDATE products
            SET button_color = ?,
                updated_at = ?
            WHERE id = ?
        """
        params = (
            color_code,
            datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            product_id
        )

        success = db.execute(query, params)
        if success:
            db.commit()
            return True
        return False