# -*- coding: utf-8 -*-
from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QLabel, QPushButton, QHBoxLayout,
                          QFrame, QGridLayout, QScrollArea, QSizePolicy, QTableWidget, QTableWidgetItem, QHeaderView, QComboBox,
                          QCompleter, QLineEdit, QDialog, QListWidget, QListWidgetItem, QGroupBox, QCheckBox, QMessageBox,
                          QDoubleSpinBox, QInputDialog, QMenu, QAction, QAbstractItemView, QFormLayout, QSpacerItem, QApplication,
                          QColorDialog, QSplitter)
from PyQt5.QtCore import Qt, QTimer, QSortFilterProxyModel, QRegExp, QStringListModel, QSize, QMimeData, QPoint, QByteArray, QSettings
from PyQt5.QtGui import QFont, QIcon, QColor, QPainter, QDrag, QPixmap, QCursor, QKeySequence
from PyQt5.QtWidgets import QShortcut
import traceback  # Agregar para obtener información detallada de errores


# Define RTL helper classes directly
class RTLComboBox(QComboBox):
    """Replacement class for RTLComboBox"""
    pass

def apply_rtl_to_all_widgets(widget):
    """Replacement function for apply_rtl_to_all_widgets"""
    pass

from styles import AppStyles  # استيراد التنسيقات من ملف styles.py
import datetime
from models.customers import CustomerModel  # استيراد نموذج العملاء
from models.products import ProductModel
from models.invoices import InvoiceModel
from utils.custom_widgets import show_information, show_warning, show_error, show_question
from utils.date_utils import DateTimeUtils

# ثوابت التطبيق
CURRENCY = "جنيه"  # العملة المستخدمة في التطبيق
CURRENCY_SYMBOL = "ج.م"  # رمز العملة المصرية

class DraggableProductButton(QPushButton):
    """Botón que permite arrastrar y soltar para reordenar los productos favoritos"""
    def __init__(self, product, format_price_func, parent=None):
        super().__init__(parent)
        self.product = product
        self.format_price_func = format_price_func
        self.setObjectName("favorite_product_btn")
        self.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.setCursor(QCursor(Qt.PointingHandCursor))
        self.setAcceptDrops(True)  # Permitir soltar elementos en este botón

        # Tipo de producto (físico o servicio)
        product_type = product.get('product_type', 'physical')

        # Obtener el color del botón personalizado si existe
        button_color = product.get('button_color')

        # Aplicar estilo según el tipo de producto y si tiene color personalizado
        if button_color:
            # Usar color personalizado
            self.setStyleSheet(f"""
                #favorite_product_btn {{
                    background-color: {button_color};
                    border: 1px solid {self.get_border_color(button_color)};
                    border-radius: 5px;
                    min-height: 35px;
                }}
                #favorite_product_btn:hover {{
                    background-color: {self.get_hover_color(button_color)};
                    border: 1px solid {self.get_border_color(button_color)};
                }}
            """)
        elif product_type == 'service':
            self.setStyleSheet("""
                #favorite_product_btn {
                    background-color: #e3f2fd;
                    border: 1px solid #bbdefb;
                    border-radius: 5px;
                    min-height: 35px;
                }
                #favorite_product_btn:hover {
                    background-color: #bbdefb;
                    border: 1px solid #64b5f6;
                }
            """)
        else:
            self.setStyleSheet("""
                #favorite_product_btn {
                    background-color: #e3f2fd;
                    border: 1px solid #bbdefb;
                    border-radius: 5px;
                    min-height: 35px;
                }
                #favorite_product_btn:hover {
                    background-color: #bbdefb;
                    border: 1px solid #64b5f6;
                }
            """)

        # Diseño del contenido del botón
        btn_layout = QVBoxLayout(self)
        btn_layout.setContentsMargins(1, 1, 1, 1)
        btn_layout.setSpacing(0)
        btn_layout.setAlignment(Qt.AlignCenter)

        # Nombre del producto
        display_name = product.get('name', '')
        # Añadir ícono para servicios
        if product.get('product_type', '') == 'service':
            display_name = f"🛠️ {display_name}"

        self.name_label = QLabel(display_name)
        self.name_label.setObjectName("product_name")
        self.name_label.setAlignment(Qt.AlignCenter)
        self.name_label.setWordWrap(True)
        name_font = QFont()
        name_font.setPointSize(10)
        name_font.setBold(True)
        self.name_label.setFont(name_font)
        self.name_label.setStyleSheet("background-color: transparent;")

        # Precio del producto
        price_text = "∞" if product_type == 'service' else self.format_price_func(product.get('price', 0))
        self.price_label = QLabel(price_text)
        self.price_label.setObjectName("product_price")
        self.price_label.setAlignment(Qt.AlignCenter)
        price_font = QFont()
        price_font.setPointSize(9)
        self.price_label.setFont(price_font)

        # Color del precio según tipo de producto
        if product_type == 'service':
            self.price_label.setStyleSheet("background-color: transparent; color: #1976d2;")
        else:
            self.price_label.setStyleSheet("background-color: transparent; color: #475569;")

        # Añadir elementos al diseño
        btn_layout.addWidget(self.name_label)
        btn_layout.addWidget(self.price_label)

        # Punto inicial para el arrastre
        self.drag_start_position = None

        # تعيين قابلية قائمة السياق عند الضغط بالزر الأيمن
        self.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customContextMenuRequested.connect(self.show_context_menu)

        # إضافة tooltip للزر
        product_name = product.get('name', 'منتج')
        self.setToolTip(f"إضافة {product_name} للفاتورة")

    def get_border_color(self, background_color):
        """Obtener un color de borde adecuado basado en el color de fondo"""
        # Simplemente oscurecer el color de fondo para el borde
        return self.adjust_color_brightness(background_color, -30)

    def get_hover_color(self, background_color):
        """Obtener un color de hover adecuado basado en el color de fondo"""
        # Oscurecer el color de fondo para el efecto hover
        return self.adjust_color_brightness(background_color, -15)

    def adjust_color_brightness(self, color, amount):
        """Ajustar el brillo de un color hexadecimal"""
        if not color.startswith('#'):
            return color

        # Convertir color hex a RGB
        rgb_hex = color.lstrip('#')
        r, g, b = tuple(int(rgb_hex[i:i+2], 16) for i in (0, 2, 4))

        # Ajustar los componentes RGB
        r = max(0, min(255, r + amount))
        g = max(0, min(255, g + amount))
        b = max(0, min(255, b + amount))

        # Convertir de vuelta a hex
        return f'#{r:02x}{g:02x}{b:02x}'

    def mousePressEvent(self, event):
        """Capturar posición de inicio para arrastrar"""
        if event.button() == Qt.LeftButton:
            self.drag_start_position = event.pos()
        super().mousePressEvent(event)

    def mouseMoveEvent(self, event):
        """Iniciar arrastre si se ha movido lo suficiente"""
        if not (event.buttons() & Qt.LeftButton) or not self.drag_start_position:
            return

        # Comprobar si se ha movido lo suficiente para considerar un arrastre
        distance = (event.pos() - self.drag_start_position).manhattanLength()
        if distance < QApplication.startDragDistance():
            return

        # Crear el arrastre
        drag = QDrag(self)
        mime_data = QMimeData()

        # Guardar el ID del producto en los datos MIME
        product_id = str(self.product.get('id', ''))
        mime_data.setText(product_id)

        # Crear una imagen del botón para mostrar durante el arrastre
        pixmap = QPixmap(self.size())
        pixmap.fill(Qt.transparent)
        painter = QPainter(pixmap)
        self.render(painter)
        painter.end()

        drag.setMimeData(mime_data)
        drag.setPixmap(pixmap)
        drag.setHotSpot(event.pos())

        # Ejecutar el arrastre
        drop_action = drag.exec_(Qt.MoveAction)

    def dragEnterEvent(self, event):
        """Permitir el arrastre sobre el botón"""
        if event.mimeData().hasText():
            event.acceptProposedAction()

    def dropEvent(self, event):
        """Manejar la colocación de otro botón sobre este"""
        try:
            if event.mimeData().hasText():
                # Obtener el ID del producto que se está arrastrando
                source_product_id = int(event.mimeData().text())
                target_product_id = self.product.get('id', 0)

                # Sólo procesar si son diferentes
                if source_product_id != target_product_id:
                    # Buscar la vista de ventas (SalesView) en la jerarquía de widgets
                    sales_view = None
                    parent = self.parent()
                    print(f"Iniciando búsqueda de SalesView. Parent inmediato: {parent}")

                    while parent:
                        print(f"Verificando: {parent.__class__.__name__}")
                        if parent.__class__.__name__ == 'SalesView':
                            sales_view = parent
                            break
                        parent = parent.parent()

                    # Si encontramos la vista de ventas, llamar a su método swap_favorite_products
                    if sales_view:
                        print(f"SalesView encontrada: {sales_view}")
                        sales_view.swap_favorite_products(source_product_id, target_product_id)
                        event.acceptProposedAction()
                    else:
                        print("Error: No se encontró la vista de ventas en la jerarquía de widgets")
        except Exception as e:
            print(f"Error en dropEvent: {str(e)}")
            traceback.print_exc()
            event.acceptProposedAction()

    def show_context_menu(self, position):
        """عرض قائمة السياق للمنتج عند النقر بزر الماوس الأيمن"""
        try:
            # الحصول على معرف المنتج
            product_id = self.product.get('id', 0)
            product_name = self.product.get('name', '')

            if not product_id:
                return

            # إنشاء قائمة السياق
            context_menu = QMenu(self)
            context_menu.setLayoutDirection(Qt.RightToLeft)

            # تطبيق نمط مخصص للقائمة
            context_menu.setStyleSheet("""
                QMenu {
                    background-color: white;
                    border: 1px solid #cbd5e1;
                    border-radius: 6px;
                    padding: 5px;
                }

                QMenu::item {
                    padding: 8px 25px;
                    border-radius: 4px;
                    color: #333333;
                }

                QMenu::item:selected {
                    background-color: #3b82f6;
                    color: white;
                }

                QMenu::separator {
                    height: 1px;
                    background-color: #e2e8f0;
                    margin: 5px 0px;
                }
            """)

            # إضافة عنوان العنصر
            title_action = QAction(f"المنتج: {product_name}", self)
            title_action.setEnabled(False)
            title_font = title_action.font()
            title_font.setBold(True)
            title_action.setFont(title_font)
            context_menu.addAction(title_action)

            # إضافة فاصل
            context_menu.addSeparator()

            # إضافة إجراء تغيير لون الزر
            change_color_action = QAction("🎨 تغيير لون الزر", self)

            # إضافة إجراء إزالة من المفضلة
            remove_action = QAction("⭐ إزالة من المفضلة", self)

            # البحث عن الطبقة الأم SalesView لاستدعاء وظائف إدارة المنتجات المفضلة
            sales_view = None
            parent = self.parent()
            while parent:
                if parent.__class__.__name__ == 'SalesView':
                    sales_view = parent
                    break
                parent = parent.parent()

            if sales_view:
                # ربط إجراء تغيير اللون بدالة في SalesView
                change_color_action.triggered.connect(lambda: sales_view.change_favorite_button_color(product_id))
                context_menu.addAction(change_color_action)

                # ربط إجراء إزالة من المفضلة
                remove_action.triggered.connect(lambda: sales_view.remove_from_favorites(product_id))
                context_menu.addAction(remove_action)

                # عرض القائمة في موقع النقر
                context_menu.exec_(self.mapToGlobal(position))

        except Exception as e:
            print(f"خطأ في عرض قائمة السياق: {str(e)}")
            traceback.print_exc()

class SalesView(QWidget):
    def __init__(self):
        super().__init__()

        # Inicializar el atributo payment_method para evitar errores
        self.payment_method = "نقدي"  # Establecer "efectivo" como valor predeterminado

        # إعداد QSettings لحفظ إعدادات المستخدم
        self.settings = QSettings("MyCompany", "SmartManager")

        # إعداد التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(15, 10, 15, 15)  # تقليل الهوامش العلوية والجانبية
        main_layout.setSpacing(3)  # تقليل المسافات بين العناصر

        # إضافة عنوان الصفحة
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(0)

        header_widget = QWidget()
        header_widget.setStyleSheet("background-color: transparent; padding: 0; margin: 0;")
        header_widget.setMaximumHeight(35)  # تحديد حد أقصى لارتفاع العنوان
        header_widget.setLayout(header_layout)

        page_title = QLabel("المبيعات")
        page_title.setObjectName("page_title")
        page_title.setFont(QFont("Arial", 16, QFont.Bold))  # تقليل حجم الخط
        page_title.setStyleSheet("background-color: transparent; padding: 0; margin: 0;")
        page_title.setMaximumHeight(30)  # تحديد حد أقصى للارتفاع
        header_layout.addWidget(page_title)

        # إضافة مساحة فارغة لملء المكان الذي كان يشغله زر عملية بيع جديدة
        header_layout.addStretch()

        main_layout.addWidget(header_widget)

        # إضافة فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("content_separator")
        separator.setStyleSheet("margin-top: 0;")
        main_layout.addWidget(separator)

        # تقسيم الصفحة إلى قسمين: قسم الفاتورة وقسم الاختصارات باستخدام QSplitter
        content_splitter = QSplitter(Qt.Horizontal)
        content_splitter.setChildrenCollapsible(False)  # منع طي الأقسام بالكامل
        content_splitter.setHandleWidth(0)  # إخفاء مقبض السحب تماماً

        # تطبيق نمط لإخفاء مقبض السحب وتعطيل التفاعل معه
        content_splitter.setStyleSheet("""
            QSplitter::handle {
                background-color: transparent;
                border: none;
                width: 0px;
                height: 0px;
            }
            QSplitter::handle:hover {
                background-color: transparent;
            }
            QSplitter::handle:pressed {
                background-color: transparent;
            }
        """)

        # قسم الفاتورة (سيتم إضافته لاحقاً)
        invoice_widget = QWidget()
        invoice_widget.setObjectName("invoice_widget")
        invoice_layout = QVBoxLayout(invoice_widget)
        invoice_layout.setContentsMargins(10, 10, 10, 10)

        # عنوان قسم الفاتورة
        invoice_header = QHBoxLayout()
        invoice_title = QLabel("الفاتورة الحالية")
        invoice_title.setObjectName("section_title")
        invoice_title.setAlignment(Qt.AlignRight)
        invoice_title.setStyleSheet("background-color: transparent; padding: 0; margin: 0;")
        invoice_header.addWidget(invoice_title)

        # إضافة مساحة فارغة في مكان الأزرار المحذوفة
        invoice_header.addStretch()

        invoice_layout.addLayout(invoice_header)

        # إضافة فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("separator")
        invoice_layout.addWidget(separator)

        # إضافة حقل البحث عن المنتجات
        product_search_layout = QHBoxLayout()
        product_search_layout.setContentsMargins(5, 5, 5, 5)

        product_search_label = QLabel("بحث عن منتج:")
        product_search_label.setObjectName("field_label")
        product_search_label.setFixedWidth(90)
        product_search_label.setStyleSheet("background-color: transparent; padding: 0; margin: 0;")
        product_search_layout.addWidget(product_search_label)

        self.product_search_input = QLineEdit()
        self.product_search_input.setObjectName("search_input")
        self.product_search_input.setPlaceholderText("أدخل اسم المنتج أو الباركود...")
        self.product_search_input.setClearButtonEnabled(True)
        self.product_search_input.setAlignment(Qt.AlignCenter)

        # ربط مؤشر المفاتيح بالبحث
        self.product_search_input.returnPressed.connect(self.process_product_search)
        self.product_search_input.textChanged.connect(self.on_product_search_changed)

        product_search_layout.addWidget(self.product_search_input)
        invoice_layout.addLayout(product_search_layout)

        # إعداد جدول الفاتورة
        self.invoice_table = QTableWidget()
        self.invoice_table.setObjectName("invoice_table")
        self.invoice_table.setColumnCount(5)  # زيادة عدد الأعمدة إلى 5 لإضافة عمود المسلسل
        self.invoice_table.setRowCount(0)

        # تعيين خصائص الجدول
        self.invoice_table.setEditTriggers(QTableWidget.NoEditTriggers)
        self.invoice_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.invoice_table.setSelectionMode(QTableWidget.SingleSelection)

        # إخفاء عمود المسلسل الافتراضي
        self.invoice_table.verticalHeader().setVisible(False)

        # تعيين عناوين الأعمدة
        headers = ["#", "المنتج", "السعر", "الكمية", "الإجمالي"]
        self.invoice_table.setHorizontalHeaderLabels(headers)

        # تعيين عرض الأعمدة
        header = self.invoice_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.Fixed)  # عمود المسلسل
        header.setSectionResizeMode(1, QHeaderView.Stretch)  # عمود المنتج
        header.setSectionResizeMode(2, QHeaderView.Fixed)    # عمود السعر
        header.setSectionResizeMode(3, QHeaderView.Fixed)    # عمود الكمية
        header.setSectionResizeMode(4, QHeaderView.Fixed)    # عمود الإجمالي

        # تعيين عرض ثابت للأعمدة
        self.invoice_table.setColumnWidth(0, 40)   # عمود المسلسل
        self.invoice_table.setColumnWidth(2, 100)  # عمود السعر
        self.invoice_table.setColumnWidth(3, 80)   # عمود الكمية
        self.invoice_table.setColumnWidth(4, 100)  # عمود الإجمالي

        # تعيين اتجاه الجدول من اليمين إلى اليسار
        self.invoice_table.setLayoutDirection(Qt.RightToLeft)

        # تفعيل قائمة السياق
        self.invoice_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.invoice_table.customContextMenuRequested.connect(self.show_invoice_context_menu)

        # ربط حدث الضغط على المفاتيح بدالة مخصصة للتعامل مع مفتاح Delete
        self.invoice_table.keyPressEvent = self.handle_invoice_table_key_press

        invoice_layout.addWidget(self.invoice_table)

        # 2. إضافة قسم الدفع وإتمام البيع
        payment_frame = QFrame()
        payment_frame.setObjectName("payment_frame")
        payment_layout = QVBoxLayout(payment_frame)
        payment_layout.setContentsMargins(5, 5, 5, 5)  # تقليل الهوامش العلوية والسفلية
        payment_layout.setSpacing(8)  # تقليل المسافات بين العناصر

        # إضافة تخطيط أفقي يجمع بين إجمالي الفاتورة وقسم العميل
        top_section = QHBoxLayout()
        top_section.setSpacing(10)

        # إضافة إجمالي الفاتورة مباشرة إلى top_section
        total_label = QLabel("إجمالي الفاتورة:")
        total_label.setObjectName("grand_total_label")
        self.grand_total_value = QLabel("0.00 ج.م")
        self.grand_total_value.setObjectName("grand_total_value")
        self.grand_total_value.setAlignment(Qt.AlignLeft | Qt.AlignVCenter) # إضافة محاذاة عمودية للمركز
        total_label.setAlignment(Qt.AlignVCenter) # إضافة محاذاة عمودية للمركز

        top_section.addWidget(total_label)
        top_section.addWidget(self.grand_total_value)
        top_section.addStretch() # إضافة مساحة فارغة بعد القيمة لدفع العناصر إلى اليمين

        # إضافة قسم اختيار العميل في الجانب الأيسر
        customer_section = QHBoxLayout()
        customer_section.setSpacing(5)

        # إنشاء مجموعة متمركزة في الوسط
        customer_label = QLabel("العميل:")
        customer_label.setObjectName("field_label")
        customer_label.setFixedWidth(60)  # زيادة العرض من 45 إلى 60 لضمان ظهور النص بالكامل
        customer_label.setStyleSheet("""
            QLabel {
                color: #555;
                font-size: 12px;
                font-weight: bold;
            }
        """)
        customer_section.addWidget(customer_label)

        # قائمة منسدلة للعملاء
        self.customer_combo = RTLComboBox()
        self.customer_combo.setObjectName("combo_box")
        self.customer_combo.setFixedWidth(250)
        self.customer_combo.setMinimumHeight(25)  # تقليل الارتفاع
        self.customer_combo.setEditable(True)

        # تطبيق نمط البحث للكومبوبوكس مع إضافة علامة + في زر القائمة المنسدلة
        self.customer_combo.setStyleSheet("""
            QComboBox {
                background-color: #f0f0f0;
                border: 1px solid #a0a0a0;
                border-radius: 2px;
                padding: 5px 10px;
                font-size: 12px;
                font-weight: normal;
                color: #333;
                min-height: 25px;
            }
            QComboBox:hover {
                border: 1px solid #7a7a7a;
            }
            QComboBox:focus {
                border: 1px solid #0078d7;
            }
            QComboBox::drop-down {
                subcontrol-origin: padding;
                subcontrol-position: top left;
                width: 20px;
                border-left: none;
                background-color: transparent;
            }
            QComboBox::down-arrow {
                image: none;
                width: 0;
                height: 0;
                border-left: 5px solid transparent;
                border-right: 5px solid transparent;
                border-top: 5px solid #565656;
                margin-top: 3px;
                margin-left: 2px;
            }
            QComboBox QAbstractItemView {
                background-color: #f9f9f9;
                border: 1px solid #a0a0a0;
                border-radius: 0px;
                selection-background-color: #d8d8d8;
                selection-color: #333333;
                padding: 2px;
            }
            QComboBox QAbstractItemView::item {
                min-height: 20px;
                padding: 3px;
            }
            QComboBox QAbstractItemView::item:selected {
                background-color: #d8d8d8;
                color: #333333;
            }
            QComboBox QAbstractItemView::item:hover {
                background-color: #e0e0e0;
            }
            QComboBox QLineEdit {
                border: none;
                background: transparent;
                padding: 0;
                color: #333;
                font-weight: normal;
                selection-background-color: #a0c8fb;
                selection-color: #000000;
                text-align: center;
            }
        """)

        # توسيط النص في الكومبوبوكس
        self.customer_combo.lineEdit().setAlignment(Qt.AlignCenter)

        # إضافة الكومبوبوكس إلى قسم العميل
        customer_section.addWidget(self.customer_combo)

        # الحصول على العملاء من قاعدة البيانات
        self.load_customers_from_database()

        # تعيين اتجاه القائمة المنسدلة من اليمين إلى اليسار
        self.customer_combo.setLayoutDirection(Qt.RightToLeft)
        self.customer_combo.view().setLayoutDirection(Qt.RightToLeft)

        # إعداد مكمل تلقائي مخصص مع خاصية البحث المتقدم
        self.completer_model = QStringListModel()
        self.completer_model.setStringList(self.default_customers)

        # إنشاء نموذج وسيط للفلترة
        self.proxy_model = QSortFilterProxyModel()
        self.proxy_model.setSourceModel(self.completer_model)
        self.proxy_model.setFilterCaseSensitivity(Qt.CaseInsensitive)

        # إنشاء المكمل التلقائي
        self.completer = QCompleter(self.proxy_model)
        self.completer.setCompletionMode(QCompleter.PopupCompletion)
        self.completer.setCaseSensitivity(Qt.CaseInsensitive)
        self.completer.setFilterMode(Qt.MatchContains)  # للبحث في أي مكان من النص

        # ربط حدث اختيار عنصر من المكمل التلقائي
        self.completer.activated.connect(self.on_completer_activated)

        # تطبيق اتجاه اللغة العربية على نافذة المكمل التلقائي
        self.completer.popup().setLayoutDirection(Qt.RightToLeft)

        # تعيين المكمل التلقائي للكومبوبوكس
        self.customer_combo.setCompleter(self.completer)

        # ربط حدث تغيير النص بتحديث المكمل التلقائي
        self.customer_combo.lineEdit().textChanged.connect(self.update_completer_filter)

        # إضافة معالج حدث النقر لتحديد النص تلقائيًا
        self.customer_combo.lineEdit().installEventFilter(self)

        # تكوين الكومبوبوكس للبحث المباشر
        self.customer_combo.setInsertPolicy(QComboBox.NoInsert)

        # ربط إشارة الكومبوبوكس بوظيفة تغيير العميل
        self.customer_combo.currentIndexChanged.connect(self.customer_changed)

        top_section.addLayout(customer_section, 6)  # إعطاء وزن نسبي أكبر
        payment_layout.addLayout(top_section)

        # قسم أزرار الدفع
        payment_buttons = QHBoxLayout()
        payment_buttons.setSpacing(8)

        payment_buttons.addStretch()  # إضافة مساحة فارغة لدفع الأزرار إلى اليسار

        # إضافة أزرار الدفع
        self.process_invoice_btn = QPushButton("💵 دفع نقدي")
        self.process_invoice_btn.setObjectName("action_button")
        self.process_invoice_btn.setCursor(QCursor(Qt.PointingHandCursor))
        self.process_invoice_btn.setMinimumHeight(35)  # تقليل الارتفاع
        self.process_invoice_btn.setFixedWidth(120)  # تحديد عرض ثابت للزر
        self.process_invoice_btn.setToolTip("دفع نقدي سريع (Ctrl+Enter)")  # إضافة تلميح الاختصار

        later_payment_btn = QPushButton("📆 دفع آجل")
        later_payment_btn.setObjectName("action_button")  # تعديل لاستخدام نفس نمط الزر الأول
        later_payment_btn.setCursor(QCursor(Qt.PointingHandCursor))
        later_payment_btn.setMinimumHeight(35)  # تقليل الارتفاع
        later_payment_btn.setFixedWidth(120)  # تحديد عرض ثابت للزر
        later_payment_btn.setToolTip("تسجيل الفاتورة كدين على العميل")

        # ربط إشارات أزرار الدفع بالوظائف
        self.process_invoice_btn.clicked.connect(lambda: self.process_payment("نقدي"))
        later_payment_btn.clicked.connect(lambda: self.process_payment("آجل"))

        payment_buttons.addWidget(self.process_invoice_btn)
        payment_buttons.addWidget(later_payment_btn)
        # payment_buttons.addStretch()  # إزالة المساحة الفارغة من هنا

        # إضافة أزرار الدفع إلى قسم الدفع
        payment_layout.addLayout(payment_buttons)

        invoice_layout.addWidget(payment_frame)

        # قسم اختصارات المنتجات المفضلة
        settings = QSettings("MyCompany", "SmartManager")
        show_favorites = settings.value("show_favorites_section_in_sales", True)
        if isinstance(show_favorites, str):
            show_favorites = show_favorites.lower() == 'true'
        favorites_widget = None
        if show_favorites:
            favorites_widget = self.add_favorite_products()

        # إنشاء حاوية للقسم الأيمن (المفضلة فقط)
        right_section = QWidget()
        right_section.setObjectName('right_section')
        right_layout = QHBoxLayout(right_section)
        right_layout.setContentsMargins(0, 0, 0, 0)
        right_layout.setSpacing(0)
        if favorites_widget is not None:
            favorites_widget.setFixedWidth(200)
            favorites_widget.setMinimumWidth(200)
            favorites_widget.setMaximumWidth(200)
            right_layout.addWidget(favorites_widget)

        # إضافة الأقسام إلى QSplitter
        content_splitter.addWidget(invoice_widget)
        content_splitter.addWidget(right_section)

        # تخزين مرجع للـ splitter و favorites_widget
        self.content_splitter = content_splitter
        self.favorites_widget = favorites_widget

        # تعيين النسب الافتراضية: قسم الفاتورة قابل للتمدد، قسم المفضلة ثابت
        content_splitter.setStretchFactor(0, 1)  # قسم الفاتورة قابل للتمدد
        content_splitter.setStretchFactor(1, 0)  # قسم المفضلة بحجم ثابت

        main_layout.addWidget(content_splitter, 1)  # إضافة عامل تمدد 1 لجعل QSplitter يأخذ كامل المساحة المتاحة

        # تطبيق الأنماط
        self.apply_styles()

        # تعيين حدث تغيير حجم النافذة
        self.installEventFilter(self)

        # تم إزالة اختصار F12 المحلي لتجنب التداخل مع الاختصار العام في النافذة الرئيسية

        # إعداد اختصار Ctrl+Enter للدفع النقدي
        self.cash_payment_shortcut = QShortcut(QKeySequence("Ctrl+Return"), self)
        self.cash_payment_shortcut.activated.connect(self.quick_cash_payment)

    def eventFilter(self, obj, event):
        """فلترة الأحداث للتعامل مع تغيير حجم النافذة ونقرات الماوس"""
        if obj == self and event.type() == 14:  # 14 هو رقم نوع حدث تغيير الحجم (QEvent.Resize)
            pass  # يتم التعامل مع تغيير الحجم داخل المنتجات المفضلة

        # التحقق مما إذا كان الحدث هو نقرة ماوس على حقل إدخال الكومبوبوكس
        if obj == self.customer_combo.lineEdit() and event.type() == 2:  # 2 هو QEvent.MouseButtonPress
            # تحديد كل النص في حقل الإدخال
            QTimer.singleShot(0, lambda: self.customer_combo.lineEdit().selectAll())

        return super().eventFilter(obj, event)

    def showEvent(self, event):
        """تفعيل خانة البحث عند عرض التاب"""
        super().showEvent(event)
        # تأخير قصير لضمان اكتمال عرض الواجهة قبل تفعيل التركيز
        QTimer.singleShot(100, self.focus_search_input)

    def focus_search_input(self):
        """تفعيل التركيز على خانة البحث"""
        try:
            if hasattr(self, 'product_search_input') and self.product_search_input:
                # التأكد من أن الحقل مرئي ومفعل
                if self.product_search_input.isVisible() and self.product_search_input.isEnabled():
                    self.product_search_input.setFocus()
                    self.product_search_input.selectAll()
                    print("تم تنشيط شريط البحث بنجاح")
                else:
                    print("شريط البحث غير مرئي أو غير مفعل")
            else:
                print("شريط البحث غير موجود")
        except Exception as e:
            print(f"خطأ في تفعيل خانة البحث: {str(e)}")

    def quick_cash_payment(self):
        """تنفيذ الدفع النقدي السريع باستخدام اختصار Ctrl+Enter"""
        try:
            # التحقق من وجود منتجات في الفاتورة
            if self.invoice_table.rowCount() == 0:
                # تفعيل خانة البحث إذا لم تكن هناك منتجات
                self.focus_search_input()
                return

            # تنفيذ الدفع النقدي
            self.process_payment("نقدي")
        except Exception as e:
            print(f"خطأ في الدفع النقدي السريع: {str(e)}")



    def apply_styles(self):
        """تطبيق أنماط CSS على العناصر"""
        styles = """
            QWidget {
                background-color: #f9f9f9;
                font-family: 'Readex Pro', 'Tahoma', 'Arial';
            }

            #main_container {
                background-color: #ffffff;
            }

            #page_title {
                color: #333;
                font-size: 24px;
                padding: 10px;
                font-weight: bold;
            }

            #section_title {
                color: #555;
                font-size: 14px;
                font-weight: bold;
                margin-bottom: 8px;
            }

            #invoice_widget {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 8px;
            }

            #favorites_widget {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 8px;
                padding: 8px;
            }

            #favorites_scroll {
                border: none;
                background-color: transparent;
            }

            #favorite_product_btn {
                background-color: #e3f2fd;
                border: 1px solid #bbdefb;
                border-radius: 6px;
                padding: 2px;
                margin: 2px;
                text-align: center;
                min-height: 35px;
            }

            #favorite_product_btn:hover {
                background-color: #bbdefb;
                border: 1px solid #64b5f6;
            }

            #favorite_product_btn QLabel {
                background-color: transparent;
            }

            #product_name {
                color: #333;
                font-size: 10px;
                font-weight: bold;
                margin: 0px;
                padding: 1px;
                background-color: transparent;
            }

            #product_price {
                color: #d32f2f;
                font-size: 9px;
                margin: 0px;
                padding: 1px;
                background-color: transparent;
            }

            #mini_button {
                background-color: #f8f8f8;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 3px 8px;
                font-size: 11px;
                max-height: 24px;
                margin-top: 5px;
            }

            #mini_button:hover {
                background-color: #eaeaea;
            }

            #action_button {
                background-color: #4285f4;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 13px;
            }

            #action_button:hover {
                background-color: #3367d6;
            }

            #secondary_button {
                background-color: white;
                color: #4285f4;
                border: 1px solid #4282f6;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 13px;
            }

            #secondary_button:hover {
                background-color: #f0f7ff;
            }

            #danger_button {
                background-color: white;
                color: #ea4335;
                border: 1px solid #ea4335;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 13px;
            }

            #danger_button:hover {
                background-color: #fef0f0;
            }

            #placeholder_content {
                color: #999;
                font-style: italic;
            }

            #totals_frame, #payment_frame {
                background-color: #f9f9f9;
                border: 1px solid #ddd;
                border-radius: 6px;
                padding: 10px;
                margin-top: 10px;
            }

            #total_label {
                color: #555;
                font-size: 12px;
            }

            #total_value {
                color: #333;
                font-size: 12px;
                font-weight: bold;
            }

            #grand_total_label {
                color: #333;
                font-size: 13px;  /* تصغير حجم خط التسمية */
                font-weight: bold;
            }

            #grand_total_value {
                color: #ea4335;
                font-size: 13px;  /* تعديل حجم خط القيمة ليتطابق مع التسمية */
                font-weight: bold;
            }

            #invoice_table {
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                background-color: white;
                font-size: 13px;
                gridline-color: #e2e8f0;
            }

            #invoice_table QHeaderView::section {
                background-color: #f1f5f9;
                color: #475569;
                font-weight: bold;
                border: none;
                padding: 10px;
                border-bottom: 1px solid #cbd5e1;
                border-right: 1px solid #e2e8f0;
            }

            #invoice_table QHeaderView::section:first {
                border-top-right-radius: 6px;
            }

            #invoice_table QHeaderView::section:last {
                border-top-left-radius: 6px;
                border-right: none;
            }

            #invoice_table::item {
                padding: 8px;
                background-color: #f8fafc;
                border-bottom: 1px solid #f1f5f9;
            }

            #invoice_table::item:selected {
                background-color: rgba(59, 130, 246, 0.15);
                color: #000000;
            }

            QScrollBar:vertical {
                border: none;
                background: #f0f0f0;
                width: 8px;
                margin: 0px;
            }

            QScrollBar::handle:vertical {
                background: #cdcdcd;
                min-height: 20px;
                border-radius: 4px;
            }

            QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
                height: 0px;
            }

            QScrollBar:horizontal {
                border: none;
                background: #f0f0f0;
                height: 8px;
                margin: 0px;
            }

            QScrollBar::handle:horizontal {
                background: #cdcdcd;
                min-width: 20px;
                border-radius: 4px;
            }

            QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
                width: 0px;
            }

            /* إزالة الإطار المنقط من جميع العناصر */
            *:focus {
                outline: none !important;
            }
        """
        self.setStyleSheet(styles)

    def format_price(self, price):
        """تنسيق سعر المنتج"""
        return f"{price:.2f} ج.م"

    def add_favorite_products(self):
        """إضافة قسم المنتجات المفضلة مع دعم التكيف مع حجم الشاشة"""
        # إنشاء حاوية للمنتجات المفضلة
        favorites_widget = QWidget()
        favorites_widget.setObjectName("favorites_widget")
        favorites_layout = QVBoxLayout(favorites_widget)
        favorites_layout.setContentsMargins(5, 5, 5, 5)
        favorites_layout.setSpacing(2)

        # إنشاء header للعنوان وزر الإخفاء/الإظهار
        header_layout = QHBoxLayout()
        header_layout.setContentsMargins(0, 0, 0, 0)
        header_layout.setSpacing(5)

        # إضافة عنوان القسم
        section_title = QLabel("المنتجات المفضلة")
        section_title.setObjectName("section_title")
        section_title.setAlignment(Qt.AlignRight)
        header_layout.addWidget(section_title)

        # إضافة مساحة فارغة لدفع العنوان
        header_layout.addStretch()

        # إنشاء زر الإخفاء/الإظهار


        # إضافة header إلى التخطيط الرئيسي
        favorites_layout.addLayout(header_layout)

        # إنشاء حاوية مباشرة للأزرار بدون تمرير
        self.favorites_content = QWidget()
        self.buttons_layout = QGridLayout(self.favorites_content)
        self.buttons_layout.setContentsMargins(1, 1, 1, 1)
        self.buttons_layout.setSpacing(3)
        self.buttons_layout.setAlignment(Qt.AlignTop)  # تعيين المحاذاة للأعلى

        # جلب المنتجات المفضلة من قاعدة البيانات (بدلاً من البيانات التجريبية)
        self.load_favorite_products()

        # عدد الأعمدة الابتدائي - عمود واحد فقط
        self.num_columns = 1
        self.product_buttons = []

        # تحديد عدد الصفوف الأقصى في العمود
        max_rows_per_column = 12  # زيادة العدد الأقصى إلى 12 زر

        # إضافة أزرار للمنتجات المفضلة (الحد الأقصى 12 زر)
        for i, product in enumerate(self.favorite_products[:12]):  # تحديد العدد إلى 12 فقط
            # حساب الصف - عمود واحد فقط
            col = 0  # عمود واحد فقط
            row = i  # الصف حسب ترتيب المنتج

            # إنشاء زر المنتج قابل للسحب والإفلات مع دالة تنسيق السعر
            product_btn = DraggableProductButton(product, self.format_price, self)

            # إضافة الزر إلى الشبكة
            self.buttons_layout.addWidget(product_btn, row, col)
            self.product_buttons.append(product_btn)

            # ربط الزر بوظيفة إضافة المنتج إلى الفاتورة
            product_btn.clicked.connect(lambda checked, p=product: self.add_product_to_invoice(p))

        # ضبط سياسات الحجم للحاوية
        self.favorites_content.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        self.favorites_content.setStyleSheet("""
            QWidget {
                border: none;
                background-color: transparent;
            }
        """)

        favorites_layout.addWidget(self.favorites_content)

        # إزالة المؤقت لضمان التحديث الفوري

        # تخزين حاوية الأزرار للاستخدام في تغيير الحجم
        self.buttons_container = self.favorites_content

        # متغيرات لتتبع آخر أبعاد تم تطبيقها لتجنب التحديث المستمر
        self.last_applied_height = 0
        self.last_applied_width = 0
        self.default_button_height = 50  # الارتفاع الافتراضي للأزرار

        # دالة معالجة تغيير الحجم
        def handle_resize(event):
            # تحديث فوري بدون مؤقت لضمان الاستجابة السريعة
            if hasattr(self, 'resize_buttons_grid'):
                self.resize_buttons_grid()
            QWidget.resizeEvent(self.favorites_content, event)

        # ربط وظيفة إعادة الترتيب بتغيير حجم الحاوية
        self.favorites_content.resizeEvent = handle_resize

        # استدعاء وظيفة تغيير الحجم فوراً للتطبيق الأولي
        QTimer.singleShot(10, self.resize_buttons_grid)

        return favorites_widget

    def load_favorite_products(self):
        """تحميل المنتجات المفضلة من قاعدة البيانات"""
        try:
            # تحميل المنتجات المفضلة مباشرة
            db_favorites = ProductModel.get_favorite_products()
            self.on_favorites_loaded(db_favorites)

        except Exception as e:
            print(f"خطأ في تحميل المنتجات المفضلة: {str(e)}")
            self.favorite_products = []
            self.populate_favorite_products()

    def on_favorites_loaded(self, favorites):
        """معالجة المنتجات المفضلة بعد تحميلها"""
        try:
            # إزالة رسالة التحميل
            if hasattr(self, 'loading_label'):
                self.loading_label.deleteLater()

            if favorites and len(favorites) > 0:
                self.favorite_products = favorites
            else:
                self.favorite_products = []

            # تحديث العرض
            self.refresh_favorites_display(reload_data=False)

        except Exception as e:
            print(f"خطأ في معالجة المنتجات المفضلة: {str(e)}")

    def on_favorites_error(self, error_msg):
        """معالجة خطأ تحميل المنتجات المفضلة"""
        print(f"خطأ في تحميل المنتجات المفضلة: {error_msg}")
        self.favorite_products = []

        # إزالة رسالة التحميل وعرض رسالة الخطأ
        if hasattr(self, 'loading_label'):
            self.loading_label.setText("فشل تحميل المنتجات المفضلة")
            self.loading_label.setStyleSheet("color: #e53e3e; font-style: italic;")

    def on_loading_status(self, status):
        """تحديث حالة التحميل"""
        if hasattr(self, 'loading_label'):
            self.loading_label.setText(status)

    def get_product_icon(self, category):
        """تحديد رمز (إيموجي) مناسب لفئة المنتج"""
        # تعيين رمز افتراضي حسب التصنيف
        category_icons = {
            "مشروبات": "🥤",
            "مشروبات ساخنة": "☕",
            "وجبات خفيفة": "🍟",
            "حلويات": "🍫",
            "أدوات مكتبية": "📝",
            "إلكترونيات": "📱",
            "منظفات": "🧼",
            "طعام": "🍽️",
            "ملابس": "👕",
            "أحذية": "👞",
            "اكسسوارات": "👑",
            "مستلزمات منزلية": "🏠",
        }

        # الرجوع إلى رمز الفئة إذا كان موجودًا، وإلا استخدام رمز عام
        return category_icons.get(category, "📦")

    def refresh_favorites_display(self, reload_data=True):
        """تحديث عرض المنتجات المفضلة

        Args:
            reload_data (bool): إذا كان True، سيتم إعادة تحميل البيانات من قاعدة البيانات
                               إذا كان False، سيتم استخدام البيانات الموجودة في self.favorite_products
        """
        # إزالة جميع الأزرار من التخطيط
        for btn in self.product_buttons:
            self.buttons_layout.removeWidget(btn)
            btn.deleteLater()

        # إعادة تحميل المنتجات المفضلة إذا طلب ذلك
        self.product_buttons = []
        if reload_data:
            self.load_favorite_products()

        # التأكد من تعيين محاذاة التخطيط للأعلى
        self.buttons_layout.setAlignment(Qt.AlignTop)

        # التحقق مما إذا كانت المفضلة فارغة
        if not self.favorite_products:
            # إنشاء رسالة توضيحية
            empty_label = QLabel("لا توجد منتجات مفضلة.\n\nيمكنك إضافة منتجات إلى المفضلة\nمن صفحة المنتجات.")
            empty_label.setAlignment(Qt.AlignTop | Qt.AlignHCenter)
            empty_label.setStyleSheet("color: #64748b; font-size: 14px; background-color: transparent; padding-top: 20px;")
            empty_label.setWordWrap(True)

            # إضافة الرسالة إلى التخطيط
            self.buttons_layout.addWidget(empty_label, 0, 0, 1, self.num_columns)
            self.buttons_layout.setAlignment(empty_label, Qt.AlignTop)
            self.product_buttons.append(empty_label)
            return

        # تحديد عدد الصفوف الأقصى في العمود
        max_rows_per_column = 12  # زيادة العدد الأقصى إلى 12 زر

        # إعادة إنشاء الأزرار في عمود واحد (الحد الأقصى 12 زر)
        for i, product in enumerate(self.favorite_products[:12]):  # تحديد العدد إلى 12 فقط
            # حساب الصف - عمود واحد فقط
            col = 0  # عمود واحد فقط
            row = i  # الصف حسب ترتيب المنتج

            # إنشاء زر المنتج قابل للسحب والإفلات مع دالة تنسيق السعر
            product_btn = DraggableProductButton(product, self.format_price, self)

            # إضافة الزر إلى الشبكة
            self.buttons_layout.addWidget(product_btn, row, col)
            self.product_buttons.append(product_btn)

            # ربط الزر بوظيفة إضافة المنتج إلى الفاتورة
            product_btn.clicked.connect(lambda checked, p=product: self.add_product_to_invoice(p))

        # تحديث التخطيط
        self.resize_buttons_grid()

    def resize_buttons_grid(self):
        """إعادة ترتيب أزرار المنتجات المفضلة عند تغيير الحجم"""
        if not hasattr(self, 'buttons_container') or not hasattr(self, 'product_buttons'):
            return

        # تجنب التحديث إذا لم تكن الصفحة مرئية (توفير الموارد)
        if not self.isVisible():
            return

        current_width = self.buttons_container.width()
        current_height = self.buttons_container.height()

        # تجنب التحديث إذا كانت الأبعاد صغيرة جداً (النافذة مصغرة)
        if current_width < 50 or current_height < 50:
            return

        # تعديل: جعل عدد الأعمدة دائما 1 فقط
        new_columns = 1

        # تعديل حجم الأزرار بناءً على عرض المنطقة
        if current_width < 180:
            name_size = 9
            price_size = 8
        else:
            name_size = 10
            price_size = 9

        # تحديد عدد الصفوف الأقصى في العمود
        max_rows_per_column = 12  # زيادة العدد الأقصى إلى 12 زر

        # إعادة ترتيب الأزرار فقط إذا تغير عدد الأعمدة
        if new_columns != self.num_columns:
            self.num_columns = new_columns

            # إزالة جميع الأزرار من التخطيط (باستثناء رسالة "لا توجد منتجات مفضلة")
            buttons_to_rearrange = []
            for btn in self.product_buttons:
                # نتحقق ما إذا كان الزر هو رسالة "لا توجد منتجات مفضلة"
                if not isinstance(btn, QLabel) or "لا توجد منتجات مفضلة" not in btn.text():
                    self.buttons_layout.removeWidget(btn)
                    buttons_to_rearrange.append(btn)

            # إعادة إضافة الأزرار في عمود واحد
            for i, btn in enumerate(buttons_to_rearrange):
                # حساب الصف - عمود واحد فقط
                col = 0  # عمود واحد فقط
                row = i  # الصف حسب ترتيب الزر

                self.buttons_layout.addWidget(btn, row, col)

        # حساب ارتفاع الزر الجديد بناءً على المساحة المتاحة
        new_button_height = self.calculate_button_height(current_height, max_rows_per_column)

        # التحقق من تغيير ارتفاع الزر المطلوب (هذا هو الأهم)
        current_button_height = self.product_buttons[0].height() if self.product_buttons and not isinstance(self.product_buttons[0], QLabel) else self.default_button_height
        button_height_changed = abs(new_button_height - current_button_height) > 2

        # التحقق من تغيير الأبعاد
        height_changed = abs(current_height - self.last_applied_height) > 3
        width_changed = abs(current_width - self.last_applied_width) > 3

        # فحص خاص للتصغير (عندما يكون الارتفاع الجديد أصغر من الحالي)
        is_shrinking = new_button_height < current_button_height

        # تحديث فوري إذا كان هناك أي تغيير، خاصة عند التصغير
        if button_height_changed or height_changed or width_changed or is_shrinking:
            # حفظ الأبعاد الحالية
            self.last_applied_height = current_height
            self.last_applied_width = current_width

            # تحديث حجم الخط للعناصر الداخلية بناءً على ارتفاع الزر
            font_size_multiplier = max(0.8, min(1.2, new_button_height / self.default_button_height))

            # إنشاء الخطوط مرة واحدة لتحسين الأداء
            name_font = QFont()
            name_font.setPointSize(int(name_size * font_size_multiplier))
            name_font.setBold(True)

            price_font = QFont()
            price_font.setPointSize(int(price_size * font_size_multiplier))

            # تعديل حجم الأزرار والخط لتناسب العرض والارتفاع الجديد
            for btn in self.product_buttons:
                # نتخطى رسالة "لا توجد منتجات مفضلة"
                if isinstance(btn, QLabel):
                    continue

                # ضبط حجم الزر ليأخذ كامل المساحة المتاحة
                btn.setFixedHeight(new_button_height)

                # تطبيق الخطوط المحضرة مسبقاً
                layout = btn.layout()
                if layout is not None:
                    for i in range(layout.count()):
                        widget = layout.itemAt(i).widget()
                        if widget:
                            if i == 0:  # اسم المنتج
                                widget.setFont(name_font)
                            elif i == 1:  # سعر المنتج
                                widget.setFont(price_font)

            # إجبار تحديث التخطيط
            self.buttons_container.updateGeometry()
            self.buttons_layout.update()

    def calculate_button_height(self, available_height, max_rows_per_column):
        """حساب ارتفاع الزر المناسب بناءً على المساحة المتاحة (محسن للأداء)"""
        # حساب المساحة القابلة للاستخدام (مع ترك مساحة للهوامش والتباعد)
        usable_height = available_height - 40  # ترك مساحة للهوامش

        # حساب الارتفاع المثالي للزر
        calculated_height = usable_height // max_rows_per_column

        # تطبيق حدود دنيا وعليا للارتفاع
        min_height = 30  # الحد الأدنى
        max_height = 80  # الحد الأقصى

        # إرجاع الارتفاع المحسوب مع تطبيق الحدود
        return max(min_height, min(calculated_height, max_height))

    def reset_buttons_to_default_size(self):
        """إعادة تعيين جميع الأزرار إلى الحجم الافتراضي بسرعة"""
        if not hasattr(self, 'product_buttons'):
            return

        # إنشاء الخطوط مرة واحدة لتحسين الأداء
        name_font = QFont()
        name_font.setPointSize(10)
        name_font.setBold(True)

        price_font = QFont()
        price_font.setPointSize(9)

        for btn in self.product_buttons:
            # نتخطى رسالة "لا توجد منتجات مفضلة"
            if isinstance(btn, QLabel):
                continue

            # إعادة تعيين الارتفاع إلى القيمة الافتراضية
            btn.setFixedHeight(self.default_button_height)

            # إعادة تعيين حجم الخط إلى القيمة الافتراضية
            layout = btn.layout()
            if layout is not None:
                for i in range(layout.count()):
                    widget = layout.itemAt(i).widget()
                    if widget:
                        if i == 0:  # اسم المنتج
                            widget.setFont(name_font)
                        elif i == 1:  # سعر المنتج
                            widget.setFont(price_font)

    def add_product_to_invoice(self, product):
        """إضافة منتج إلى الفاتورة"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية إضافة منتج للفاتورة
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "إضافة منتج للفاتورة", show_message=True, parent_widget=self):
                    return

            # التحقق من أن المنتج ليس تصنيفًا
            if product.get('product_type') == 'category_placeholder':
                self.show_warning_dialog(
                    "تصنيف وليس منتج",
                    f"العنصر '{product.get('name')}' هو تصنيف وليس منتجًا قابلاً للإضافة إلى الفاتورة."
                )
                return
        except Exception as e:
            self.show_error_dialog("خطأ", f"حدث خطأ أثناء التحقق من الصلاحيات: {str(e)}")
            return

        # التحقق من نوع المنتج (منتج عادي أم خدمة)
        product_type = product.get('product_type', 'physical')
        product_name = product.get('name', '')
        product_id = product.get('id')
        price = product.get('price', 0)
        service_note = ""  # متغير لتخزين ملاحظة الخدمة

        # إذا كان المنتج خدمة، فسيتم عرض نافذة حوار لتعديل السعر
        if product_type == 'service':
            # عرض نافذة لتعديل سعر الخدمة
            dialog = ServicePriceDialog(self, product_name, 0)  # بدء السعر بصفر دائما
            if dialog.exec_() == QDialog.Accepted:
                # تحديث السعر الذي سيتم استخدامه في الفاتورة
                price = dialog.get_price()
                # الحصول على الملاحظة
                service_note = dialog.get_note()
            else:
                # المستخدم ألغى العملية
                return

        # التحقق من وجود المنتج في الجدول وزيادة الكمية إذا كان موجودًا
        product_found = False

        for row in range(self.invoice_table.rowCount()):
            if self.invoice_table.item(row, 1).text() == product_name:
                # المنتج موجود بالفعل، نزيد الكمية (فقط للمنتجات العادية، الخدمات تضاف كصف منفصل)
                if product_type == 'physical':
                    # التحقق من توفر المخزون - الحصول على الكمية الحالية من قاعدة البيانات
                    available_quantity = 0
                    if product_id:
                        # الحصول على بيانات المنتج الحالية من قاعدة البيانات
                        current_product = ProductModel.get_product_by_id(product_id)
                        if current_product:
                            available_quantity = current_product.get('quantity', 0) or current_product.get('stock', 0)
                    else:
                        # إذا لم يكن لدينا معرف المنتج، استخدم البيانات المُمررة
                        available_quantity = product.get('quantity', 0) or product.get('stock', 0)

                    current_quantity = int(self.invoice_table.item(row, 3).text())
                    new_quantity = current_quantity + 1

                    # إذا كانت الكمية الجديدة أكبر من المتوفرة
                    if new_quantity > available_quantity:
                        self.show_warning_dialog(
                            "خطأ في المخزون",
                            f"الكمية المتوفرة في المخزون ({available_quantity}) أقل من الكمية المطلوبة ({new_quantity}).\n\nيرجى تقليل الكمية أو تحديث مخزون المنتج."
                        )
                        return

                    # زيادة الكمية
                    quantity_item = self.invoice_table.item(row, 3)
                    quantity_item.setText(str(new_quantity))

                    # تحديث الإجمالي
                    total = price * new_quantity
                    total_item = self.invoice_table.item(row, 4)
                    total_item.setText(self.format_price(total))

                    # تأكيد أن معرف المنتج محفوظ
                    name_item = self.invoice_table.item(row, 1)
                    if not name_item.data(Qt.UserRole) and product_id:
                        name_item.setData(Qt.UserRole, product_id)

                    product_found = True
                    break
                else:
                    # للخدمات، نضيف صف جديد لكل خدمة
                    product_found = False
                    break

        # إذا لم يكن المنتج موجوداً أو كان خدمة، نضيفه للفاتورة
        if not product_found:
            # التحقق من توفر المخزون للمنتجات العادية
            if product_type == 'physical':
                # تحقق من وجود معلومات المخزون، سواء في 'quantity' أو 'stock'
                available_quantity = 0
                if 'quantity' in product:
                    available_quantity = product['quantity']
                elif 'stock' in product:
                    available_quantity = product['stock']

                if available_quantity < 1:
                    self.show_warning_dialog(
                        "خطأ في المخزون",
                        f"المنتج '{product_name}' غير متوفر في المخزون.\n\nيرجى تحديث مخزون المنتج أولاً."
                    )
                    return

                # ضمان اتساق البيانات بين 'quantity' و 'stock'
                product['quantity'] = available_quantity
                product['stock'] = available_quantity

            row = self.invoice_table.rowCount()
            self.invoice_table.insertRow(row)

            # إنشاء عناصر الصف الجديد

            # المسلسل (الرقم)
            serial_number = QTableWidgetItem(str(row + 1))
            serial_number.setToolTip(str(row + 1))
            self.invoice_table.setItem(row, 0, serial_number)

            # اسم المنتج/الخدمة (إضافة رمز مميز للخدمات)
            display_name = product_name
            if product_type == 'service':
                # إضافة الملاحظة إلى اسم الخدمة إذا كانت موجودة
                if service_note:
                    display_name = f"{product_name} ({service_note}) 🛠️"
                else:
                    display_name = f"{product_name} 🛠️"  # إضافة رمز للخدمات

            item_name = QTableWidgetItem(display_name)
            item_name.setToolTip(display_name)
            self.invoice_table.setItem(row, 1, item_name)

            # حفظ معرف المنتج في العنصر للاستخدام لاحقاً
            if product_id:
                item_name.setData(Qt.UserRole, product_id)
            # حفظ نوع المنتج في العنصر
            item_name.setData(Qt.UserRole + 1, product_type)
            # حفظ ملاحظة الخدمة في العنصر إذا كانت متوفرة
            if service_note:
                item_name.setData(Qt.UserRole + 2, service_note)
            # توسيط النص في خلية اسم المنتج
            item_name.setTextAlignment(Qt.AlignCenter)

            # سعر المنتج
            item_price = QTableWidgetItem(self.format_price(price))
            item_price.setToolTip(self.format_price(price))
            self.invoice_table.setItem(row, 2, item_price)

            # الكمية
            item_quantity = QTableWidgetItem("1")
            item_quantity.setToolTip("1")
            self.invoice_table.setItem(row, 3, item_quantity)

            # الإجمالي
            item_total = QTableWidgetItem(self.format_price(price))
            item_total.setToolTip(self.format_price(price))
            self.invoice_table.setItem(row, 4, item_total)

            # تطبيق لون مميز لصفوف الخدمات
            if product_type == 'service':
                # تلوين خلية المسلسل
                serial_number.setBackground(QColor('#f0f7ff'))

                # تلوين الخلية الأولى (اسم المنتج)
                item_name.setBackground(QColor('#f0f7ff'))

                # تلوين خلية السعر
                item_price.setBackground(QColor('#f0f7ff'))

                # تلوين خلية الكمية
                item_quantity.setBackground(QColor('#f0f7ff'))
                # تعطيل تعديل الكمية للخدمات
                item_quantity.setFlags(item_quantity.flags() & ~Qt.ItemIsEditable)

                # تلوين خلية الإجمالي
                item_total.setBackground(QColor('#f0f7ff'))

        # تحديث إجماليات الفاتورة
        self.calculate_totals()

        # تمرير الجدول إلى آخر صف
        self.invoice_table.scrollToBottom()

    def remove_product_from_invoice(self, row):
        """حذف منتج من الفاتورة"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية حذف منتج من الفاتورة
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "حذف منتج من الفاتورة", show_message=True, parent_widget=self):
                    return

            if 0 <= row < self.invoice_table.rowCount():
                # الحصول على اسم المنتج قبل الحذف
                product_name = self.invoice_table.item(row, 1).text()

                # حذف الصف
                self.invoice_table.removeRow(row)

                # إعادة ترقيم المسلسل للصفوف المتبقية
                for i in range(row, self.invoice_table.rowCount()):
                    serial_item = QTableWidgetItem(str(i + 1))
                    serial_item.setTextAlignment(Qt.AlignCenter)
                    self.invoice_table.setItem(i, 0, serial_item)

                    # إذا كان عنصر خدمة، نحافظ على لون الخلفية المميز
                    name_item = self.invoice_table.item(i, 1)
                    product_type = name_item.data(Qt.UserRole + 1) or 'physical'
                    if product_type == 'service':
                        serial_item.setBackground(QColor('#f0f7ff'))

                # تحديث إجماليات الفاتورة
                self.calculate_totals()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء محاولة حذف منتج من الفاتورة: {str(e)}")

            # إزالة رسالة التأكيد - تم حذفها

    def calculate_totals(self):
        """حساب إجماليات الفاتورة وتحديث العرض فقط"""
        total = 0.0

        # حساب المجموع من جميع بنود الفاتورة
        for row in range(self.invoice_table.rowCount()):
            total_text = self.invoice_table.item(row, 4).text()  # تعديل الفهرس من 3 إلى 4
            # استخراج الرقم من النص مثل "25.50 ج.م"
            try:
                total_value = float(total_text.split()[0])
                total += total_value
            except (ValueError, IndexError):
                continue

        # طباعة المبلغ الإجمالي لأغراض التصحيح
        print(f"إجمالي الفاتورة: {total} - عدد المنتجات: {self.invoice_table.rowCount()}")

        # تحديث إجمالي الفاتورة في واجهة المستخدم فقط
        self.grand_total_value.setText(self.format_price(total))



    def resizeEvent(self, event):
        """معالجة تغيير حجم النافذة لتحديث أزرار المنتجات المفضلة"""
        super().resizeEvent(event)
        # تحديث فوري لأزرار المنتجات المفضلة عند تغيير حجم النافذة
        if hasattr(self, 'resize_buttons_grid'):
            self.resize_buttons_grid()

    def update_buttons_state(self):
        """تحديث حالة تفعيل الأزرار بناءً على صلاحيات المستخدم"""
        # التحقق من وجود مستخدم حالي
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()

        if not main_window or not main_window.current_user:
            return

        user_id = main_window.current_user.get('id')
        username = main_window.current_user.get('username')

        # المستخدم admin له جميع الصلاحيات
        if username == 'admin':
            # تفعيل جميع الأزرار
            self.process_invoice_btn.setEnabled(True)
            self.process_invoice_btn.setStyleSheet("")

            # تفعيل جميع أزرار المنتجات المفضلة
            for btn in self.product_buttons:
                if not isinstance(btn, QLabel):  # تجاهل الرسائل النصية
                    btn.setEnabled(True)
                    btn.setStyleSheet(btn.styleSheet().replace("color: #999999;", ""))
            return

        # استيراد وحدة التحكم بالمستخدمين
        from controllers.user_controller import UserController

        # تحديث حالة زر إتمام البيع
        has_process_invoice_permission = UserController.check_permission(user_id, "إضافة عملية بيع")
        self.process_invoice_btn.setEnabled(has_process_invoice_permission)
        if not has_process_invoice_permission:
            self.process_invoice_btn.setStyleSheet("""
                background-color: #f0f0f0;
                color: #999999;
                border: 1px solid #cccccc;
            """)
        else:
            self.process_invoice_btn.setStyleSheet("")

        # تحديث حالة أزرار المنتجات المفضلة
        has_add_product_permission = UserController.check_permission(user_id, "إضافة منتج للفاتورة")
        for btn in self.product_buttons:
            if not isinstance(btn, QLabel):  # تجاهل الرسائل النصية
                btn.setEnabled(has_add_product_permission)
                if not has_add_product_permission:
                    # تعديل نمط الزر ليظهر معطلاً
                    current_style = btn.styleSheet()
                    if "color: #999999;" not in current_style:
                        btn.setStyleSheet(current_style + "\ncolor: #999999; background-color: #f0f0f0;")
                else:
                    # إعادة النمط الأصلي
                    btn.setStyleSheet(btn.styleSheet().replace("color: #999999;", "").replace("background-color: #f0f0f0;", ""))

    def process_invoice(self):
        """معالجة الفاتورة وحفظها في قاعدة البيانات"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية إتمام عملية البيع
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "إتمام عملية البيع", show_message=True, parent_widget=self):
                    return False

            total = 0.0

            # التحقق من وجود منتجات في الفاتورة
            if self.invoice_table.rowCount() == 0:
                self.show_warning_dialog(
                    "لا توجد منتجات",
                    "الرجاء إضافة منتجات إلى الفاتورة قبل إتمام عملية الدفع!"
                )
                return False
        except Exception as e:
            self.show_error_dialog("خطأ", f"حدث خطأ أثناء التحقق من الصلاحيات: {str(e)}")
            return False

        # حساب المجموع من جميع بنود الفاتورة
        for row in range(self.invoice_table.rowCount()):
            total_text = self.invoice_table.item(row, 4).text()
            try:
                total_value = float(total_text.split()[0])
                total += total_value
            except (ValueError, IndexError):
                continue

        # Mantenemos el valor del subtotal e impuestos para la base de datos
        subtotal = total
        tax = 0  # Ya no calculamos impuestos

        # تحديد حالة الفاتورة بناءً على طريقة الدفع
        status = "مدفوعة" if self.payment_method != "آجل" else "غير مدفوعة"

        # تحديد المبلغ المدفوع والمتبقي
        paid_amount = total if self.payment_method != "آجل" else 0
        remaining_amount = 0 if self.payment_method != "آجل" else total

        # جمع معلومات الفاتورة
        invoice_data = {
            'reference_number': InvoiceModel.generate_reference_number(),
            'customer_id': getattr(self, 'selected_customer_id', None),
            'date': DateTimeUtils.get_current_date_time(),
            'subtotal': subtotal,
            'tax': tax,
            'discount': 0,  # يمكن إضافة خصم في المستقبل
            'total': total,
            'paid_amount': paid_amount,
            'remaining_amount': remaining_amount,
            'payment_method': self.payment_method,
            'status': status,
            'notes': f"تم إنشاء الفاتورة عن طريق نافذة المبيعات - {getattr(self, 'selected_customer', 'عميل نقدي')}"
        }

        # طباعة معلومات العميل للتشخيص
        print(f"معرف العميل المحفوظ: {getattr(self, 'selected_customer_id', None)}")
        print(f"اسم العميل المحفوظ: {getattr(self, 'selected_customer', 'غير محدد')}")

        # جمع معلومات المنتجات
        items_data = []
        for row in range(self.invoice_table.rowCount()):
            product_name = self.invoice_table.item(row, 1).text()
            price_text = self.invoice_table.item(row, 2).text()
            quantity = int(self.invoice_table.item(row, 3).text())
            total_text = self.invoice_table.item(row, 4).text()

            # استخراج الأرقام من النصوص
            try:
                price = float(price_text.split()[0])
                total_price = float(total_text.split()[0])
            except (ValueError, IndexError):
                price = 0
                total_price = 0

            # الحصول على معرف المنتج ونوعه مباشرة من البيانات المخزنة في العنصر
            product_id = self.invoice_table.item(row, 1).data(Qt.UserRole)
            product_type = self.invoice_table.item(row, 1).data(Qt.UserRole + 1)

            # إذا كان المنتج خدمة، نستخرج الملاحظة المخزنة مع العنصر (إن وجدت)
            service_note = ""
            if product_type == 'service':
                service_note = self.invoice_table.item(row, 1).data(Qt.UserRole + 2) or ""

                # استخراج الملاحظة من اسم المنتج إذا لم تكن مخزنة (احتياطي)
                if not service_note and " (" in product_name and ")" in product_name:
                    # استخراج النص بين القوسين
                    note_start = product_name.find(" (") + 2
                    note_end = product_name.rfind(")")
                    if note_start < note_end:
                        service_note = product_name[note_start:note_end]

                # حذف الملاحظة والرمز من اسم المنتج قبل إرساله لقاعدة البيانات
                clean_name = product_name
                if " (" in clean_name and ")" in clean_name:
                    clean_name = clean_name[:clean_name.find(" (")]
                if "🛠️" in clean_name:
                    clean_name = clean_name.replace("🛠️", "").strip()

                # إذا كانت هناك ملاحظة، نضيفها للاسم في قاعدة البيانات بتنسيق مناسب
                if service_note:
                    product_name = f"{clean_name} ({service_note})"
                else:
                    product_name = clean_name

            # إذا كان معرف المنتج غير موجود، نبحث عنه في قاعدة البيانات
            product_code = ""
            if not product_id:
                products = ProductModel.search_products(product_name)
                product_id = products[0]['id'] if products and len(products) > 0 else None
                product_code = products[0]['code'] if products and len(products) > 0 else ""
            else:
                # إذا كان لدينا المعرف، نحصل على رمز المنتج من قاعدة البيانات
                product = ProductModel.get_product_by_id(product_id)
                product_code = product['code'] if product else ""

            item = {
                'product_id': product_id,
                'product_name': product_name,
                'product_code': product_code,
                'quantity': quantity,
                'unit_price': price,
                'total_price': total_price
            }
            items_data.append(item)

        # حفظ الفاتورة مباشرة
        try:
            # حفظ الفاتورة في قاعدة البيانات
            invoice_id = InvoiceModel.add_invoice(invoice_data, items_data)

            if invoice_id:
                # التحقق من إعداد الطباعة التلقائية قبل الطباعة
                from PyQt5.QtCore import QSettings
                settings = QSettings("MyCompany", "SmartManager")
                auto_print = settings.value("auto_print_invoice_after_sale", True)
                if isinstance(auto_print, str):
                    auto_print = auto_print.lower() == 'true'

                success_message = f"تم إنشاء الفاتورة رقم {invoice_data['reference_number']} بنجاح"

                if auto_print:
                    # طباعة الفاتورة مباشرة بدون معاينة
                    self.print_invoice_directly(invoice_data, items_data)
                    success_message += " وتم إرسالها للطباعة!"
                else:
                    success_message += "."

                # إظهار رسالة نجاح مع تنسيق الأزرار الزرقاء
                self.show_success_dialog(
                    "تم إتمام عملية البيع",
                    success_message
                )

                # مسح الفاتورة بعد إتمام العملية
                self.clear_invoice()

                # تحديث جميع الواجهات المتأثرة (الفواتير والمنتجات والعملاء)
                main_window = self.window()
                if hasattr(main_window, 'content_widget'):
                    # تحديث جميع الصفحات المتأثرة
                    from views.invoices import InvoicesView
                    from views.products import ProductsView
                    from views.customers import CustomersView

                    # البحث عن الصفحات في المكونات وتحديثها
                    for i in range(main_window.content_widget.count()):
                        widget = main_window.content_widget.widget(i)

                        # تحديث صفحة الفواتير
                        if isinstance(widget, InvoicesView):
                            widget.populate_invoices_table()

                        # تحديث صفحة المنتجات
                        elif isinstance(widget, ProductsView):
                            widget.populate_products_table()

                        # تحديث صفحة العملاء
                        elif isinstance(widget, CustomersView):
                            widget.refresh_customers_table()

                return True
            else:
                # إظهار رسالة خطأ
                self.show_error_dialog(
                    "خطأ في إنشاء الفاتورة",
                    "حدث خطأ أثناء محاولة حفظ الفاتورة في قاعدة البيانات.\nالرجاء المحاولة مرة أخرى."
                )
                return False
        except Exception as e:
            # إظهار رسالة خطأ مع التفاصيل
            self.show_error_dialog(
                "خطأ في إنشاء الفاتورة",
                f"حدث خطأ أثناء محاولة حفظ الفاتورة: {str(e)}"
            )
            return False

    def process_payment(self, payment_method):
        """معالجة عملية الدفع وإنشاء الفاتورة"""
        from PyQt5.QtWidgets import QMessageBox
        from views.customers import CustomerDialog
        from models.customers import CustomerModel

        # التحقق من وجود منتجات في الفاتورة
        if self.invoice_table.rowCount() == 0:
            self.show_warning_dialog(
                "لا توجد منتجات",
                "الرجاء إضافة منتجات إلى الفاتورة قبل إتمام عملية الدفع!"
            )
            return

        # الحصول على اسم العميل من النص الحالي في الكومبوكس مباشرة
        customer_text = self.customer_combo.lineEdit().text().strip()
        selected_customer = customer_text if customer_text else "عميل نقدي"

        print(f"العميل المحدد للدفع (من lineEdit): {selected_customer}")

        # منع الدفع الآجل للعميل النقدي
        if payment_method == "آجل" and selected_customer == "عميل نقدي":
            self.show_warning_dialog(
                "خطأ في طريقة الدفع",
                "لا يمكن استخدام الدفع الآجل مع العميل النقدي.\nالرجاء اختيار عميل آخر أو تغيير طريقة الدفع."
            )
            return

        # تحقق إذا كان العميل غير موجود في القائمة (وليس "عميل نقدي")
        if selected_customer != "عميل نقدي" and selected_customer not in self.default_customers:
            reply = show_question(
                self,
                "عميل غير مسجل",
                f"العميل '{selected_customer}' غير مسجل في النظام. هل تريد تسجيله الآن؟"
            )
            if reply:
                # فتح نافذة إضافة عميل جديد مع تعبئة الاسم
                dialog = CustomerDialog(self)
                dialog.name_input.setText(selected_customer)
                if dialog.exec_() == QDialog.Accepted:
                    customer_data = dialog.get_customer_data()
                    customer_id = CustomerModel.add_customer(customer_data)
                    if customer_id:
                        show_information(self, "تمت الإضافة", "تمت إضافة العميل بنجاح!")
                        # إعادة تحميل العملاء وتحديد العميل الجديد
                        self.load_customers_from_database(select_customer_name=customer_data['name'])
                        # سيتم تعيين selected_customer و selected_customer_id تلقائياً
                    else:
                        QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء إضافة العميل!")
                    return  # إيقاف الدفع لإعادة المحاولة بعد الإضافة
                else:
                    return  # المستخدم أغلق نافذة الإضافة
            else:
                return  # المستخدم رفض إضافة العميل

        # تخزين طريقة الدفع للاستخدام في وظيفة process_invoice
        self.payment_method = payment_method

        # حفظ الفاتورة في قاعدة البيانات
        self.process_invoice()

    def clear_invoice(self):
        """مسح محتويات الفاتورة الحالية"""
        # حذف جميع الصفوف من جدول الفاتورة
        self.invoice_table.setRowCount(0)

        # إعادة تعيين قيم الإجماليات
        self.grand_total_value.setText(self.format_price(0))

        # إعادة تعيين العميل إلى "عميل نقدي" بعد البيع
        if hasattr(self, 'default_customers') and "عميل نقدي" in self.default_customers:
            idx = self.default_customers.index("عميل نقدي")
            self.customer_combo.setCurrentIndex(idx)
            self.selected_customer = "عميل نقدي"
            self.selected_customer_id = None

        # عرض رسالة تأكيد
        pass

    def show_success_dialog(self, title, message):
        """عرض نافذة نجاح مخصصة بتنسيق الأزرار الزرقاء"""
        try:
            # إنشاء نافذة مخصصة
            dialog = QDialog(self)
            dialog.setWindowTitle(title)
            dialog.setFixedSize(400, 200)
            dialog.setLayoutDirection(Qt.RightToLeft)

            # إنشاء التخطيط الرئيسي
            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(30, 30, 30, 30)
            layout.setSpacing(20)

            # أيقونة النجاح
            icon_label = QLabel("✅")
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setFont(QFont("Arial", 32))
            layout.addWidget(icon_label)

            # عنوان الرسالة
            title_label = QLabel(title)
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setFont(QFont("Arial", 14, QFont.Bold))
            title_label.setStyleSheet("color: #2c3e50; margin-bottom: 10px;")
            layout.addWidget(title_label)

            # نص الرسالة
            message_label = QLabel(message)
            message_label.setAlignment(Qt.AlignCenter)
            message_label.setFont(QFont("Arial", 11))
            message_label.setStyleSheet("color: #555; margin-bottom: 20px;")
            message_label.setWordWrap(True)
            layout.addWidget(message_label)

            # زر موافق بتنسيق الأزرار الزرقاء
            ok_button = QPushButton("موافق")
            ok_button.setObjectName("action_button")  # استخدام نفس تنسيق الأزرار الزرقاء
            ok_button.setFixedSize(120, 40)
            ok_button.setCursor(QCursor(Qt.PointingHandCursor))
            ok_button.clicked.connect(dialog.accept)

            # تخطيط الزر
            button_layout = QHBoxLayout()
            button_layout.addStretch()
            button_layout.addWidget(ok_button)
            button_layout.addStretch()
            layout.addLayout(button_layout)

            # تطبيق الأنماط
            dialog.setStyleSheet("""
                QDialog {
                    background-color: #f9f9f9;
                    border: 1px solid #ddd;
                    border-radius: 8px;
                }

                QPushButton#action_button {
                    background-color: #0078d7;
                    color: white;
                    border: 1px solid #0078d7;
                    border-radius: 2px;
                    padding: 5px 12px;
                    text-align: center;
                    min-width: 80px;
                    min-height: 24px;
                }

                QPushButton#action_button:hover {
                    background-color: #1a86d9;
                    border: 1px solid #1a86d9;
                }

                QPushButton#action_button:pressed {
                    background-color: #006cc1;
                    border: 1px solid #006cc1;
                }
            """)

            # عرض النافذة
            dialog.exec_()

        except Exception as e:
            # في حالة فشل النافذة المخصصة، استخدم النافذة العادية
            from utils.custom_widgets import show_information
            show_information(self, title, message)

    def show_warning_dialog(self, title, message):
        """عرض نافذة تحذير مخصصة بتنسيق الأزرار الافتراضي"""
        try:
            # إنشاء نافذة مخصصة
            dialog = QDialog(self)
            dialog.setWindowTitle(title)
            dialog.setFixedSize(450, 220)
            dialog.setLayoutDirection(Qt.RightToLeft)

            # إنشاء التخطيط الرئيسي
            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(30, 30, 30, 30)
            layout.setSpacing(20)

            # أيقونة التحذير
            icon_label = QLabel("⚠️")
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setFont(QFont("Arial", 32))
            layout.addWidget(icon_label)

            # عنوان الرسالة
            title_label = QLabel(title)
            title_label.setObjectName("dialog_title")
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setFont(QFont("Arial", 14, QFont.Bold))
            title_label.setStyleSheet("color: #d68910; margin-bottom: 10px;")  # لون برتقالي للتحذير
            layout.addWidget(title_label)

            # نص الرسالة
            message_label = QLabel(message)
            message_label.setAlignment(Qt.AlignCenter)
            message_label.setFont(QFont("Arial", 11))
            message_label.setStyleSheet("color: #555; margin-bottom: 20px;")
            message_label.setWordWrap(True)
            layout.addWidget(message_label)

            # زر موافق بالتنسيق الافتراضي
            ok_button = QPushButton("موافق")
            ok_button.setDefault(True)
            ok_button.setFixedSize(120, 40)
            ok_button.setCursor(QCursor(Qt.PointingHandCursor))
            ok_button.clicked.connect(dialog.accept)
            ok_button.setToolTip("إغلاق نافذة التحذير (Enter)")

            # تخطيط الزر
            button_layout = QHBoxLayout()
            button_layout.addStretch()
            button_layout.addWidget(ok_button)
            button_layout.addStretch()
            layout.addLayout(button_layout)

            # تطبيق التنسيق الموحد للنوافذ المنبثقة
            from styles import AppStyles
            dialog.setStyleSheet(AppStyles.get_dialog_style())

            # عرض النافذة
            dialog.exec_()

        except Exception as e:
            # في حالة فشل النافذة المخصصة، استخدم النافذة العادية
            from utils.custom_widgets import show_warning
            show_warning(self, title, message)

    def show_error_dialog(self, title, message):
        """عرض نافذة خطأ مخصصة بتنسيق الأزرار الافتراضي"""
        try:
            # إنشاء نافذة مخصصة
            dialog = QDialog(self)
            dialog.setWindowTitle(title)
            dialog.setFixedSize(450, 220)
            dialog.setLayoutDirection(Qt.RightToLeft)

            # إنشاء التخطيط الرئيسي
            layout = QVBoxLayout(dialog)
            layout.setContentsMargins(30, 30, 30, 30)
            layout.setSpacing(20)

            # أيقونة الخطأ
            icon_label = QLabel("❌")
            icon_label.setAlignment(Qt.AlignCenter)
            icon_label.setFont(QFont("Arial", 32))
            layout.addWidget(icon_label)

            # عنوان الرسالة
            title_label = QLabel(title)
            title_label.setObjectName("dialog_title")
            title_label.setAlignment(Qt.AlignCenter)
            title_label.setFont(QFont("Arial", 14, QFont.Bold))
            title_label.setStyleSheet("color: #c0392b; margin-bottom: 10px;")  # لون أحمر للخطأ
            layout.addWidget(title_label)

            # نص الرسالة
            message_label = QLabel(message)
            message_label.setAlignment(Qt.AlignCenter)
            message_label.setFont(QFont("Arial", 11))
            message_label.setStyleSheet("color: #555; margin-bottom: 20px;")
            message_label.setWordWrap(True)
            layout.addWidget(message_label)

            # زر موافق بالتنسيق الافتراضي
            ok_button = QPushButton("موافق")
            ok_button.setDefault(True)
            ok_button.setFixedSize(120, 40)
            ok_button.setCursor(QCursor(Qt.PointingHandCursor))
            ok_button.clicked.connect(dialog.accept)
            ok_button.setToolTip("إغلاق نافذة الخطأ (Enter)")

            # تخطيط الزر
            button_layout = QHBoxLayout()
            button_layout.addStretch()
            button_layout.addWidget(ok_button)
            button_layout.addStretch()
            layout.addLayout(button_layout)

            # تطبيق التنسيق الموحد للنوافذ المنبثقة
            from styles import AppStyles
            dialog.setStyleSheet(AppStyles.get_dialog_style())

            # عرض النافذة
            dialog.exec_()

        except Exception as e:
            # في حالة فشل النافذة المخصصة، استخدم النافذة العادية
            from utils.custom_widgets import show_error
            show_error(self, title, message)

    def print_invoice_directly(self, invoice_data, items_data):
        """طباعة الفاتورة مباشرة بدون معاينة"""
        try:
            from PyQt5.QtCore import QSettings
            from utils.thermal_printer_helper import ESCPOSInvoicePrinter
            from models.customers import CustomerModel

            # قراءة إعدادات الطباعة من الإعدادات
            settings = QSettings("MyCompany", "SmartManager")
            default_printer = settings.value("invoice_design/default_printer", "الطابعة الافتراضية")

            # قراءة معلومات الشركة مع التحقق من وجود إعدادات حقيقية
            company_info = self.get_real_company_settings(settings)

            # تصحيح اسم العميل من قاعدة البيانات
            customer_id = getattr(self, 'selected_customer_id', None)
            if customer_id:
                # الحصول على اسم العميل من قاعدة البيانات
                customer = CustomerModel.get_customer_by_id(customer_id)
                if customer:
                    invoice_data['customer_name'] = customer['name']
                    print(f"تم تصحيح اسم العميل من قاعدة البيانات: {customer['name']}")
                else:
                    print(f"لم يتم العثور على العميل بالمعرف: {customer_id}")
            else:
                # إذا لم يكن هناك معرف عميل، استخدم "عميل نقدي"
                invoice_data['customer_name'] = "عميل نقدي"
                print("لا يوجد معرف عميل، سيتم استخدام 'عميل نقدي'")

            print(f"اسم العميل النهائي للطباعة: {invoice_data.get('customer_name', 'غير محدد')}")

            # إنشاء طابعة ESC/POS
            escpos_printer = ESCPOSInvoicePrinter()

            # طباعة الفاتورة مباشرة
            success = escpos_printer.print_invoice(
                invoice_data,
                items_data,
                company_info,
                default_printer if default_printer != "الطابعة الافتراضية" else None
            )

            if success:
                print("✅ تم طباعة الفاتورة بنجاح")
            else:
                print("❌ فشل في طباعة الفاتورة")
                # يمكن إضافة رسالة تحذير هنا إذا لزم الأمر

        except Exception as e:
            print(f"خطأ في طباعة الفاتورة مباشرة: {str(e)}")
            # يمكن إضافة رسالة خطأ هنا إذا لزم الأمر




    def get_current_invoice_data(self):
        """الحصول على بيانات الفاتورة الحالية"""
        try:
            # حساب الإجماليات
            subtotal = sum(float(self.invoice_table.item(row, 4).text()) for row in range(self.invoice_table.rowCount()))
            tax_rate = float(self.tax_rate.text()) if self.tax_rate.text() else 0
            discount = float(self.discount.text()) if self.discount.text() else 0

            tax_amount = (subtotal * tax_rate) / 100
            total = subtotal + tax_amount - discount

            # إنشاء رقم مرجعي للفاتورة
            import datetime
            reference_number = f"INV-{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}"

            # الحصول على اسم العميل المحدد
            customer_name = "عميل نقدي"  # القيمة الافتراضية

            # استخدام العميل المحفوظ إذا كان متوفراً
            if hasattr(self, 'selected_customer') and self.selected_customer:
                customer_name = self.selected_customer
            else:
                # إذا لم يكن محفوظاً، استخدم النص الحالي من الكومبو بوكس
                current_text = self.customer_combo.currentText()
                if current_text and current_text != "اختر العميل" and current_text != "جاري تحميل العملاء...":
                    customer_name = current_text

            print(f"اسم العميل في الفاتورة: {customer_name}")

            return {
                'reference_number': reference_number,
                'date': datetime.datetime.now().strftime("%Y/%m/%d %H:%M"),
                'customer_name': customer_name,
                'subtotal': subtotal,
                'tax': tax_amount,
                'discount': discount,
                'total': total,
                'payment_method': 'نقدي',
                'status': 'مدفوعة'
            }
        except Exception as e:
            print(f"خطأ في الحصول على بيانات الفاتورة: {str(e)}")
            return None

    def get_current_items_data(self):
        """الحصول على بيانات عناصر الفاتورة الحالية"""
        try:
            items = []
            for row in range(self.invoice_table.rowCount()):
                item = {
                    'product_name': self.invoice_table.item(row, 0).text(),
                    'product_code': self.invoice_table.item(row, 1).text(),
                    'quantity': int(self.invoice_table.item(row, 2).text()),
                    'unit_price': float(self.invoice_table.item(row, 3).text()),
                    'total_price': float(self.invoice_table.item(row, 4).text())
                }
                items.append(item)
            return items
        except Exception as e:
            print(f"خطأ في الحصول على بيانات العناصر: {str(e)}")
            return []

    def confirm_clear_invoice(self):
        """تأكيد مسح الفاتورة الحالية"""
        # عدم عرض تأكيد إذا كانت الفاتورة فارغة بالفعل
        if self.invoice_table.rowCount() == 0:
            return

        # عرض مربع حوار تأكيد
        from utils.custom_widgets import show_question
        reply = show_question(
            self,
            "تأكيد الحذف",
            "هل أنت متأكد من رغبتك في حذف جميع المنتجات من الفاتورة الحالية؟"
        )

        # إذا كانت الإجابة نعم، قم بمسح الفاتورة
        if reply:
            self.clear_invoice()

    def update_completer_filter(self, text):
        """تحديث معيار الفلترة للمكمل التلقائي"""
        try:
            # التحقق مما إذا كان النص المكتوب يطابق بالضبط أحد العملاء الموجودين
            # إذا كان كذلك، فلا داعي لفتح النافذة المنسدلة مرة أخرى
            if text in self.default_customers:
                # إغلاق النافذة المنسدلة إذا كانت مفتوحة
                if self.completer.popup().isVisible():
                    self.completer.popup().hide()
                return

            # تطبيق الفلتر فقط إذا كان هناك نص
            if text:
                # تعيين النمط المستخدم للفلترة (للبحث في أي مكان من النص)
                self.proxy_model.setFilterRegExp(QRegExp(text, Qt.CaseInsensitive, QRegExp.FixedString))

                # إظهار مربع الاقتراحات إذا كان هناك نتائج
                if self.proxy_model.rowCount() > 0:
                    rect = self.customer_combo.lineEdit().rect()
                    self.completer.complete(rect)
            else:
                # إذا كان حقل البحث فارغًا، أعرض كل العملاء
                self.proxy_model.setFilterRegExp("")
        except Exception as e:
            # تجاهل أي أخطاء لمنع إغلاق البرنامج
            print(f"خطأ في تحديث المكمل التلقائي: {e}")

    def on_completer_activated(self, text):
        """معالجة حدث اختيار عنصر من المكمل التلقائي"""
        try:
            # تخزين النص المختار
            selected_text = text

            # إغلاق النافذة المنسدلة للمكمل التلقائي فوراً
            if self.completer.popup().isVisible():
                self.completer.popup().hide()

            # تعطيل مؤقت لإشارات تغيير النص لمنع إعادة فتح النافذة المنسدلة
            line_edit = self.customer_combo.lineEdit()
            line_edit.blockSignals(True)

            # تعيين النص المختار في الكومبوبوكس
            self.customer_combo.setCurrentText(selected_text)

            # البحث عن العنصر في القائمة وتحديثه
            idx = self.customer_combo.findText(selected_text)
            if idx >= 0:
                self.customer_combo.setCurrentIndex(idx)

            # استعادة إشارات تغيير النص
            line_edit.blockSignals(False)

            # تنشيط الحقل مرة أخرى للسماح بمواصلة الكتابة
            line_edit.setFocus()

            # وضع المؤشر في نهاية النص للسماح بمواصلة الكتابة
            line_edit.setCursorPosition(len(selected_text))

            # استدعاء دالة تغيير العميل
            self.customer_changed(idx)

            # منع ظهور النافذة المنسدلة مرة أخرى مباشرة بعد الاختيار
            QTimer.singleShot(100, lambda: self.prevent_immediate_popup())

        except Exception as e:
            print(f"خطأ في معالجة اختيار العميل: {e}")

    def prevent_immediate_popup(self):
        """منع ظهور النافذة المنسدلة مباشرة بعد الاختيار"""
        try:
            if self.completer.popup().isVisible():
                self.completer.popup().hide()
            if self.customer_combo.view().isVisible():
                self.customer_combo.hidePopup()
        except Exception as e:
            print(f"خطأ في منع ظهور النافذة المنسدلة: {e}")

    def process_product_search(self):
        """معالجة البحث عن منتج باستخدام الاسم أو الباركود مع حماية من التكرار"""
        try:
            search_text = self.product_search_input.text().strip()
            if not search_text:
                return

            # التحقق من وجود متغيرات التحكم في التكرار
            if not hasattr(self, 'last_search_text'):
                self.last_search_text = ""
                self.last_search_time = 0
                self.search_repeat_count = 0
                self.search_blocked = False

            # الحصول على الوقت الحالي
            import time
            current_time = time.time()

            # إذا كان البحث محظوراً مؤقتاً، تجاهل الطلب
            if self.search_blocked:
                return

            # التحقق من التكرار السريع (أقل من 1 ثانية)
            time_diff = current_time - self.last_search_time

            if search_text == self.last_search_text and time_diff < 1.0:
                # زيادة عداد التكرار
                self.search_repeat_count += 1

                # إذا تكرر البحث أكثر من 2 مرات في وقت قصير، احظره مؤقتاً
                if self.search_repeat_count > 2:
                    self.search_blocked = True

                    # عرض رسالة تحذير
                    from utils.custom_widgets import show_warning
                    show_warning(
                        self,
                        "تحذير - تكرار في البحث",
                        "تم اكتشاف تكرار مفرط في البحث.\n"
                        "تم إيقاف البحث مؤقتاً لمدة ثانيتين.\n"
                        "يرجى الانتظار قبل المحاولة مرة أخرى."
                    )

                    # إعادة تفعيل البحث بعد ثانيتين
                    QTimer.singleShot(2000, self.reset_search_protection)
                    return
            else:
                # إعادة تعيين العداد إذا كان النص مختلف أو الوقت كافي
                self.search_repeat_count = 0

            # تحديث آخر بحث ووقته
            self.last_search_text = search_text
            self.last_search_time = current_time

            # البحث عن منتج بواسطة الباركود أولاً
            product = ProductModel.get_product_by_code(search_text)

            # إذا تم العثور على منتج بواسطة الباركود، أضفه مباشرة إلى الفاتورة
            if product:
                # التحقق من أن المنتج ليس تصنيفًا
                if product.get('product_type') != 'category_placeholder':
                    self.add_product_to_invoice(product)
                    self.product_search_input.clear()
                    # إعادة تعيين متغيرات الحماية بعد إضافة المنتج بنجاح
                    self.reset_all_protection_variables()
                else:
                    # إذا كان المنتج تصنيفًا، عرض رسالة توضيحية
                    from utils.custom_widgets import show_information
                    show_information(
                        self,
                        "تصنيف وليس منتج",
                        f"العنصر '{product.get('name')}' هو تصنيف وليس منتجًا قابلاً للإضافة إلى الفاتورة."
                    )
                return

            # البحث عن منتج بواسطة الاسم المطابق تماماً
            exact_product = ProductModel.get_product_by_name(search_text)
            if exact_product and exact_product.get('product_type') != 'category_placeholder':
                # التحقق من توفر المخزون للمنتجات العادية
                if exact_product.get('product_type') == 'physical':
                    stock_quantity = None
                    if 'quantity' in exact_product:
                        stock_quantity = exact_product['quantity']
                    elif 'stock' in exact_product:
                        stock_quantity = exact_product['stock']

                    # إذا كان هناك مخزون متاح، أضف المنتج مباشرة
                    if stock_quantity is not None and stock_quantity > 0:
                        self.add_product_to_invoice(exact_product)
                        self.product_search_input.clear()
                        # إعادة تعيين متغيرات الحماية بعد إضافة المنتج بنجاح
                        self.reset_all_protection_variables()
                        return
                else:
                    # إذا كان المنتج خدمة، أضفه مباشرة
                    self.add_product_to_invoice(exact_product)
                    self.product_search_input.clear()
                    # إعادة تعيين متغيرات الحماية بعد إضافة المنتج بنجاح
                    self.reset_all_protection_variables()
                    return

            # إذا لم يتم العثور على باركود أو اسم مطابق، ابحث بالاسم وأظهر نتائج البحث
            self.show_product_search_results(search_text)

        except Exception as e:
            # معالجة الأخطاء مع إعادة تعيين متغيرات الحماية
            self.handle_search_error(str(e))

    def reset_search_protection(self):
        """إعادة تعيين حماية البحث بعد انتهاء فترة الحظر"""
        self.search_blocked = False
        self.search_repeat_count = 0
        self.last_search_text = ""
        print("تم إعادة تفعيل البحث")

    def on_product_search_changed(self, text):
        """معالجة حدث تغيير النص في حقل البحث عن المنتجات مع حماية محسنة من التكرار"""
        # التحقق من وجود نص
        if not text or len(text.strip()) < 3:
            return

        # الحصول على الوقت الحالي
        import time
        current_time = time.time()

        # التحقق من وجود متغيرات التحكم في التكرار
        if not hasattr(self, 'last_barcode_input'):
            self.last_barcode_input = ""
            self.last_input_time = 0
            self.barcode_repeat_count = 0
            self.barcode_blocked = False

        # إذا كان الباركود محظوراً مؤقتاً، تجاهل الإدخال
        if self.barcode_blocked:
            return

        # التحقق من التكرار السريع (أقل من 0.3 ثانية للباركود)
        time_diff = current_time - self.last_input_time

        if text == self.last_barcode_input and time_diff < 0.3:
            # زيادة عداد التكرار
            self.barcode_repeat_count += 1

            # إذا تكرر الباركود أكثر من 2 مرات في وقت قصير، احظره مؤقتاً
            if self.barcode_repeat_count > 2:
                self.barcode_blocked = True
                self.product_search_input.clear()

                # عرض رسالة تحذير
                from utils.custom_widgets import show_warning
                show_warning(
                    self,
                    "تحذير - تكرار في قراءة الباركود",
                    "تم اكتشاف تكرار مفرط في قراءة الباركود.\n"
                    "تم إيقاف القراءة مؤقتاً لمدة 2.5 ثانية.\n"
                    "تأكد من سلامة قارئ الباركود."
                )

                # إعادة تفعيل القراءة بعد 2.5 ثانية
                QTimer.singleShot(2500, self.reset_barcode_protection)
                return
        else:
            # إعادة تعيين العداد إذا كان الباركود مختلف أو الوقت كافي
            self.barcode_repeat_count = 0

        # تحديث آخر إدخال ووقته
        self.last_barcode_input = text
        self.last_input_time = current_time

        # إذا كان النص يبدو كباركود صالح، قم بمعالجته تلقائياً
        if self.is_valid_barcode(text.strip()):
            # تأخير قصير للتأكد من اكتمال الإدخال
            QTimer.singleShot(150, lambda: self.auto_process_barcode(text.strip()))

    def reset_barcode_protection(self):
        """إعادة تعيين حماية الباركود بعد انتهاء فترة الحظر"""
        self.barcode_blocked = False
        self.barcode_repeat_count = 0
        self.last_barcode_input = ""
        print("تم إعادة تفعيل قراءة الباركود")

    def auto_process_barcode(self, barcode):
        """معالجة الباركود تلقائياً مع حماية محسنة"""
        try:
            # التحقق من أن الباركود لا يزال موجوداً في حقل البحث
            current_text = self.product_search_input.text().strip()
            if current_text != barcode:
                return  # تم تغيير النص، تجاهل المعالجة

            # التحقق من أن الباركود لا يزال محظوراً
            if hasattr(self, 'barcode_blocked') and self.barcode_blocked:
                return  # الباركود محظور، تجاهل المعالجة

            # التحقق من أن البحث لا يزال محظوراً
            if hasattr(self, 'search_blocked') and self.search_blocked:
                return  # البحث محظور، تجاهل المعالجة

            # التحقق من صحة الباركود باستخدام الدالة المخصصة
            if not self.is_valid_barcode(barcode):
                return  # باركود غير صالح

            # معالجة البحث عن المنتج
            self.process_product_search()

        except Exception as e:
            print(f"خطأ في معالجة الباركود التلقائي: {str(e)}")
            # في حالة حدوث خطأ، امسح حقل البحث لتجنب التكرار
            try:
                self.product_search_input.clear()
            except:
                pass

    def refresh_page(self):
        """تحديث بيانات الصفحة عند الانتقال إليها أو عند فتح التطبيق"""
        try:
            # إعادة تحميل العملاء من قاعدة البيانات دائماً
            self.load_customers_from_database()

            # تحديث حالة إظهار/إخفاء قسم المفضلة بناءً على الإعداد
            from PyQt5.QtCore import QSettings
            settings = QSettings("MyCompany", "SmartManager")
            show_favorites = settings.value("show_favorites_section_in_sales", True)
            if isinstance(show_favorites, str):
                show_favorites = show_favorites.lower() == 'true'

            print(f"إعداد إظهار المفضلة: {show_favorites}")

            # إذا كان القسم يجب أن يظهر وهو غير موجود، أنشئه وأضفه
            if show_favorites and (not hasattr(self, 'favorites_widget') or self.favorites_widget is None):
                print("إنشاء قسم المفضلة...")
                favorites_widget = self.add_favorite_products()
                if favorites_widget:
                    favorites_widget.setFixedWidth(200)
                    favorites_widget.setMinimumWidth(200)
                    favorites_widget.setMaximumWidth(200)
                    # ابحث عن right_section وأضف إليه القسم
                    if hasattr(self, 'content_splitter'):
                        for i in range(self.content_splitter.count()):
                            widget = self.content_splitter.widget(i)
                            if widget.objectName() == 'right_section':
                                layout = widget.layout()
                                if layout:
                                    layout.addWidget(favorites_widget)
                                    print("تم إضافة قسم المفضلة")
                                break
                    self.favorites_widget = favorites_widget

            # إذا كان القسم يجب أن يختفي وهو موجود، احذفه
            elif not show_favorites and hasattr(self, 'favorites_widget') and self.favorites_widget is not None:
                print("إزالة قسم المفضلة...")
                # ابحث عن right_section وأزل القسم
                if hasattr(self, 'content_splitter'):
                    for i in range(self.content_splitter.count()):
                        widget = self.content_splitter.widget(i)
                        if widget.objectName() == 'right_section':
                            layout = widget.layout()
                            if layout and self.favorites_widget:
                                layout.removeWidget(self.favorites_widget)
                                self.favorites_widget.deleteLater()
                                print("تم إزالة قسم المفضلة")
                            break
                self.favorites_widget = None

            # تحديث المنتجات المفضلة إذا كان القسم ظاهر
            if hasattr(self, 'favorites_widget') and self.favorites_widget is not None:
                self.refresh_favorites_display()

            # تحديث حالة تفعيل الأزرار بناءً على صلاحيات المستخدم
            self.update_buttons_state()

            print("تم تحديث صفحة المبيعات بنجاح")

        except Exception as e:
            print(f"خطأ في تحديث صفحة المبيعات: {str(e)}")
            import traceback
            traceback.print_exc()

    def reset_all_protection_variables(self):
        """إعادة تعيين جميع متغيرات الحماية من التكرار"""
        # إعادة تعيين متغيرات حماية الباركود
        self.last_barcode_input = ""
        self.last_input_time = 0
        self.barcode_repeat_count = 0
        self.barcode_blocked = False

        # إعادة تعيين متغيرات حماية البحث
        self.last_search_text = ""
        self.last_search_time = 0
        self.search_repeat_count = 0
        self.search_blocked = False

        print("تم إعادة تعيين جميع متغيرات الحماية من التكرار")

    def handle_search_error(self, error_message, clear_input=True):
        """معالجة أخطاء البحث مع إعادة تعيين متغيرات الحماية"""
        try:
            print(f"خطأ في البحث: {error_message}")

            # مسح حقل البحث إذا طُلب ذلك
            if clear_input:
                self.product_search_input.clear()

            # إعادة تعيين متغيرات الحماية
            self.reset_all_protection_variables()

            # عرض رسالة خطأ للمستخدم
            from utils.custom_widgets import show_error
            show_error(
                self,
                "خطأ في البحث",
                f"حدث خطأ أثناء البحث عن المنتج:\n{error_message}\n\nيرجى المحاولة مرة أخرى."
            )

        except Exception as e:
            print(f"خطأ في معالجة خطأ البحث: {str(e)}")

    def is_valid_barcode(self, text):
        """التحقق من صحة الباركود - يدعم الأرقام والحروف الإنجليزية"""
        try:
            # التحقق من وجود نص
            if not text or not text.strip():
                return False

            text = text.strip()

            # التحقق من طول الباركود (يجب أن يكون بين 3 و 30 حرف)
            if len(text) < 3 or len(text) > 30:
                return False

            # التحقق من أن النص يحتوي على أحرف مدعومة فقط
            # الأحرف المدعومة: أرقام (0-9)، حروف إنجليزية (A-Z, a-z)، ورموز خاصة محددة
            allowed_chars = set('0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz -._/+=')

            for char in text:
                if char not in allowed_chars:
                    return False

            # التحقق من عدم وجود مسافات في البداية أو النهاية (تم تنظيفها بالفعل)
            # والتحقق من عدم وجود مسافات متتالية
            if '  ' in text:  # مسافتان متتاليتان
                return False

            return True

        except Exception:
            return False

    def get_real_company_settings(self, settings):
        """قراءة إعدادات الشركة الحقيقية مع التحقق من وجود قيم مخصصة"""
        try:
            # القيم الافتراضية
            default_values = {
                'name': "اسم الشركة",
                'phone': "رقم الهاتف",
                'address': "عنوان الشركة",
                'notes': "شكراً لتعاملكم معنا"
            }

            # قراءة الإعدادات
            company_info = {
                'name': settings.value("company_name", default_values['name']),
                'phone': settings.value("company_phone", default_values['phone']),
                'address': settings.value("company_address", default_values['address']),
                'notes': settings.value("invoice_notes", default_values['notes'])
            }

            # التحقق من وجود قيم افتراضية
            has_defaults = any(
                company_info[key] == default_values[key]
                for key in default_values.keys()
            )

            if has_defaults:
                print("⚠️ تحذير: يتم استخدام بيانات افتراضية للشركة")
                print("📝 يرجى تحديث معلومات الشركة في تاب الإعدادات")

                # عرض رسالة تحذيرية للمستخدم
                try:
                    from utils.custom_widgets import show_warning
                    show_warning(
                        self,
                        "بيانات الشركة غير محدثة",
                        "يتم استخدام بيانات افتراضية للشركة في الفاتورة.\n\n"
                        "يرجى الذهاب إلى تاب 'الإعدادات' وتحديث:\n"
                        "• اسم الشركة\n"
                        "• رقم الهاتف\n"
                        "• عنوان الشركة\n"
                        "• ملاحظات الفاتورة\n\n"
                        "ثم حفظ الإعدادات لتظهر في الفواتير المطبوعة."
                    )
                except Exception as warning_error:
                    print(f"خطأ في عرض التحذير: {str(warning_error)}")
            else:
                print("✅ يتم استخدام بيانات الشركة المخصصة")

            return company_info

        except Exception as e:
            print(f"خطأ في قراءة إعدادات الشركة: {str(e)}")
            # إرجاع القيم الافتراضية في حالة الخطأ
            return {
                'name': "اسم الشركة",
                'phone': "رقم الهاتف",
                'address': "عنوان الشركة",
                'notes': "شكراً لتعاملكم معنا"
            }

    def show_product_search_results(self, search_text):
        """عرض نتائج البحث عن المنتج في نافذة منبثقة"""
        # البحث عن المنتجات بواسطة الاسم
        all_products = ProductModel.search_products(search_text)

        # فلترة التصنيفات من نتائج البحث
        products = []
        for product in all_products:
            # تجاهل عناصر التصنيف (category_placeholder)
            if product.get('product_type') != 'category_placeholder':
                products.append(product)

        if not products:
            from utils.custom_widgets import show_information
            show_information(self, "لا توجد نتائج", f"لم يتم العثور على منتجات تطابق '{search_text}'")
            return

        # إذا وجد منتج واحد فقط، أضفه مباشرة إلى الفاتورة
        if len(products) == 1:
            self.add_product_to_invoice(products[0])
            self.product_search_input.clear()
            # إعادة تعيين متغيرات الحماية بعد إضافة المنتج بنجاح
            self.reset_all_protection_variables()
            return

        # إنشاء قائمة اختيار للمنتجات
        product_dialog = QDialog(self)
        product_dialog.setWindowTitle("نتائج البحث عن المنتجات")
        product_dialog.setMinimumWidth(500)
        product_dialog.setMinimumHeight(400)
        product_dialog.setLayoutDirection(Qt.RightToLeft)
        product_dialog.setStyleSheet("""
            QDialog {
                background-color: #f9f9f9;
                border: 1px solid #ddd;
                font-family: 'Readex Pro', 'Tahoma', 'Arial';
            }
            QLabel#title_label {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                padding: 10px;
                border-bottom: 1px solid #eee;
                background-color: #f5f5f5;
                font-family: 'Readex Pro', 'Tahoma', 'Arial';
            }
            QLabel#info_label {
                color: #555;
                font-size: 12px;
                padding: 5px;
                font-family: 'Readex Pro', 'Tahoma', 'Arial';
            }
            QListWidget {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 5px;
                font-size: 13px;
                outline: none; /* إزالة الإطار المنقط من القائمة */
                font-family: 'Readex Pro', 'Tahoma', 'Arial';
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #eee;
                font-family: 'Readex Pro', 'Tahoma', 'Arial';
            }
            QListWidget::item:selected {
                background-color: #e0f0ff;
                color: #333;
                outline: none !important; /* إزالة الإطار المنقط من العناصر المحددة */
            }
            QListWidget::item:hover {
                background-color: #f0f7ff;
            }
            QListWidget::item:focus {
                outline: none !important; /* إزالة الإطار المنقط من العناصر عند التركيز */
            }
            QPushButton#action_button {
                background-color: #4285f4;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 13px;
                min-width: 100px;
                font-family: 'Readex Pro', 'Tahoma', 'Arial';
            }
            QPushButton#action_button:hover {
                background-color: #3367d6;
            }
            QPushButton#cancel_button {
                background-color: white;
                color: #555;
                border: 1px solid #ddd;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 13px;
                min-width: 100px;
                font-family: 'Readex Pro', 'Tahoma', 'Arial';
            }
            QPushButton#cancel_button:hover {
                background-color: #f0f0f0;
            }
            /* إزالة الإطار المنقط من جميع العناصر */
            *:focus {
                outline: none !important;
            }
            * {
                font-family: 'Readex Pro', 'Tahoma', 'Arial';
            }
        """)

        dialog_layout = QVBoxLayout(product_dialog)
        dialog_layout.setContentsMargins(0, 0, 0, 10)
        dialog_layout.setSpacing(10)

        # إضافة عنوان للنافذة
        title_label = QLabel(f"نتائج البحث عن: {search_text}")
        title_label.setObjectName("title_label")
        title_label.setAlignment(Qt.AlignCenter)
        dialog_layout.addWidget(title_label)

        # إضافة محتوى النافذة
        content_widget = QWidget()
        content_layout = QVBoxLayout(content_widget)
        content_layout.setContentsMargins(15, 15, 15, 15)
        content_layout.setSpacing(15)

        # إضافة شرح
        info_label = QLabel(f"تم العثور على {len(products)} منتج. اختر المنتج المطلوب:")
        info_label.setObjectName("info_label")
        content_layout.addWidget(info_label)

        # إنشاء قائمة للمنتجات
        product_list = QListWidget()
        product_list.setLayoutDirection(Qt.RightToLeft)

        for product in products:
            # إنشاء عنصر قائمة مع معلومات أكثر تفصيلاً
            item_text = f"{product['name']}"

            # إضافة معلومات الباركود إذا كان موجوداً
            if product.get('code'):
                item_text += f" (كود: {product['code']})"

            # إضافة السعر
            item_text += f" - {self.format_price(product['price'])}"

            # إضافة معلومات المخزون إذا كانت متوفرة
            # للمنتجات العادية (ليست خدمات) فقط
            if product.get('product_type') != 'service':
                # تحقق من وجود معلومات المخزون، سواء في 'quantity' أو 'stock'
                stock_quantity = None
                if 'quantity' in product:
                    stock_quantity = product['quantity']
                elif 'stock' in product:
                    stock_quantity = product['stock']

                if stock_quantity is not None:
                    item_text += f" - متوفر: {stock_quantity} قطعة"
            else:
                # إذا كان المنتج خدمة، أضف رمز الخدمة
                item_text += " - 🛠️ خدمة"

            # إنشاء عنصر القائمة وتخزين بيانات المنتج
            item = QListWidgetItem(item_text)
            item.setData(Qt.UserRole, product)
            product_list.addItem(item)

        product_list.setAlternatingRowColors(True)
        product_list.setSelectionMode(QListWidget.SingleSelection)
        product_list.setCurrentRow(0)  # تحديد العنصر الأول افتراضياً
        content_layout.addWidget(product_list)

        dialog_layout.addWidget(content_widget)

        # إضافة أزرار
        buttons_widget = QWidget()
        buttons_layout = QHBoxLayout(buttons_widget)
        buttons_layout.setContentsMargins(15, 0, 15, 10)
        buttons_layout.setSpacing(10)

        select_btn = QPushButton("إضافة للفاتورة")
        select_btn.setObjectName("action_button")
        select_btn.setCursor(QCursor(Qt.PointingHandCursor))

        cancel_btn = QPushButton("إلغاء")
        cancel_btn.setObjectName("cancel_button")
        cancel_btn.setCursor(QCursor(Qt.PointingHandCursor))

        buttons_layout.addWidget(cancel_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(select_btn)

        dialog_layout.addWidget(buttons_widget)

        # ربط الأحداث
        select_btn.clicked.connect(lambda: self.select_product_from_results(product_list, product_dialog))
        cancel_btn.clicked.connect(product_dialog.reject)
        product_list.itemDoubleClicked.connect(lambda: self.select_product_from_results(product_list, product_dialog))

        # إضافة معالج أحداث لوحة المفاتيح للنافذة
        def handle_key_press(event):
            if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
                # إضافة المنتج المحدد عند الضغط على Enter
                self.select_product_from_results(product_list, product_dialog)
            elif event.key() == Qt.Key_Escape:
                # إغلاق النافذة عند الضغط على Escape
                product_dialog.reject()
            else:
                # السلوك الافتراضي للمفاتيح الأخرى
                QDialog.keyPressEvent(product_dialog, event)

        # ربط معالج الأحداث بالنافذة
        product_dialog.keyPressEvent = handle_key_press

        # إضافة معالج أحداث لوحة المفاتيح للقائمة
        def handle_list_key_press(event):
            if event.key() == Qt.Key_Return or event.key() == Qt.Key_Enter:
                # إضافة المنتج المحدد عند الضغط على Enter في القائمة
                self.select_product_from_results(product_list, product_dialog)
            else:
                # السلوك الافتراضي للمفاتيح الأخرى
                QListWidget.keyPressEvent(product_list, event)

        # ربط معالج الأحداث بالقائمة
        product_list.keyPressEvent = handle_list_key_press

        # تعيين التركيز على القائمة عند فتح النافذة
        product_list.setFocus()

        # عرض النافذة المنبثقة
        product_dialog.exec_()

    def select_product_from_results(self, product_list, dialog):
        """التعامل مع اختيار منتج من نتائج البحث"""
        selected_items = product_list.selectedItems()
        if selected_items:
            # الحصول على بيانات المنتج المرتبطة بالعنصر المحدد
            product = selected_items[0].data(Qt.UserRole)

            # تعديل المفتاح من 'stock' إلى 'quantity' لتوافق مع دالة add_product_to_invoice
            # ضمان وجود كلا الحقلين 'stock' و 'quantity' وأنهما متطابقان
            if 'stock' in product:
                # إذا كان المنتج خدمة، لا نحتاج لكمية
                if product.get('product_type') != 'service':
                    product['quantity'] = product['stock']
                    # أيضًا نجعل 'stock' يساوي 'quantity' للتأكد من اتساق البيانات
                    product['stock'] = product['quantity']
            elif 'quantity' in product and product.get('product_type') != 'service':
                # إذا كان لدينا 'quantity' فقط، نضيف 'stock' للاتساق
                product['stock'] = product['quantity']

            # إضافة المنتج إلى الفاتورة
            self.add_product_to_invoice(product)

            # مسح حقل البحث
            self.product_search_input.clear()

            # إعادة تعيين متغيرات الحماية بعد إضافة المنتج بنجاح
            self.reset_all_protection_variables()

            # إغلاق النافذة المنبثقة
            dialog.accept()

    def customer_changed(self, index):
        """التعامل مع تغيير العميل المحدد"""
        selected_customer = self.customer_combo.currentText()
        print(f"تم اختيار العميل: {selected_customer}")

        # تخزين اسم العميل المحدد للاستخدام في عملية البيع
        self.selected_customer = selected_customer

        # تخزين معرف العميل كخاصية للاستخدام في عملية البيع
        self.selected_customer_id = None

        # إذا كان العميل ليس "عميل نقدي"، ابحث عن معرفه في قاعدة البيانات
        if selected_customer != "عميل نقدي":
            customers = CustomerModel.search_customers(selected_customer)
            if customers and len(customers) > 0:
                self.selected_customer_id = customers[0]['id']

    def add_new_customer(self, suggested_name=None):
        """فتح نافذة إضافة عميل جديد (نفس نافذة العملاء) مع إمكانية تمرير اسم مقترح"""
        from views.customers import CustomerDialog
        from models.customers import CustomerModel
        from utils.custom_widgets import show_information
        from PyQt5.QtWidgets import QMessageBox

        dialog = CustomerDialog(self)
        if suggested_name:
            dialog.name_input.setText(suggested_name)
        if dialog.exec_() == dialog.Accepted:
            customer_data = dialog.get_customer_data()
            customer_id = CustomerModel.add_customer(customer_data)
            if customer_id:
                show_information(self, "تمت الإضافة", "تمت إضافة العميل بنجاح!")
                # إعادة تحميل العملاء وتحديد العميل الجديد
                self.load_customers_from_database(select_customer_name=customer_data['name'])
            else:
                QMessageBox.warning(self, "خطأ", "حدث خطأ أثناء إضافة العميل!")
        # إذا أغلق المستخدم النافذة أو ألغى، لا شيء

    def load_customers_from_database(self, select_customer_name=None):
        """تحميل العملاء من قاعدة البيانات بشكل غير متزامن، مع إمكانية تحديد عميل بعد التحميل"""
        try:
            # إضافة رسالة تحميل مؤقتة
            self.customer_combo.clear()
            self.customer_combo.addItem("جاري تحميل العملاء...")
            self.customer_combo.setEnabled(False)

            # دالة تحميل العملاء
            def load_customers():
                return CustomerModel.get_all_customers()

            # معالجة البيانات بعد التحميل
            def on_customers_loaded(customers):
                try:
                    # إضافة العميل النقدي دائماً كخيار أول
                    self.default_customers = ["عميل نقدي"]

                    # إضافة أسماء العملاء إلى القائمة
                    for customer in customers:
                        self.default_customers.append(customer["name"])

                    # إضافة العملاء إلى القائمة المنسدلة
                    self.customer_combo.clear()
                    self.customer_combo.addItems(self.default_customers)

                    # تحديد العميل المطلوب إذا تم تمريره
                    if select_customer_name and select_customer_name in self.default_customers:
                        idx = self.default_customers.index(select_customer_name)
                        self.customer_combo.setCurrentIndex(idx)
                        self.selected_customer = select_customer_name
                        # تحديث selected_customer_id
                        customers_found = CustomerModel.search_customers(select_customer_name)
                        if customers_found and len(customers_found) > 0:
                            self.selected_customer_id = customers_found[0]['id']
                        else:
                            self.selected_customer_id = None
                    else:
                        # تعيين العميل النقدي (الافتراضي) كخيار أول
                        self.customer_combo.setCurrentIndex(0)
                        self.selected_customer = "عميل نقدي"
                        self.selected_customer_id = None

                    # تحديث المكمل التلقائي
                    if hasattr(self, 'completer_model'):
                        self.completer_model.setStringList(self.default_customers)

                    # تفعيل القائمة
                    self.customer_combo.setEnabled(True)

                except Exception as e:
                    print(f"خطأ في معالجة بيانات العملاء: {str(e)}")
                    self.customer_combo.clear()
                    self.customer_combo.addItem("عميل نقدي")
                    self.customer_combo.setEnabled(True)

            # معالجة الأخطاء
            def on_error(error_msg):
                print(f"خطأ في تحميل العملاء: {error_msg}")
                self.customer_combo.clear()
                self.customer_combo.addItem("عميل نقدي")
                self.customer_combo.setEnabled(True)
                show_error(self, "خطأ", f"فشل تحميل قائمة العملاء: {error_msg}")

            # تحميل العملاء مباشرة
            try:
                customers = load_customers()
                on_customers_loaded(customers)
            except Exception as e:
                on_error(str(e))

        except Exception as e:
            print(f"خطأ في بدء تحميل العملاء: {str(e)}")
            self.customer_combo.clear()
            self.customer_combo.addItem("عميل نقدي")
            self.customer_combo.setEnabled(True)

    def show_invoice_context_menu(self, position):
        """عرض قائمة السياق للعناصر في جدول الفاتورة"""
        # التحقق من وجود صف محدد
        selected_indexes = self.invoice_table.selectedIndexes()
        if not selected_indexes:
            return

        # الحصول على صف العنصر المحدد
        row = selected_indexes[0].row()

        # الحصول على بيانات العنصر
        product_name = self.invoice_table.item(row, 1).text()
        price_text = self.invoice_table.item(row, 2).text().replace(' ج.م', '').strip()
        try:
            current_price = float(price_text)
        except ValueError:
            self.show_warning_dialog("خطأ", "لا يمكن تحويل السعر إلى رقم")
            return

        quantity = int(self.invoice_table.item(row, 3).text())

        # التحقق من صلاحية المستخدم
        main_window = None
        parent = self.parent()
        user_id = None

        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()

        if main_window and main_window.current_user:
            user_id = main_window.current_user.get('id')

        # استيراد وحدة التحكم بالمستخدمين
        from controllers.user_controller import UserController

        # إنشاء قائمة السياق
        context_menu = QMenu(self)
        context_menu.setLayoutDirection(Qt.RightToLeft)

        # تطبيق نمط مخصص للقائمة
        context_menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                padding: 5px;
            }

            QMenu::item {
                padding: 8px 25px;
                border-radius: 4px;
                color: #333333;
            }

            QMenu::item:selected {
                background-color: #3b82f6;
                color: white;
            }

            QMenu::separator {
                height: 1px;
                background-color: #e2e8f0;
                margin: 5px 0px;
            }
        """)

        # إضافة عنوان العنصر
        title_action = QAction(f"العنصر: {product_name}", self)
        title_action.setEnabled(False)
        title_font = title_action.font()
        title_font.setBold(True)
        title_action.setFont(title_font)
        context_menu.addAction(title_action)

        # إضافة فاصل
        context_menu.addSeparator()

        # إضافة إجراء تعديل السعر - يتطلب صلاحية
        if user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "تعديل سعر منتج"):
            edit_price_action = QAction("💰 تعديل السعر", self)
            edit_price_action.triggered.connect(lambda: self.edit_item_price(row))
            context_menu.addAction(edit_price_action)

        # إضافة إجراء تعديل الكمية - يتطلب صلاحية
        if user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "تعديل كمية منتج"):
            edit_quantity_action = QAction("📦 تعديل الكمية", self)
            edit_quantity_action.triggered.connect(lambda: self.edit_item_quantity(row))
            context_menu.addAction(edit_quantity_action)

        # إضافة فاصل
        context_menu.addSeparator()

        # إضافة إجراء الحذف - يتطلب صلاحية
        if user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "حذف منتج من الفاتورة"):
            delete_action = QAction("🗑️ حذف العنصر", self)
            delete_action.triggered.connect(lambda: self.remove_product_from_invoice(row))
            context_menu.addAction(delete_action)

        # عرض القائمة في موقع النقر
        context_menu.exec_(self.invoice_table.mapToGlobal(position))

    def edit_item_price(self, row):
        """تعديل سعر عنصر في الفاتورة"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية تعديل سعر منتج
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "تعديل سعر منتج", show_message=True, parent_widget=self):
                    return

            # الحصول على البيانات الحالية للعنصر
            price_text = self.invoice_table.item(row, 2).text().replace(' ج.م', '').strip()
            try:
                current_price = float(price_text)
            except ValueError:
                self.show_warning_dialog("خطأ", "لا يمكن تحويل السعر إلى رقم")
                return

            quantity = int(self.invoice_table.item(row, 3).text())
            product_name = self.invoice_table.item(row, 1).text()
        except Exception as e:
            self.show_error_dialog("خطأ", f"حدث خطأ أثناء التحقق من الصلاحيات: {str(e)}")
            return

        # إنشاء مربع حوار لتعديل السعر مع تطبيق التنسيقات
        from styles import AppStyles

        # إنشاء QInputDialog وتطبيق التنسيقات عليه
        input_dialog = QInputDialog(self)
        input_dialog.setWindowTitle("تعديل السعر")
        input_dialog.setLabelText(f"أدخل السعر الجديد للمنتج '{product_name}':")
        input_dialog.setInputMode(QInputDialog.DoubleInput)
        input_dialog.setDoubleValue(current_price)
        input_dialog.setDoubleRange(0.01, 999999.99)
        input_dialog.setDoubleDecimals(2)

        # تطبيق التنسيقات على النافذة
        input_dialog.setStyleSheet(AppStyles.get_input_dialog_style())

        # عرض النافذة والحصول على النتيجة
        ok = input_dialog.exec_() == QInputDialog.Accepted
        new_price = input_dialog.doubleValue() if ok else current_price

        if ok and new_price != current_price:
            # تحديث سعر العنصر في الجدول
            price_item = QTableWidgetItem(f"{new_price:.2f} {CURRENCY_SYMBOL}")
            price_item.setTextAlignment(Qt.AlignCenter)
            self.invoice_table.setItem(row, 2, price_item)

            # تحديث إجمالي العنصر
            total = new_price * quantity
            total_item = QTableWidgetItem(f"{total:.2f} {CURRENCY_SYMBOL}")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.invoice_table.setItem(row, 4, total_item)

            # تحديث إجمالي الفاتورة
            self.calculate_totals()

    def edit_item_quantity(self, row):
        """تعديل كمية عنصر في الفاتورة"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية تعديل كمية منتج
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "تعديل كمية منتج", show_message=True, parent_widget=self):
                    return

            # الحصول على البيانات الحالية للعنصر
            current_quantity = int(self.invoice_table.item(row, 3).text())
            price_text = self.invoice_table.item(row, 2).text().replace(' ج.م', '').strip()
            try:
                price = float(price_text)
            except ValueError:
                self.show_warning_dialog("خطأ", "لا يمكن تحويل السعر إلى رقم")
                return

            product_name = self.invoice_table.item(row, 1).text()
        except Exception as e:
            self.show_error_dialog("خطأ", f"حدث خطأ أثناء التحقق من الصلاحيات: {str(e)}")
            return

        # الحصول على معرف المنتج (إذا كان متاحاً)
        product_id = self.invoice_table.item(row, 1).data(Qt.UserRole)

        # الحصول على نوع المنتج
        product_type = self.invoice_table.item(row, 1).data(Qt.UserRole + 1) or 'physical'

        # لا نحتاج للتحقق من المخزون للخدمات
        available_quantity = None
        if product_type != 'service' and product_id:
            # الحصول على معلومات المنتج من قاعدة البيانات للتحقق من المخزون
            product = ProductModel.get_product_by_id(product_id)
            if product:
                # التحقق من وجود معلومات المخزون، سواء في 'quantity' أو 'stock'
                if 'quantity' in product:
                    available_quantity = product.get('quantity', 0)
                elif 'stock' in product:
                    available_quantity = product.get('stock', 0)
                else:
                    available_quantity = 0

        # إنشاء مربع حوار لتعديل الكمية مع تطبيق التنسيقات
        from styles import AppStyles

        # إنشاء QInputDialog وتطبيق التنسيقات عليه
        input_dialog = QInputDialog(self)
        input_dialog.setWindowTitle("تعديل الكمية")
        input_dialog.setLabelText(f"أدخل الكمية الجديدة للمنتج '{product_name}':")
        input_dialog.setInputMode(QInputDialog.IntInput)
        input_dialog.setIntValue(current_quantity)
        input_dialog.setIntRange(1, 9999)
        input_dialog.setIntStep(1)

        # تطبيق التنسيقات على النافذة
        input_dialog.setStyleSheet(AppStyles.get_input_dialog_style())

        # عرض النافذة والحصول على النتيجة
        ok = input_dialog.exec_() == QInputDialog.Accepted
        new_quantity = input_dialog.intValue() if ok else current_quantity

        if ok and new_quantity != current_quantity:
            # التحقق من توفر المخزون الكافي
            if product_type != 'service' and available_quantity is not None:
                # إذا كانت الكمية المطلوبة أكبر من المتوفرة
                if new_quantity > available_quantity:
                    self.show_warning_dialog(
                        "خطأ في المخزون",
                        f"الكمية المتوفرة في المخزون ({available_quantity}) أقل من الكمية المطلوبة ({new_quantity}).\n\nيرجى تقليل الكمية أو تحديث مخزون المنتج."
                    )
                    return

            # تحديث الكمية في الجدول
            quantity_item = QTableWidgetItem(str(new_quantity))
            quantity_item.setTextAlignment(Qt.AlignCenter)
            self.invoice_table.setItem(row, 3, quantity_item)

            # تحديث الإجمالي
            total = price * new_quantity
            total_item = QTableWidgetItem(f"{total:.2f} {CURRENCY_SYMBOL}")
            total_item.setTextAlignment(Qt.AlignCenter)
            self.invoice_table.setItem(row, 4, total_item)

            # تحديث إجمالي الفاتورة
            self.calculate_totals()

    def handle_invoice_table_key_press(self, event):
        """معالجة أحداث المفاتيح في جدول الفاتورة"""
        # معالجة مفتاح Delete لحذف العنصر المحدد
        if event.key() == Qt.Key_Delete:
            selected_rows = self.invoice_table.selectionModel().selectedRows()
            if selected_rows:
                # الحصول على الصف المحدد وحذفه
                row = selected_rows[0].row()
                self.remove_product_from_invoice(row)
        else:
            # استدعاء السلوك الافتراضي لمعالجة المفاتيح الأخرى
            QTableWidget.keyPressEvent(self.invoice_table, event)

    def swap_favorite_products(self, source_id, target_id):
        """تبديل موضع منتجين مفضلين"""
        try:
            # البحث عن المنتجات في القائمة
            source_index = -1
            target_index = -1

            for i, product in enumerate(self.favorite_products):
                if product.get('id') == source_id:
                    source_index = i
                elif product.get('id') == target_id:
                    target_index = i

                # إذا تم العثور على كليهما، توقف عن البحث
                if source_index >= 0 and target_index >= 0:
                    break

            # إذا تم العثور على كلا المنتجين، قم بتبديلهما
            if source_index >= 0 and target_index >= 0:
                # تبديل المنتجات في القائمة
                self.favorite_products[source_index], self.favorite_products[target_index] = \
                    self.favorite_products[target_index], self.favorite_products[source_index]

                # تحديث ترتيب المنتجات في قاعدة البيانات
                # حفظ الترتيب الجديد للمنتجات المفضلة
                for i, product in enumerate(self.favorite_products):
                    # تحديث ترتيب المنتج في قاعدة البيانات باستخدام الموضع الجديد
                    ProductModel.update_product_order(product.get('id'), i)

                # تحديث العرض بدون إعادة تحميل البيانات لتجنب فقدان التغييرات
                self.refresh_favorites_display(reload_data=False)

                print(f"تم تبديل منتج بمعرف {source_id} مع منتج بمعرف {target_id} وحفظ الترتيب الجديد")
        except Exception as e:
            print(f"خطأ في تبديل المنتجات المفضلة: {str(e)}")
            traceback.print_exc()

    def remove_from_favorites(self, product_id):
        """إزالة منتج من قائمة المفضلة"""
        try:
            # البحث عن المنتج في قاعدة البيانات
            product = ProductModel.get_product_by_id(product_id)

            if not product:
                self.show_warning_dialog("خطأ", "لم يتم العثور على المنتج")
                return

            # الحصول على اسم المنتج لعرضه في رسالة التأكيد
            product_name = product.get('name', '')

            # عرض رسالة تأكيد للمستخدم
            from utils.custom_widgets import show_question
            reply = show_question(
                self,
                "تأكيد الإزالة من المفضلة",
                f"هل أنت متأكد من إزالة المنتج '{product_name}' من المفضلة؟"
            )

            if reply:
                # تحديث حالة المفضلة في قاعدة البيانات
                success = ProductModel.toggle_favorite(product_id, False)

                if success:
                    # تحديث قائمة المفضلة
                    self.refresh_favorites_display()

                    # عرض رسالة نجاح
                    show_information(
                        self,
                        "تمت الإزالة من المفضلة",
                        f"تم إزالة '{product_name}' من المفضلة بنجاح"
                    )
                else:
                    show_warning(self, "خطأ", "فشلت عملية تحديث حالة المفضلة")

        except Exception as e:
            show_error(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")
            print(f"خطأ في إزالة المنتج من المفضلة: {str(e)}")
            traceback.print_exc()

    def change_favorite_button_color(self, product_id):
        """تغيير لون زر المنتج المفضل"""
        try:
            # البحث عن المنتج في قاعدة البيانات
            product = ProductModel.get_product_by_id(product_id)

            if not product:
                show_warning(self, "خطأ", "لم يتم العثور على المنتج")
                return

            # الحصول على اسم المنتج واللون الحالي
            product_name = product.get('name', '')
            current_color = product.get('button_color')

            # إنشاء مربع حوار اختيار اللون
            color_dialog = ColorSelectionDialog(self, product_name, current_color)

            # تنفيذ المربع وانتظار النتيجة
            if color_dialog.exec_() == QDialog.Accepted:
                # الحصول على اللون المختار
                selected_color = color_dialog.get_selected_color()

                # تحديث اللون في قاعدة البيانات
                self.update_product_color(product_id, product_name, selected_color)

        except Exception as e:
            show_error(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")
            print(f"خطأ في تغيير لون زر المنتج: {str(e)}")
            traceback.print_exc()

    def update_product_color(self, product_id, product_name, color_code):
        """تحديث لون زر المنتج المفضل في قاعدة البيانات"""
        try:
            # تحديث اللون في قاعدة البيانات
            success = ProductModel.update_product_button_color(product_id, color_code)

            if success:
                # تحديث قائمة المفضلة لعرض اللون الجديد
                self.refresh_favorites_display()

                # تحديد نص الرسالة بناءً على إذا كان إعادة إلى الافتراضي أم تغيير اللون
                if color_code is None:
                    message = f"تم إعادة لون زر المنتج '{product_name}' إلى اللون الافتراضي بنجاح"
                else:
                    message = f"تم تغيير لون زر المنتج '{product_name}' بنجاح"

                # عرض رسالة نجاح
                show_information(self, "تم تغيير اللون", message)
            else:
                show_warning(self, "خطأ", "فشلت عملية تحديث لون الزر")

        except Exception as e:
            show_error(self, "خطأ", f"حدث خطأ غير متوقع: {str(e)}")
            print(f"خطأ في تحديث لون زر المنتج: {str(e)}")
            traceback.print_exc()


class ColorSelectionDialog(QDialog):
    """نافذة حوار لاختيار لون زر المنتج المفضل"""
    def __init__(self, parent=None, product_name="", current_color=None):
        super().__init__(parent)

        # تعيين خصائص النافذة
        self.setWindowTitle("اختيار لون الزر")
        self.setFixedSize(400, 300)  # زيادة حجم النافذة قليلاً لاستيعاب المزيد من الألوان
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

        # تعريف الألوان المتاحة - 8 ألوان متناسقة مع استايل البرنامج
        self.available_colors = [
            {"name": "أزرق فاتح", "color": "#e3f2fd"},
            {"name": "أخضر فاتح", "color": "#e8f5e9"},
            {"name": "وردي فاتح", "color": "#fce4ec"},
            {"name": "برتقالي فاتح", "color": "#fff3e0"},
            {"name": "أصفر فاتح", "color": "#fff9c4"},
            {"name": "تركواز فاتح", "color": "#e0f7fa"},
            {"name": "بنفسجي فاتح", "color": "#f3e5f5"},
            {"name": "بيج فاتح", "color": "#efebe9"}
        ]

        # اللون المحدد حاليًا (سيتم تعيينه عند اختيار لون)
        self.selected_color = current_color

        # إعداد التخطيط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # عنوان المنتج
        title_label = QLabel(f"اختر لونًا للمنتج: {product_name}")
        title_label.setObjectName("dialog_title")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        layout.addWidget(separator)

        # معاينة الألوان المتاحة
        colors_layout = QGridLayout()
        colors_layout.setSpacing(8)  # تقليل المساحة بين الأزرار

        # إنشاء زر لكل لون متاح
        for i, color_info in enumerate(self.available_colors):
            # إنشاء زر اللون
            color_button = QPushButton(color_info["name"])
            color_button.setFixedSize(95, 35)  # تصغير حجم الأزرار
            color_button.setCursor(QCursor(Qt.PointingHandCursor))

            # تطبيق اللون كخلفية للزر ولون مناسب للنص
            color_button.setStyleSheet(f"""
                QPushButton {{
                    background-color: {color_info["color"]};
                    border: 1px solid {self.get_border_color(color_info["color"])};
                    border-radius: 4px;
                    color: #333333;
                    font-weight: bold;
                    font-size: 9pt;
                }}
                QPushButton:hover {{
                    background-color: {self.get_hover_color(color_info["color"])};
                    border: 1px solid {self.get_border_color(color_info["color"])};
                }}
            """)

            # تخزين اللون في خاصية الزر
            color_button.setProperty("color_value", color_info["color"])

            # ربط الزر بحدث اختيار اللون
            color_button.clicked.connect(self.on_color_selected)

            # تنظيم الأزرار في 4 أعمدة لاستيعاب الألوان الـ 8
            row = i // 4
            col = i % 4
            colors_layout.addWidget(color_button, row, col)

        # تنظيم تخطيط الألوان
        colors_container = QWidget()
        colors_container.setLayout(colors_layout)
        colors_container.setStyleSheet("background-color: transparent;")
        layout.addWidget(colors_container)

        # زر إعادة إلى اللون الافتراضي
        reset_button = QPushButton("إعادة إلى اللون الافتراضي")
        reset_button.setObjectName("default_button")
        reset_button.setMinimumHeight(40)
        reset_button.clicked.connect(self.on_reset_color)
        layout.addWidget(reset_button)

        # مساحة فارغة للتمدد
        layout.addStretch()

        # أزرار في الأسفل
        buttons_layout = QHBoxLayout()

        # زر إغلاق الحوار
        close_button = QPushButton("إغلاق")
        close_button.setObjectName("default_button")
        close_button.setMinimumHeight(40)
        close_button.setMinimumWidth(100)
        close_button.clicked.connect(self.reject)

        buttons_layout.addStretch()  # مساحة فارغة على اليمين
        buttons_layout.addWidget(close_button)
        buttons_layout.addStretch()  # مساحة فارغة على اليسار

        layout.addLayout(buttons_layout)

        # تطبيق النمط العام
        self.apply_styles()

    def get_border_color(self, background_color):
        """الحصول على لون مناسب للحدود استنادًا إلى لون الخلفية"""
        # ببساطة تغميق لون الخلفية للحدود
        return self.adjust_color_brightness(background_color, -30)

    def get_hover_color(self, background_color):
        """الحصول على لون مناسب للتأثير عند التمرير استنادًا إلى لون الخلفية"""
        # تغميق لون الخلفية لتأثير التمرير
        return self.adjust_color_brightness(background_color, -15)

    def adjust_color_brightness(self, color, amount):
        """تعديل سطوع لون سداسي عشري"""
        if not color.startswith('#'):
            return color

        # تحويل اللون السداسي عشري إلى RGB
        rgb_hex = color.lstrip('#')
        r, g, b = tuple(int(rgb_hex[i:i+2], 16) for i in (0, 2, 4))

        # تعديل مكونات RGB
        r = max(0, min(255, r + amount))
        g = max(0, min(255, g + amount))
        b = max(0, min(255, b + amount))

        # تحويل مرة أخرى إلى سداسي عشري
        return f'#{r:02x}{g:02x}{b:02x}'

    def on_color_selected(self):
        """معالجة حدث اختيار لون"""
        # الحصول على اللون المخزن في خاصية الزر المضغوط
        sender = self.sender()
        self.selected_color = sender.property("color_value")
        self.accept()

    def on_reset_color(self):
        """معالجة حدث إعادة اللون إلى الافتراضي"""
        self.selected_color = None
        self.accept()

    def get_selected_color(self):
        """إرجاع اللون المختار"""
        return self.selected_color

    def apply_styles(self):
        """تطبيق أنماط CSS على العناصر"""
        self.setStyleSheet("""
            QDialog {
                background-color: white;
                border-radius: 10px;
            }

            #dialog_title {
                color: #333;
                font-size: 16px;
                font-weight: bold;
                margin-bottom: 10px;
            }

            QPushButton#default_button {
                background-color: #f0f0f0;
                border: 1px solid #d0d0d0;
                border-radius: 4px;
                color: #333;
                font-weight: bold;
                padding: 8px 16px;
                min-height: 32px;
                font-size: 14px;
            }

            QPushButton#default_button:hover {
                background-color: #e0e0e0;
                border: 1px solid #c0c0c0;
            }

            QPushButton#default_button:pressed {
                background-color: #d0d0d0;
                border: 1px solid #b0b0b0;
            }
        """)


class ServicePriceDialog(QDialog):
    """نافذة حوار لتعديل سعر الخدمة قبل إضافتها إلى الفاتورة"""
    def __init__(self, parent=None, service_name="", default_price=0):
        super().__init__(parent)

        # تعيين خصائص النافذة
        self.setWindowTitle("تحديد سعر الخدمة")
        self.setMinimumWidth(400)
        self.setLayoutDirection(Qt.RightToLeft)

        # إعداد التخطيط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # عنوان الخدمة
        title_label = QLabel(f"الخدمة: {service_name}")
        title_label.setObjectName("dialog_title")
        title_label.setAlignment(Qt.AlignRight)
        layout.addWidget(title_label)

        # فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("separator")
        layout.addWidget(separator)

        # حقل السعر
        price_layout = QHBoxLayout()
        price_label = QLabel("سعر الخدمة:")
        price_label.setObjectName("field_label")
        price_label.setFixedWidth(100)

        self.price_input = QDoubleSpinBox()
        self.price_input.setObjectName("spinbox")
        self.price_input.setMinimumHeight(40)
        self.price_input.setMinimum(0)
        self.price_input.setMaximum(1000000)
        self.price_input.setDecimals(2)
        self.price_input.setSingleStep(1)
        self.price_input.setValue(default_price)
        self.price_input.setPrefix("ج.م ")
        self.price_input.setButtonSymbols(QDoubleSpinBox.NoButtons)  # إزالة أسهم زيادة ونقصان
        self.price_input.selectAll()

        price_layout.addWidget(price_label)
        price_layout.addWidget(self.price_input)
        layout.addLayout(price_layout)

        # حقل الملاحظة
        note_layout = QHBoxLayout()
        note_label = QLabel("ملاحظة:")
        note_label.setObjectName("field_label")
        note_label.setFixedWidth(100)

        self.note_input = QLineEdit()
        self.note_input.setObjectName("text_input")
        self.note_input.setMinimumHeight(40)
        self.note_input.setPlaceholderText("أدخل ملاحظة (اختياري)")

        # ربط مفتاح Enter بزر الإضافة أيضاً
        self.note_input.returnPressed.connect(self.accept)

        note_layout.addWidget(note_label)
        note_layout.addWidget(self.note_input)
        layout.addLayout(note_layout)

        # مساحة فارغة
        layout.addStretch()

        # أزرار الإجراءات
        button_layout = QHBoxLayout()
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.setObjectName("secondary_button")
        self.cancel_btn.setMinimumHeight(40)
        self.cancel_btn.clicked.connect(self.reject)

        self.save_btn = QPushButton("إضافة للفاتورة")
        self.save_btn.setObjectName("primary_button")
        self.save_btn.setMinimumHeight(40)
        self.save_btn.clicked.connect(self.accept)
        self.save_btn.setToolTip("إضافة الخدمة للفاتورة (Enter)")

        button_layout.addWidget(self.cancel_btn)
        button_layout.addWidget(self.save_btn)
        layout.addLayout(button_layout)

        # إضافة اختصار Enter لزر الإضافة
        from PyQt5.QtWidgets import QShortcut
        from PyQt5.QtGui import QKeySequence
        self.enter_shortcut = QShortcut(QKeySequence("Return"), self)
        self.enter_shortcut.activated.connect(self.accept)

        # تطبيق الأنماط
        self.setStyleSheet("""
            QDialog {
                background-color: white;
            }
            #dialog_title {
                font-size: 16px;
                font-weight: bold;
                color: #333;
                margin-bottom: 5px;
            }
            #field_label {
                color: #555;
                font-weight: bold;
            }
            QDoubleSpinBox, QLineEdit {
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 5px;
                background-color: #f9f9f9;
                font-size: 14px;
            }
            QDoubleSpinBox:focus, QLineEdit:focus {
                border-color: #3b82f6;
                background-color: white;
            }
            #separator {
                background-color: #eee;
                max-height: 1px;
            }
            #primary_button {
                background-color: #3b82f6;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }
            #primary_button:hover {
                background-color: #2563eb;
            }
            #secondary_button {
                background-color: white;
                color: #3b82f6;
                border: 1px solid #3b82f6;
                border-radius: 4px;
                padding: 8px 16px;
            }
            #secondary_button:hover {
                background-color: #f0f7ff;
            }
        """)

        # تركيز على حقل السعر
        self.price_input.setFocus()

    def get_price(self):
        """إرجاع السعر المعدل"""
        return self.price_input.value()

    def get_note(self):
        """إرجاع الملاحظة المضافة"""
        return self.note_input.text().strip()