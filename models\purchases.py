"""
وحدة إدارة المشتريات - توفر وظائف للتعامل مع المشتريات من الموردين في قاعدة البيانات
"""

from models.database import db
from models.suppliers import SupplierModel
from models.products import ProductModel
from utils.date_utils import DateTimeUtils
import datetime
import random
import string

class PurchaseModel:
    """نموذج التعامل مع بيانات المشتريات"""

    @staticmethod
    def get_all_purchases():
        """استرجاع جميع المشتريات من قاعدة البيانات"""
        query = """
            SELECT p.id, p.reference_number, p.date, p.subtotal, p.tax, p.discount,
                   p.total, p.paid_amount, p.remaining_amount, p.payment_method, p.status,
                   p.notes, s.name as supplier_name, s.id as supplier_id
            FROM purchases p
            LEFT JOIN suppliers s ON p.supplier_id = s.id
            ORDER BY p.date DESC
        """
        return db.fetch_all(query)

    @staticmethod
    def get_purchase_by_id(purchase_id):
        """استرجاع عملية شراء بواسطة المعرف"""
        query = """
            SELECT p.id, p.reference_number, p.date, p.subtotal, p.tax, p.discount,
                   p.total, p.paid_amount, p.remaining_amount, p.payment_method, p.status,
                   p.notes, s.name as supplier_name, s.id as supplier_id
            FROM purchases p
            LEFT JOIN suppliers s ON p.supplier_id = s.id
            WHERE p.id = ?
        """
        return db.fetch_one(query, (purchase_id,))

    @staticmethod
    def get_purchase_by_reference(reference_number):
        """استرجاع عملية شراء بواسطة الرقم المرجعي"""
        if not reference_number:
            print("[DEBUG] Referencia vacía, no se puede buscar")
            return None

        print(f"[DEBUG] Buscando compra con referencia: {reference_number}")
        query = """
            SELECT p.id, p.reference_number, p.date, p.subtotal, p.tax, p.discount,
                   p.total, p.paid_amount, p.remaining_amount, p.payment_method, p.status,
                   p.notes, s.name as supplier_name, p.supplier_id
            FROM purchases p
            LEFT JOIN suppliers s ON p.supplier_id = s.id
            WHERE p.reference_number = ?
            LIMIT 1
        """
        result = db.fetch_one(query, (reference_number,))
        if result:
            print(f"[DEBUG] Compra encontrada con ID: {result.get('id')}")
        else:
            print(f"[DEBUG] No se encontró compra con referencia: {reference_number}")

            # Intentar búsqueda alternativa
            alt_query = "SELECT id, reference_number FROM purchases WHERE reference_number LIKE ?"
            alt_result = db.fetch_all(alt_query, (f"%{reference_number}%",))
            if alt_result:
                print(f"[DEBUG] Resultados similares encontrados: {alt_result}")

        return result

    @staticmethod
    def search_purchases(search_text=None, start_date=None, end_date=None, status=None, supplier_id=None):
        """البحث عن مشتريات بواسطة معايير مختلفة"""
        print(f"[DEBUG] search_purchases - Iniciando búsqueda con supplier_id: {supplier_id}")

        base_query = """
            SELECT p.id, p.reference_number, p.date, p.subtotal, p.tax, p.discount,
                   p.total, p.paid_amount, p.remaining_amount, p.payment_method, p.status,
                   p.notes, s.name as supplier_name, p.supplier_id
            FROM purchases p
            LEFT JOIN suppliers s ON p.supplier_id = s.id
            WHERE 1=1
        """
        params = []

        # إضافة فلتر البحث النصي
        if search_text:
            base_query += " AND (p.reference_number LIKE ? OR s.name LIKE ? OR p.notes LIKE ?)"
            search_pattern = f"%{search_text}%"
            params.extend([search_pattern, search_pattern, search_pattern])
            print(f"[DEBUG] search_purchases - Filtrando por texto: '{search_text}'")

        # إضافة فلتر تاريخ البدء
        if start_date:
            base_query += " AND p.date >= ?"
            params.append(start_date)
            print(f"[DEBUG] search_purchases - Filtrando por fecha de inicio: {start_date}")

        # إضافة فلتر تاريخ الانتهاء
        if end_date:
            base_query += " AND p.date <= ?"
            params.append(end_date)
            print(f"[DEBUG] search_purchases - Filtrando por fecha fin: {end_date}")

        # إضافة فلتر الحالة
        if status and status != "الكل":
            base_query += " AND p.status = ?"
            params.append(status)
            print(f"[DEBUG] search_purchases - Filtrando por estado: {status}")

        # إضافة فلتر المورد
        if supplier_id:
            try:
                # Asegurar que supplier_id es entero وconvertirlo
                supplier_id_int = int(supplier_id)
                base_query += " AND p.supplier_id = ?"
                params.append(supplier_id_int)
                print(f"[DEBUG] search_purchases - Filtrando por proveedor ID: {supplier_id_int}")
            except (ValueError, TypeError):
                print(f"[DEBUG] search_purchases - Error convirtiendo supplier_id: {supplier_id}")

        # ترتيب النتائج حسب التاريخ (الأحدث أولاً)
        base_query += " ORDER BY p.date DESC"

        # Ejecutar la consulta con parámetros
        print(f"[DEBUG] search_purchases - Consulta SQL: {base_query}")
        print(f"[DEBUG] search_purchases - Parámetros: {params}")

        result = db.fetch_all(base_query, params)
        print(f"[DEBUG] search_purchases - Resultados encontrados: {len(result) if result else 0}")

        # Si hay resultados, mostrar la primera compra para depuración
        if result and len(result) > 0:
            print(f"[DEBUG] search_purchases - Primera compra: {result[0]}")

        return result

    @staticmethod
    def get_purchase_items(purchase_id):
        """استرجاع عناصر عملية شراء محددة"""
        query = """
            SELECT id, purchase_id, product_id, product_name, product_code,
                   quantity, unit_price, total_price
            FROM purchase_items
            WHERE purchase_id = ?
        """
        return db.fetch_all(query, (purchase_id,))

    @staticmethod
    def generate_reference_number():
        """توليد رقم مرجعي فريد لعملية الشراء"""
        # التاريخ الحالي بتنسيق yyyymmdd
        date_part = datetime.datetime.now().strftime("%Y%m%d")

        # 4 أحرف عشوائية
        random_part = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))

        # تنسيق الرقم المرجعي
        reference = f"PO-{date_part}-{random_part}"

        # التحقق من عدم وجود عملية شراء بنفس الرقم المرجعي
        while PurchaseModel.get_purchase_by_reference(reference):
            random_part = ''.join(random.choices(string.ascii_uppercase + string.digits, k=4))
            reference = f"PO-{date_part}-{random_part}"

        return reference

    @staticmethod
    def add_purchase(purchase_data, items_data):
        """إضافة عملية شراء جديدة مع عناصرها إلى قاعدة البيانات"""
        try:
            # بدء معاملة قاعدة البيانات
            if not db.conn:
                if not db.connect():
                    db.log_error("فشل الاتصال بقاعدة البيانات عند إنشاء عملية الشراء")
                    return None

            # التأكد من أن التاريخ يحتوي على الوقت
            if 'date' in purchase_data:
                purchase_data['date'] = DateTimeUtils.convert_old_date_to_datetime(purchase_data['date'])
            else:
                purchase_data['date'] = DateTimeUtils.get_current_date_time()

            # التأكد من وجود رقم مرجعي فريد
            if not purchase_data.get('reference_number'):
                purchase_data['reference_number'] = PurchaseModel.generate_reference_number()

            # IMPORTANTE: Verificar y convertir supplier_id explícitamente a entero
            try:
                if 'supplier_id' in purchase_data and purchase_data['supplier_id'] is not None:
                    purchase_data['supplier_id'] = int(purchase_data['supplier_id'])
                    print(f"[DEBUG] supplier_id convertido a entero: {purchase_data['supplier_id']}")
                else:
                    print("[DEBUG] ADVERTENCIA: supplier_id es None o no está presente")
                    # El proveedor es obligatorio, si no hay ID, no podemos continuar
                    db.log_error("Error: No se proporcionó un ID de proveedor válido")
                    return None
            except (ValueError, TypeError) as e:
                print(f"[DEBUG] Error al convertir supplier_id: {str(e)}")
                db.log_error(f"Error al convertir el ID del proveedor: {str(e)}")
                return None

            # Verificar y asegurar que los valores de pago sean válidos
            total = purchase_data.get('total', 0)
            paid_amount = purchase_data.get('paid_amount', 0)
            remaining_amount = purchase_data.get('remaining_amount', 0)

            # Validar montos
            if total < 0:
                total = 0
                purchase_data['total'] = total

            if paid_amount < 0:
                paid_amount = 0
                purchase_data['paid_amount'] = paid_amount

            if paid_amount > total:
                paid_amount = total
                purchase_data['paid_amount'] = paid_amount

            # Recalcular el monto restante
            purchase_data['remaining_amount'] = total - paid_amount

            # Determinar el estado basado en los montos
            if purchase_data['remaining_amount'] <= 0:
                purchase_data['status'] = "مدفوعة"
            else:
                purchase_data['status'] = "غير مدفوعة"

            # Guardar el reference_number para consultarlo después
            reference_number = purchase_data.get('reference_number')
            print(f"[DEBUG] Número de referencia para seguimiento: {reference_number}")

            # Imprimir los datos de compra para verificación
            print(f"[DEBUG] Datos de compra a insertar: {purchase_data}")

            # إدراج بيانات عملية الشراء الرئيسية
            query = """
                INSERT INTO purchases (
                    reference_number, supplier_id, date, subtotal,
                    tax, discount, total, paid_amount, remaining_amount,
                    payment_method, status, notes
                )
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                purchase_data.get('reference_number'),
                purchase_data.get('supplier_id'),
                purchase_data.get('date'),
                purchase_data.get('subtotal', 0),
                purchase_data.get('tax', 0),
                purchase_data.get('discount', 0),
                purchase_data.get('total', 0),
                purchase_data.get('paid_amount', 0),
                purchase_data.get('remaining_amount', 0),
                purchase_data.get('payment_method', 'نقدي'),
                purchase_data.get('status', 'مدفوعة'),
                purchase_data.get('notes', '')
            )

            print(f"[DEBUG] Ejecutando INSERT con parámetros: {params}")
            insert_result = db.execute(query, params)
            print(f"[DEBUG] INSERT ejecutado correctamente, resultado: {insert_result}")

            # Obtener el ID de la última inserción
            purchase_id = db.get_last_insert_id()
            print(f"[DEBUG] ID obtenido de get_last_insert_id(): {purchase_id}")

            # Si el ID es 0 o None, buscar por el número de referencia
            if not purchase_id:
                print("[DEBUG] El ID es 0 o None, intentando recuperar por referencia")
                # Usar directamente una consulta para obtener el ID
                query_id = """
                    SELECT id FROM purchases
                    WHERE reference_number = ?
                    ORDER BY id DESC LIMIT 1
                """
                id_result = db.fetch_one(query_id, (reference_number,))

                if id_result and 'id' in id_result:
                    purchase_id = id_result['id']
                    print(f"[DEBUG] ID recuperado por consulta directa: {purchase_id}")
                else:
                    # Intentar otra forma de consulta
                    all_purchases = db.fetch_all("SELECT id, reference_number FROM purchases ORDER BY id DESC LIMIT 10")
                    print(f"[DEBUG] Últimas 10 compras: {all_purchases}")

                    for purchase in all_purchases:
                        if purchase.get('reference_number') == reference_number:
                            purchase_id = purchase.get('id')
                            print(f"[DEBUG] ID encontrado en los últimos registros: {purchase_id}")
                            break

                    if not purchase_id:
                        print("[DEBUG] No se pudo recuperar el ID por ningún método")
                        db.rollback()
                        return None

            print(f"[DEBUG] ID de compra confirmado: {purchase_id}")

            # إدراج عناصر عملية الشراء
            if items_data and purchase_id:
                print(f"[DEBUG] Insertando {len(items_data)} items para la compra ID: {purchase_id}")
                for item in items_data:
                    # Registrar información de diagnóstico para cada elemento
                    print(f"[DEBUG] Procesando item: {item}")

                    item_query = """
                        INSERT INTO purchase_items (
                            purchase_id, product_id, product_name, product_code,
                            quantity, unit_price, total_price
                        )
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """

                    product_id = item.get('product_id')
                    product_name = item.get('product_name')
                    product_code = item.get('product_code')
                    quantity = item.get('quantity', 0)
                    unit_price = item.get('unit_price', 0)
                    total_price = item.get('total_price', 0) or (quantity * unit_price)

                    # Verificar datos de producto
                    print(f"[DEBUG] Product ID: {product_id}, Code: {product_code}, Name: {product_name}")

                    item_params = (
                        purchase_id,
                        product_id,
                        product_name,
                        product_code,
                        quantity,
                        unit_price,
                        total_price
                    )
                    db.execute(item_query, item_params)

                    # تحديث كمية المنتج في المخزون أو إضافة منتج جديد
                    if product_id:
                        # تحديث منتج موجود
                        print(f"[DEBUG] Actualizando producto existente ID: {product_id}")
                        product = ProductModel.get_product_by_id(product_id)
                        if product:
                            new_quantity = product.get('stock', 0) + quantity
                            print(f"[DEBUG] Actualizando cantidad de producto {product_id} de {product.get('stock', 0)} a {new_quantity}")
                            ProductModel.update_product_quantity(product_id, new_quantity)
                            # إصلاح: تحديث بيانات المنتج مع تمرير الكود الحالي دائماً
                            update_data = {
                                'code': product.get('code'),
                                'name': product.get('name'),
                                'description': product.get('description'),
                                'category': product.get('category'),
                                'price': product.get('price'),
                                'cost': item.get('cost', product.get('cost', 0)),
                                'stock': new_quantity,
                                'min_quantity': product.get('min_quantity', 1),
                                'image_path': product.get('image_path'),
                                'favorite': product.get('is_favorite', 0),
                                'product_type': product.get('product_type', 'physical')
                            }
                            ProductModel.update_product(product_id, update_data)
                        else:
                            print(f"[DEBUG] ADVERTENCIA: No se encontró producto con ID {product_id}")
                    elif product_code and product_name:
                        # إضافة منتج جديد أو تحديث منتج موجود بواسطة الكود
                        print(f"[DEBUG] Buscando producto por código: {product_code}")
                        product = ProductModel.get_product_by_code(product_code)
                        if product:
                            # تحديث الكمية
                            new_quantity = product.get('stock', 0) + quantity
                            print(f"[DEBUG] Producto encontrado por código, actualizando cantidad de {product.get('stock', 0)} a {new_quantity}")
                            ProductModel.update_product_quantity(product['id'], new_quantity)
                        else:
                            # إضافة منتج جديد إلى المخزون
                            print(f"[DEBUG] Producto no encontrado. Creando nuevo producto: {product_name}")
                            new_product = {
                                'code': product_code,
                                'name': product_name,
                                'description': '',
                                'category': item.get('category', 'غير مصنف'),
                                'price': item.get('sell_price', unit_price * 1.2),  # سعر البيع
                                'cost': unit_price,
                                'stock': quantity,  # Usar 'stock' en lugar de 'quantity' para compatibilidad con add_product
                                'quantity': quantity,  # Mantener 'quantity' por compatibilidad con otras partes del código
                                'min_quantity': item.get('min_quantity', 5),  # حد أدنى من العنصر
                                'is_favorite': item.get('is_favorite', 0)  # إضافة للمفضلة
                            }
                            new_product_id = ProductModel.add_product(new_product)
                            print(f"[DEBUG] Nuevo producto creado con ID: {new_product_id}")
                    else:
                        # No hay suficiente información para actualizar o crear un producto
                        print(f"[DEBUG] ADVERTENCIA: No hay suficiente información para el producto en item: {item}")

            # تحديث إجمالي مشتريات المورد
            supplier_id = purchase_data.get('supplier_id')
            total_amount = purchase_data.get('total', 0)
            if supplier_id:
                print(f"[DEBUG] Actualizando las compras totales del proveedor ID: {supplier_id}")
                SupplierModel.update_supplier_purchases(supplier_id, total_amount)

            # حفظ جميع التغييرات
            print("[DEBUG] Realizando commit de los cambios")
            db.commit()
            print(f"[DEBUG] Commit completo, retornando ID: {purchase_id}")
            return purchase_id

        except Exception as e:
            # التراجع عن التغييرات في حالة حدوث خطأ
            import traceback
            print("[DEBUG] Error en add_purchase:")
            print(traceback.format_exc())
            db.rollback()
            db.log_error(f"خطأ في إضافة عملية الشراء: {str(e)}")
            return None

    @staticmethod
    def update_purchase(purchase_id, purchase_data, items_data=None):
        """تحديث بيانات عملية شراء وعناصرها اختياريًا"""
        try:
            # بدء معاملة قاعدة البيانات
            if not db.conn:
                db.connect()

            # التأكد من أن التاريخ يحتوي على الوقت
            if 'date' in purchase_data:
                purchase_data['date'] = DateTimeUtils.convert_old_date_to_datetime(purchase_data['date'])

            # تحديث بيانات عملية الشراء الرئيسية
            query = """
                UPDATE purchases
                SET supplier_id = COALESCE(?, supplier_id),
                    date = COALESCE(?, date),
                    subtotal = COALESCE(?, subtotal),
                    tax = COALESCE(?, tax),
                    discount = COALESCE(?, discount),
                    total = COALESCE(?, total),
                    paid_amount = COALESCE(?, paid_amount),
                    remaining_amount = COALESCE(?, remaining_amount),
                    payment_method = COALESCE(?, payment_method),
                    status = COALESCE(?, status),
                    notes = COALESCE(?, notes)
                WHERE id = ?
            """
            params = (
                purchase_data.get('supplier_id', None),
                purchase_data.get('date', None),
                purchase_data.get('subtotal', None),
                purchase_data.get('tax', None),
                purchase_data.get('discount', None),
                purchase_data.get('total', None),
                purchase_data.get('paid_amount', None),
                purchase_data.get('remaining_amount', None),
                purchase_data.get('payment_method', None),
                purchase_data.get('status', None),
                purchase_data.get('notes', None),
                purchase_id
            )

            purchase_update_success = db.execute(query, params)

            # إذا تم توفير بيانات العناصر، فقم بتحديثها أيضًا
            if items_data is not None:
                # احصل على العناصر الحالية للفاتورة
                current_items = PurchaseModel.get_purchase_items(purchase_id)

                # حذف العناصر الحالية من الفاتورة
                delete_items_query = "DELETE FROM purchase_items WHERE purchase_id = ?"
                db.execute(delete_items_query, (purchase_id,))

                # إضافة العناصر الجديدة
                for item in items_data:
                    item_query = """
                        INSERT INTO purchase_items (
                            purchase_id, product_id, product_name, product_code,
                            quantity, unit_price, total_price
                        )
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """

                    # الحصول على سعر الوحدة والإجمالي
                    unit_price = 0
                    for price_field in ["unit_price", "cost", "price"]:
                        if price_field in item and item[price_field] is not None:
                            unit_price = item[price_field]
                            break

                    total_price = 0
                    for total_field in ["total_price", "total"]:
                        if total_field in item and item[total_field] is not None:
                            total_price = item[total_field]
                            break

                    # إذا لم يتم العثور على الإجمالي، حسابه من السعر والكمية
                    if total_price == 0 and unit_price > 0 and item.get("quantity", 0) > 0:
                        total_price = unit_price * item.get("quantity", 0)

                    item_params = (
                        purchase_id,
                        item.get('product_id'),
                        item.get('name') or item.get('product_name'),
                        item.get('product_code') or item.get('code', ''),
                        item.get('quantity', 0),
                        unit_price,
                        total_price
                    )
                    db.execute(item_query, item_params)

                # تحديث المخزون (يمكن إضافة هذه الميزة لاحقًا إذا لزم الأمر)

            # حفظ التغييرات
            db.commit()
            return True

        except Exception as e:
            import traceback
            print(f"[ERROR] خطأ في تحديث عملية الشراء: {str(e)}")
            print(traceback.format_exc())
            db.rollback()
            db.log_error(f"خطأ في تحديث عملية الشراء: {str(e)}")
            return False

    @staticmethod
    def delete_purchase(purchase_id):
        """حذف عملية شراء مع عناصرها من قاعدة البيانات"""
        try:
            # بدء معاملة قاعدة البيانات
            if not db.conn:
                db.connect()

            # استرجاع عناصر عملية الشراء وتحديث كميات المنتجات في المخزون
            items = PurchaseModel.get_purchase_items(purchase_id)
            for item in items:
                product_id = item.get('product_id')
                quantity = item.get('quantity', 0)
                if product_id:
                    product = ProductModel.get_product_by_id(product_id)
                    if product:
                        new_quantity = product.get('stock', 0) - quantity
                        if new_quantity < 0:
                            new_quantity = 0
                        ProductModel.update_product_quantity(product_id, new_quantity)

            # حذف عناصر عملية الشراء (سيتم حذفها تلقائيًا بسبب الـ ON DELETE CASCADE)
            # حذف عملية الشراء
            delete_query = "DELETE FROM purchases WHERE id = ?"
            db.execute(delete_query, (purchase_id,))

            # حفظ التغييرات
            db.commit()
            return True
        except Exception as e:
            # التراجع عن التغييرات في حالة حدوث خطأ
            db.rollback()
            db.log_error(f"خطأ في حذف عملية الشراء: {str(e)}")
            return False

    @staticmethod
    def get_purchase_stats():
        """استرجاع إحصائيات المشتريات"""
        # إجمالي عدد المشتريات
        total_query = "SELECT COUNT(*) as total FROM purchases"
        total_result = db.fetch_one(total_query)
        total_purchases = total_result.get('total', 0) if total_result else 0

        # إجمالي قيمة المشتريات
        value_query = "SELECT SUM(total) as total_value FROM purchases"
        value_result = db.fetch_one(value_query)
        total_value = value_result.get('total_value', 0) if value_result else 0

        # متوسط قيمة المشتريات
        if total_purchases > 0:
            avg_value = total_value / total_purchases
        else:
            avg_value = 0

        # عدد المشتريات غير المدفوعة
        unpaid_query = "SELECT COUNT(*) as count FROM purchases WHERE status = 'غير مدفوعة'"
        unpaid_result = db.fetch_one(unpaid_query)
        unpaid_count = unpaid_result.get('count', 0) if unpaid_result else 0

        # إجمالي المبالغ غير المدفوعة
        unpaid_amount_query = "SELECT SUM(remaining_amount) as total FROM purchases WHERE status = 'غير مدفوعة'"
        unpaid_amount_result = db.fetch_one(unpaid_amount_query)
        unpaid_amount = unpaid_amount_result.get('total', 0) if unpaid_amount_result else 0

        return {
            "total_purchases": total_purchases,
            "total_value": total_value,
            "avg_value": avg_value,
            "unpaid_count": unpaid_count,
            "unpaid_amount": unpaid_amount
        }

    @staticmethod
    def get_monthly_purchases(year, month):
        """استرجاع مشتريات شهر محدد"""
        # تنسيق تاريخ البداية والنهاية للشهر المحدد
        if month < 10:
            month_str = f"0{month}"
        else:
            month_str = str(month)

        start_date = f"{year}/{month_str}/01"

        # تحديد تاريخ نهاية الشهر
        if month == 12:
            end_date = f"{year+1}/01/01"
        else:
            next_month = month + 1
            if next_month < 10:
                next_month_str = f"0{next_month}"
            else:
                next_month_str = str(next_month)
            end_date = f"{year}/{next_month_str}/01"

        query = """
            SELECT p.id, p.reference_number, p.date, p.total, s.name as supplier_name, p.status
            FROM purchases p
            LEFT JOIN suppliers s ON p.supplier_id = s.id
            WHERE p.date >= ? AND p.date < ?
            ORDER BY p.date DESC
        """
        return db.fetch_all(query, (start_date, end_date))

    @staticmethod
    def debug_purchase_supplier_id(purchase_id):
        """Función de depuración para verificar el supplier_id de una compra"""
        try:
            query = """
                SELECT p.id, p.reference_number, p.supplier_id,
                       s.id as real_supplier_id, s.name as supplier_name
                FROM purchases p
                LEFT JOIN suppliers s ON p.supplier_id = s.id
                WHERE p.id = ?
            """
            result = db.fetch_one(query, (purchase_id,))
            if result:
                return result
            else:
                return None
        except Exception as e:
            return None

    @staticmethod
    def diagnose_supplier_id_issues():
        """Método de diagnóstico para analizar problemas con supplier_id en las compras"""
        try:
            # 1. Verificar todas las compras واسترجاع عناصرها وتحديث كميات المنتجات في المخزون
            all_purchases_query = """
                SELECT p.id, p.reference_number, p.supplier_id,
                       s.id as actual_supplier_id, s.name as supplier_name
                FROM purchases p
                LEFT JOIN suppliers s ON p.supplier_id = s.id
                ORDER BY p.id
            """
            purchases = db.fetch_all(all_purchases_query)

            # 2. Verificar النوع من البيانات في عمود supplier_id
            column_info_query = "PRAGMA table_info(purchases)"
            columns = db.fetch_all(column_info_query)

            supplier_id_column = None
            for col in columns:
                if col.get('name') == 'supplier_id':
                    supplier_id_column = col
                    break

            # المعلومات عن عمود supplier_id متوفرة إذا كانت ضرورية

            # 3. Verificar القيود على المفاتيح الخارجية
            fk_query = "PRAGMA foreign_key_list(purchases)"
            foreign_keys = db.fetch_all(fk_query)

            supplier_fk = None
            for fk in foreign_keys:
                if fk.get('from') == 'supplier_id':
                    supplier_fk = fk
                    break

            # المعلومات عن المفاتيح الخارجية متوفرة إذا كانت ضرورية

            # 4. Verificar الموردين
            suppliers_query = "SELECT id, name FROM suppliers ORDER BY id"
            suppliers = db.fetch_all(suppliers_query)

            return True

        except Exception as e:
            return False