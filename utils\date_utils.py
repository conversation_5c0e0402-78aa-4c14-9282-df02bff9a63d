#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
وحدة أدوات التاريخ والوقت - توفر وظائف مساعدة للتعامل مع التواريخ والأوقات
"""

import datetime
import random
import re

class DateTimeUtils:
    """فئة أدوات التاريخ والوقت"""
    
    @staticmethod
    def get_current_date_time():
        """الحصول على التاريخ والوقت الحاليين بالتنسيق المطلوب"""
        return datetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S")
    
    @staticmethod
    def get_current_date():
        """الحصول على التاريخ الحالي فقط"""
        return datetime.datetime.now().strftime("%Y/%m/%d")
    
    @staticmethod
    def get_current_time():
        """الحصول على الوقت الحالي فقط"""
        return datetime.datetime.now().strftime("%H:%M:%S")
    
    @staticmethod
    def convert_old_date_to_datetime(date_str):
        """تحويل التاريخ القديم (بدون وقت) إلى تاريخ ووقت"""
        if not date_str:
            return DateTimeUtils.get_current_date_time()
        
        # إذا كان التاريخ يحتوي بالفعل على وقت، أرجعه كما هو
        if ' ' in str(date_str) and ':' in str(date_str):
            return str(date_str)
        
        # إذا كان التاريخ بدون وقت، أضف وقت عشوائي
        try:
            # تحويل التاريخ إلى كائن datetime
            if '/' in str(date_str):
                date_obj = datetime.datetime.strptime(str(date_str), "%Y/%m/%d")
            elif '-' in str(date_str):
                date_obj = datetime.datetime.strptime(str(date_str), "%Y-%m-%d")
            else:
                # إذا لم يكن التنسيق معروف، استخدم التاريخ الحالي
                return DateTimeUtils.get_current_date_time()
            
            # إضافة وقت عشوائي (من 8 صباحاً إلى 10 مساءً)
            random_hour = random.randint(8, 22)
            random_minute = random.randint(0, 59)
            random_second = random.randint(0, 59)
            
            date_obj = date_obj.replace(hour=random_hour, minute=random_minute, second=random_second)
            return date_obj.strftime("%Y/%m/%d %H:%M:%S")
            
        except ValueError:
            # في حالة فشل التحويل، استخدم التاريخ والوقت الحاليين
            return DateTimeUtils.get_current_date_time()
    
    @staticmethod
    def format_date_for_display(date_str):
        """تنسيق التاريخ للعرض في الواجهة"""
        if not date_str:
            return ""
        
        try:
            # إذا كان التاريخ يحتوي على وقت
            if ' ' in str(date_str) and ':' in str(date_str):
                date_obj = datetime.datetime.strptime(str(date_str), "%Y/%m/%d %H:%M:%S")
                return date_obj.strftime("%Y/%m/%d %H:%M")
            else:
                # إذا كان التاريخ بدون وقت
                if '/' in str(date_str):
                    date_obj = datetime.datetime.strptime(str(date_str), "%Y/%m/%d")
                elif '-' in str(date_str):
                    date_obj = datetime.datetime.strptime(str(date_str), "%Y-%m-%d")
                else:
                    return str(date_str)
                
                return date_obj.strftime("%Y/%m/%d")
                
        except ValueError:
            return str(date_str)
    
    @staticmethod
    def format_date_for_table(date_str):
        """تنسيق التاريخ للعرض في الجداول"""
        if not date_str:
            return ""
        
        try:
            # إذا كان التاريخ يحتوي على وقت
            if ' ' in str(date_str) and ':' in str(date_str):
                date_obj = datetime.datetime.strptime(str(date_str), "%Y/%m/%d %H:%M:%S")
                return date_obj.strftime("%d/%m/%Y %H:%M")
            else:
                # إذا كان التاريخ بدون وقت
                if '/' in str(date_str):
                    date_obj = datetime.datetime.strptime(str(date_str), "%Y/%m/%d")
                elif '-' in str(date_str):
                    date_obj = datetime.datetime.strptime(str(date_str), "%Y-%m-%d")
                else:
                    return str(date_str)
                
                return date_obj.strftime("%d/%m/%Y")
                
        except ValueError:
            return str(date_str)
    
    @staticmethod
    def is_date_in_range(date_str, start_date, end_date):
        """التحقق من وجود التاريخ في نطاق معين"""
        if not date_str or not start_date or not end_date:
            return False
        
        try:
            # تحويل التواريخ إلى كائنات datetime للمقارنة
            if ' ' in str(date_str):
                check_date = datetime.datetime.strptime(str(date_str).split(' ')[0], "%Y/%m/%d")
            else:
                check_date = datetime.datetime.strptime(str(date_str), "%Y/%m/%d")
            
            start_dt = datetime.datetime.strptime(start_date, "%Y/%m/%d")
            end_dt = datetime.datetime.strptime(end_date, "%Y/%m/%d")
            
            return start_dt <= check_date <= end_dt
            
        except ValueError:
            return False
    
    @staticmethod
    def get_date_range_filter(filter_type):
        """الحصول على نطاق تاريخ بناءً على نوع الفلتر"""
        current_date = datetime.datetime.now()
        
        if filter_type == "اليوم":
            start_date = current_date.strftime("%Y/%m/%d")
            end_date = current_date.strftime("%Y/%m/%d")
        elif filter_type == "أمس":
            yesterday = current_date - datetime.timedelta(days=1)
            start_date = yesterday.strftime("%Y/%m/%d")
            end_date = yesterday.strftime("%Y/%m/%d")
        elif filter_type == "آخر أسبوع":
            start_date = (current_date - datetime.timedelta(days=7)).strftime("%Y/%m/%d")
            end_date = current_date.strftime("%Y/%m/%d")
        elif filter_type == "آخر شهر":
            start_date = (current_date - datetime.timedelta(days=30)).strftime("%Y/%m/%d")
            end_date = current_date.strftime("%Y/%m/%d")
        elif filter_type == "آخر 3 أشهر":
            start_date = (current_date - datetime.timedelta(days=90)).strftime("%Y/%m/%d")
            end_date = current_date.strftime("%Y/%m/%d")
        elif filter_type == "آخر سنة":
            start_date = (current_date - datetime.timedelta(days=365)).strftime("%Y/%m/%d")
            end_date = current_date.strftime("%Y/%m/%d")
        else:
            # الكل
            start_date = "1900/01/01"
            end_date = current_date.strftime("%Y/%m/%d")
        
        return start_date, end_date
    
    @staticmethod
    def update_database_dates(db):
        """تحديث التواريخ في قاعدة البيانات بإضافة أوقات عشوائية"""
        try:
            updated_count = 0
            
            # تحديث جدول الفواتير
            invoices = db.fetch_all("SELECT id, date FROM invoices WHERE date NOT LIKE '% %'")
            for invoice in invoices:
                new_date = DateTimeUtils.convert_old_date_to_datetime(invoice['date'])
                db.execute("UPDATE invoices SET date = ? WHERE id = ?", (new_date, invoice['id']))
                updated_count += 1
            
            # تحديث جدول المشتريات
            purchases = db.fetch_all("SELECT id, date FROM purchases WHERE date NOT LIKE '% %'")
            for purchase in purchases:
                new_date = DateTimeUtils.convert_old_date_to_datetime(purchase['date'])
                db.execute("UPDATE purchases SET date = ? WHERE id = ?", (new_date, purchase['id']))
                updated_count += 1
            
            # تحديث جدول العملاء - حقل آخر شراء
            customers = db.fetch_all("SELECT id, last_purchase FROM customers WHERE last_purchase IS NOT NULL AND last_purchase != '' AND last_purchase NOT LIKE '% %'")
            for customer in customers:
                new_date = DateTimeUtils.convert_old_date_to_datetime(customer['last_purchase'])
                db.execute("UPDATE customers SET last_purchase = ? WHERE id = ?", (new_date, customer['id']))
                updated_count += 1
            
            # تحديث جدول المدفوعات
            payments = db.fetch_all("SELECT id, payment_date FROM payments WHERE payment_date NOT LIKE '% %'")
            for payment in payments:
                new_date = DateTimeUtils.convert_old_date_to_datetime(payment['payment_date'])
                db.execute("UPDATE payments SET payment_date = ? WHERE id = ?", (new_date, payment['id']))
                updated_count += 1
            
            # حفظ التغييرات
            db.commit()
            
            return True, f"تم تحديث {updated_count} سجل بنجاح"
            
        except Exception as e:
            db.rollback()
            return False, f"خطأ في تحديث التواريخ: {str(e)}"
