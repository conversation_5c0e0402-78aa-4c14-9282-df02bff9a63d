"""
وحدة قاعدة البيانات - مسؤولة عن التعامل مع قاعدة البيانات وعمليات الاتصال والاستعلام
"""

import os
import sqlite3
import datetime
import json
import shutil
from PyQt5.QtWidgets import QMessageBox
from PyQt5.QtCore import QSettings


class Database:
    """
    فئة قاعدة البيانات - تدير الاتصال بقاعدة البيانات وتنفيذ الاستعلامات
    """
    _instance = None

    def __new__(cls):
        """تطبيق نمط Singleton للتأكد من وجود نسخة واحدة فقط من قاعدة البيانات"""
        if cls._instance is None:
            cls._instance = super(Database, cls).__new__(cls)
            cls._instance._initialized = False
        return cls._instance

    def __init__(self):
        """تهيئة متغيرات قاعدة البيانات"""
        if self._initialized:
            return

        self._initialized = True
        self.conn = None
        self.cursor = None
        self.db_type = "sqlite"
        self.settings = QSettings("MyCompany", "SmartManager")
        self.load_settings()

    def load_settings(self):
        """تحميل إعدادات قاعدة البيانات من الإعدادات المحفوظة"""
        # استخدام SQLite دائمًا
        self.db_type = "sqlite"
        self.db_path = self.settings.value("db_path", "database.db")

        # قيم احتياطية (غير مستخدمة فعليًا في النظام الحالي)
        self.db_host = ""
        self.db_port = 0
        self.db_name = ""
        self.db_user = ""
        self.db_password = ""

    def connect(self):
        """إنشاء اتصال بقاعدة البيانات"""
        try:
            # التحقق مما إذا كان هناك اتصال نشط بالفعل
            if self.conn:
                # إذا كان الاتصال مفتوحًا بالفعل، نعيد True
                return True

            # التحقق من وجود قاعدة البيانات
            db_exists = os.path.exists(self.db_path)

            # إنشاء اتصال بقاعدة البيانات
            self.conn = sqlite3.connect(self.db_path)
            self.conn.row_factory = sqlite3.Row
            self.cursor = self.conn.cursor()

            # طباعة رسالة تأكيد
            print("تم الاتصال بقاعدة البيانات بنجاح")

            # إذا لم تكن قاعدة البيانات موجودة، قم بإنشاء الجداول اللازمة
            if not db_exists:
                self.create_tables()

            # تحديث تركيب قاعدة البيانات إذا لزم الأمر
            self.update_schema()

            return True
        except Exception as e:
            print(f"خطأ في الاتصال بقاعدة البيانات: {str(e)}")
            self.log_error(f"خطأ في الاتصال بقاعدة البيانات: {str(e)}")
            return False

    def disconnect(self):
        """إغلاق الاتصال بقاعدة البيانات"""
        try:
            if self.conn:
                self.conn.close()
                self.conn = None
                self.cursor = None
                print("تم قطع الاتصال بقاعدة البيانات")
        except Exception as e:
            self.log_error(f"خطأ في قطع الاتصال بقاعدة البيانات: {str(e)}")

    def commit(self):
        """حفظ التغييرات في قاعدة البيانات"""
        try:
            if self.conn:
                self.conn.commit()
                print("تم حفظ التغييرات في قاعدة البيانات")
                return True
            else:
                self.log_error("محاولة حفظ التغييرات بدون اتصال مفتوح")
                print("محاولة حفظ التغييرات بدون اتصال مفتوح")
                return False
        except Exception as e:
            self.log_error(f"خطأ في حفظ التغييرات: {str(e)}")
            print(f"خطأ في حفظ التغييرات: {str(e)}")
            return False

    def rollback(self):
        """التراجع عن التغييرات غير المحفوظة"""
        try:
            if self.conn:
                self.conn.rollback()
                print("تم التراجع عن التغييرات غير المحفوظة")
                return True
            else:
                self.log_error("محاولة التراجع عن التغييرات بدون اتصال مفتوح")
                print("محاولة التراجع عن التغييرات بدون اتصال مفتوح")
                return False
        except Exception as e:
            self.log_error(f"خطأ في التراجع عن التغييرات: {str(e)}")
            print(f"خطأ في التراجع عن التغييرات: {str(e)}")
            return False

    def execute(self, query, params=None):
        """تنفيذ استعلام SQL بدون إرجاع نتائج"""
        try:
            if not self.conn:
                self.connect()

            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)

            return True
        except Exception as e:
            self.log_error(f"خطأ في تنفيذ الاستعلام: {str(e)}\nالاستعلام: {query}\nالمعلمات: {params}")
            return False

    def execute_many(self, query, params_list):
        """تنفيذ استعلام SQL مع قائمة من المعلمات"""
        try:
            if not self.conn:
                self.connect()

            self.cursor.executemany(query, params_list)
            return True
        except Exception as e:
            self.log_error(f"خطأ في تنفيذ الاستعلام المتعدد: {str(e)}\nالاستعلام: {query}")
            return False

    def fetch_one(self, query, params=None):
        """تنفيذ استعلام وإرجاع سجل واحد"""
        try:
            if not self.conn:
                self.connect()

            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)

            result = self.cursor.fetchone()

            # تحويل سجل sqlite3.Row إلى قاموس
            if result and self.db_type == "sqlite":
                return dict(result)
            return result
        except Exception as e:
            self.log_error(f"خطأ في تنفيذ استعلام واحد: {str(e)}\nالاستعلام: {query}\nالمعلمات: {params}")
            return None

    def fetch_all(self, query, params=None):
        """تنفيذ استعلام وإرجاع جميع السجلات"""
        try:
            if not self.conn:
                self.connect()

            if params:
                self.cursor.execute(query, params)
            else:
                self.cursor.execute(query)

            results = self.cursor.fetchall()

            # تحويل السجلات إلى قواميس في حالة SQLite
            if results and self.db_type == "sqlite":
                return [dict(row) for row in results]
            return results
        except Exception as e:
            self.log_error(f"خطأ في تنفيذ استعلام متعدد: {str(e)}\nالاستعلام: {query}\nالمعلمات: {params}")
            return []

    def get_last_insert_id(self):
        """الحصول على معرف آخر سجل تم إدراجه"""
        try:
            if self.db_type == "sqlite":
                result = self.cursor.lastrowid
                print(f"[DEBUG] cursor.lastrowid devuelve: {result}")

                # Si lastrowid es 0 o None, intentar obtenerlo mediante consulta
                if not result:
                    try:
                        # En SQLite, podemos usar la función last_insert_rowid()
                        self.cursor.execute("SELECT last_insert_rowid() as id")
                        query_result = self.cursor.fetchone()
                        if query_result:
                            if isinstance(query_result, dict):
                                result = query_result.get('id')
                            else:
                                result = query_result[0]
                            print(f"[DEBUG] SELECT last_insert_rowid() devuelve: {result}")
                    except Exception as e:
                        print(f"[DEBUG] Error al obtener last_insert_rowid(): {e}")

                # Si aún no tenemos un ID válido, intentar con sqlite_last_insert_rowid
                if not result:
                    try:
                        self.cursor.execute("SELECT sqlite_last_insert_rowid() as id")
                        query_result = self.cursor.fetchone()
                        if query_result:
                            if isinstance(query_result, dict):
                                result = query_result.get('id')
                            else:
                                result = query_result[0]
                            print(f"[DEBUG] SELECT sqlite_last_insert_rowid() devuelve: {result}")
                    except Exception as e:
                        print(f"[DEBUG] Error al obtener sqlite_last_insert_rowid(): {e}")

                return result
            else:
                # Para otros tipos de BD podríamos implementar otras formas
                return self.cursor.lastrowid
        except Exception as e:
            print(f"[DEBUG] Error en get_last_insert_id(): {str(e)}")
            import traceback
            print(traceback.format_exc())
            return None

    def create_tables(self):
        """إنشاء جداول قاعدة البيانات"""
        try:
            # إنشاء جدول المنتجات
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS products (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    code TEXT UNIQUE,
                    name TEXT NOT NULL,
                    description TEXT,
                    category TEXT,
                    price REAL,
                    cost REAL,
                    quantity INTEGER DEFAULT 0,
                    min_quantity INTEGER DEFAULT 0,
                    image_path TEXT,
                    is_favorite BOOLEAN DEFAULT 0,
                    product_type TEXT DEFAULT 'physical',
                    custom_order INTEGER DEFAULT 0,
                    button_color TEXT DEFAULT NULL,
                    created_at TEXT,
                    updated_at TEXT
                )
            ''')

            # إنشاء جدول العملاء
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS customers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    email TEXT,
                    phone TEXT,
                    address TEXT,
                    total_purchases REAL DEFAULT 0,
                    last_purchase TEXT,
                    visit_count INTEGER DEFAULT 0,
                    created_at TEXT,
                    updated_at TEXT
                )
            ''')

            # إنشاء جدول الموردين
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS suppliers (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    email TEXT,
                    phone TEXT,
                    address TEXT,
                    contact_person TEXT,
                    total_purchases REAL DEFAULT 0,
                    purchase_count INTEGER DEFAULT 0,
                    last_purchase TEXT,
                    notes TEXT,
                    created_at TEXT,
                    updated_at TEXT
                )
            ''')

            # إنشاء جدول الفواتير
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS invoices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    reference_number TEXT UNIQUE,
                    customer_id INTEGER,
                    date TEXT NOT NULL,
                    subtotal REAL,
                    tax REAL,
                    discount REAL,
                    total REAL,
                    paid_amount REAL,
                    remaining_amount REAL,
                    payment_method TEXT,
                    status TEXT,
                    notes TEXT,
                    created_at TEXT,
                    updated_at TEXT,
                    FOREIGN KEY (customer_id) REFERENCES customers (id)
                )
            ''')

            # إنشاء جدول عناصر الفاتورة
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS invoice_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_id INTEGER,
                    product_id INTEGER,
                    product_name TEXT NOT NULL,
                    product_code TEXT,
                    quantity INTEGER,
                    unit_price REAL,
                    total_price REAL,
                    is_paid BOOLEAN DEFAULT 0,
                    paid_amount REAL DEFAULT 0,
                    created_at TEXT,
                    updated_at TEXT,
                    FOREIGN KEY (invoice_id) REFERENCES invoices (id),
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            ''')

            # إنشاء جدول دفع المنتجات الفردية
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS invoice_item_payments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_item_id INTEGER,
                    customer_id INTEGER,
                    amount REAL NOT NULL,
                    payment_date TEXT NOT NULL,
                    notes TEXT,
                    user_id INTEGER,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (invoice_item_id) REFERENCES invoice_items (id),
                    FOREIGN KEY (customer_id) REFERENCES customers (id)
                )
            ''')

            # إنشاء جدول المشتريات (الذي كان مفقودًا)
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS purchases (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    reference_number TEXT UNIQUE,
                    supplier_id INTEGER,
                    date TEXT NOT NULL,
                    subtotal REAL,
                    tax REAL,
                    discount REAL,
                    total REAL,
                    paid_amount REAL,
                    remaining_amount REAL,
                    payment_method TEXT,
                    status TEXT,
                    notes TEXT,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT,
                    FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
                )
            ''')

            # إنشاء جدول عناصر المشتريات
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS purchase_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    purchase_id INTEGER,
                    product_id INTEGER,
                    product_name TEXT NOT NULL,
                    product_code TEXT,
                    quantity INTEGER,
                    unit_price REAL,
                    total_price REAL,
                    created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                    updated_at TEXT,
                    FOREIGN KEY (purchase_id) REFERENCES purchases (id) ON DELETE CASCADE,
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            ''')

            # إنشاء جدول المدفوعات
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS payments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    invoice_id INTEGER,
                    customer_id INTEGER,
                    amount REAL,
                    payment_method TEXT,
                    payment_date TEXT,
                    notes TEXT,
                    created_at TEXT,
                    updated_at TEXT,
                    FOREIGN KEY (invoice_id) REFERENCES invoices (id),
                    FOREIGN KEY (customer_id) REFERENCES customers (id)
                )
            ''')

            # إنشاء جدول حركة المخزون
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS inventory_transactions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    product_id INTEGER,
                    old_quantity INTEGER,
                    new_quantity INTEGER,
                    change_amount INTEGER,
                    transaction_type TEXT,
                    reference_id INTEGER,
                    notes TEXT,
                    created_at TEXT,
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            ''')

            # إنشاء جدول فواتير الشراء
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS purchase_invoices (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    reference_number TEXT UNIQUE,
                    supplier_id INTEGER,
                    date TEXT NOT NULL,
                    subtotal REAL,
                    tax REAL,
                    discount REAL,
                    total REAL,
                    paid_amount REAL,
                    remaining_amount REAL,
                    payment_method TEXT,
                    status TEXT,
                    notes TEXT,
                    created_at TEXT,
                    updated_at TEXT,
                    FOREIGN KEY (supplier_id) REFERENCES suppliers (id)
                )
            ''')

            # إنشاء جدول عناصر فاتورة الشراء
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS purchase_invoice_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    purchase_invoice_id INTEGER,
                    product_id INTEGER,
                    product_name TEXT NOT NULL,
                    product_code TEXT,
                    quantity INTEGER,
                    unit_price REAL,
                    discount REAL,
                    total_price REAL,
                    created_at TEXT,
                    updated_at TEXT,
                    FOREIGN KEY (purchase_invoice_id) REFERENCES purchase_invoices (id),
                    FOREIGN KEY (product_id) REFERENCES products (id)
                )
            ''')

            # إنشاء جدول إعدادات النظام
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS settings (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT UNIQUE,
                    value TEXT,
                    group_name TEXT,
                    created_at TEXT,
                    updated_at TEXT
                )
            ''')

            # إنشاء جدول للمصروفات
            self.cursor.execute('''
                CREATE TABLE IF NOT EXISTS expenses (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    date TEXT NOT NULL,
                    category TEXT NOT NULL,
                    amount REAL NOT NULL,
                    description TEXT,
                    created_at TEXT,
                    updated_at TEXT
                )
            ''')

            # تأكيد التغييرات
            self.conn.commit()
            print("تم إنشاء الجداول بنجاح")

            # إنشاء الإعدادات الافتراضية إذا لم تكن موجودة
            try:
                self.initialize_default_settings()
            except Exception as e:
                print(f"خطأ في إنشاء الجداول: {str(e)}")

            # التحقق من أعمدة جدول المدفوعات وتحديثه إذا لزم الأمر
            try:
                # التحقق من وجود عمود user_id في جدول المدفوعات
                check_query = "PRAGMA table_info(payments)"
                columns = self.fetch_all(check_query)
                column_names = [col['name'] for col in columns]
                print(f"أعمدة جدول المدفوعات الحالية: {column_names}")

                # إضافة عمود user_id إذا لم يكن موجوداً
                if 'user_id' not in column_names:
                    self.execute("ALTER TABLE payments ADD COLUMN user_id INTEGER")
                    print("تم إضافة عمود user_id إلى جدول المدفوعات")

                # حفظ التغييرات
                self.commit()
                print("تم حفظ التغييرات في قاعدة البيانات")
            except Exception as e:
                self.log_error(f"خطأ في تحديث بنية جدول المدفوعات: {str(e)}")

        except Exception as e:
            self.log_error(f"خطأ في إنشاء الجداول: {str(e)}")
            raise

    def backup_database(self, backup_path=None):
        """إنشاء نسخة احتياطية من قاعدة البيانات"""
        try:
            # إذا لم يتم تحديد مسار، استخدم المسار الافتراضي مع طابع زمني
            if not backup_path:
                timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = f"backups/backup_{timestamp}.db"

            # التأكد من وجود مجلد النسخ الاحتياطي
            backup_dir = os.path.dirname(backup_path)
            if backup_dir:  # فقط إذا كان هناك مجلد في المسار
                os.makedirs(backup_dir, exist_ok=True)

            # إغلاق الاتصال الحالي لتحرير قاعدة البيانات
            if self.conn:
                self.disconnect()

            # نسخ ملف قاعدة البيانات
            shutil.copy2(self.db_path, backup_path)

            # إعادة فتح الاتصال
            self.connect()

            return backup_path
        except Exception as e:
            self.log_error(f"خطأ في إنشاء نسخة احتياطية: {str(e)}")
            return None

    def restore_database(self, backup_path):
        """استعادة قاعدة البيانات من نسخة احتياطية"""
        try:
            # التحقق من وجود ملف النسخة الاحتياطية
            if not os.path.exists(backup_path):
                raise Exception(f"ملف النسخة الاحتياطية غير موجود: {backup_path}")

            # إغلاق الاتصال الحالي
            if self.conn:
                self.disconnect()

            # حفظ نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            temp_backup = f"backups/before_restore_{timestamp}.db"
            os.makedirs(os.path.dirname(temp_backup), exist_ok=True)

            if os.path.exists(self.db_path):
                shutil.copy2(self.db_path, temp_backup)

            # استبدال قاعدة البيانات الحالية بالنسخة الاحتياطية
            shutil.copy2(backup_path, self.db_path)

            # إعادة فتح الاتصال
            self.connect()

            return True
        except Exception as e:
            self.log_error(f"خطأ في استعادة النسخة الاحتياطية: {str(e)}")
            return False

    def vacuum_database(self):
        """إعادة تنظيم قاعدة البيانات (فقط SQLite)"""
        try:
            self.execute("VACUUM")
            return True
        except Exception as e:
            self.log_error(f"خطأ في إعادة تنظيم قاعدة البيانات: {str(e)}")
            return False

    def reset_database(self):
        """إعادة تعيين قاعدة البيانات وحذف جميع البيانات"""
        try:
            # قائمة الجداول للحذف
            tables = [
                "invoice_items", "invoices",
                "purchase_items", "purchases",
                "products", "customers", "suppliers",
                "users", "settings"
            ]

            # حذف جميع البيانات من الجداول
            for table in tables:
                self.execute(f"DROP TABLE IF EXISTS {table}")

            # إعادة إنشاء الجداول
            self.create_tables()

            return True
        except Exception as e:
            self.log_error(f"خطأ في إعادة تعيين قاعدة البيانات: {str(e)}")
            return False

    def test_connection(self):
        """اختبار الاتصال بقاعدة البيانات"""
        try:
            if not self.conn:
                connected = self.connect()
                if not connected:
                    return False

            # اختبار تنفيذ استعلام بسيط للتأكد من سلامة قاعدة البيانات
            self.execute("SELECT sqlite_version()")

            return True
        except Exception as e:
            self.log_error(f"فشل اختبار الاتصال: {str(e)}")
            return False
        finally:
            self.disconnect()

    def test_write_permissions(self):
        """اختبار صلاحيات الكتابة في قاعدة البيانات"""
        try:
            # محاولة إنشاء جدول مؤقت
            create_query = """
                CREATE TABLE IF NOT EXISTS test_permissions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    test_value TEXT
                )
            """
            self.execute(create_query)

            # محاولة إدراج بيانات
            insert_query = "INSERT INTO test_permissions (test_value) VALUES (?)"
            self.execute(insert_query, ("test",))

            # محاولة حذف البيانات
            delete_query = "DELETE FROM test_permissions"
            self.execute(delete_query)

            # محاولة حذف الجدول المؤقت
            drop_query = "DROP TABLE IF EXISTS test_permissions"
            self.execute(drop_query)

            # التزامن بالتغييرات
            self.commit()

            return True, "تم اختبار صلاحيات الكتابة بنجاح"
        except Exception as e:
            self.rollback()
            error_message = f"فشل اختبار صلاحيات الكتابة: {str(e)}"
            self.log_error(error_message)
            return False, error_message

    def log_error(self, error_message):
        """تسجيل رسالة الخطأ في ملف السجل"""
        try:
            timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            with open("error_log.txt", "a", encoding="utf-8") as f:
                f.write(f"{timestamp} - DATABASE ERROR: {error_message}\n")
        except:
            pass  # تجاهل أخطاء تسجيل الخطأ

    def update_schema(self):
        """تحديث تركيب قاعدة البيانات إذا لزم الأمر"""
        try:
            # الحصول على سجل الإصدارات المطبقة
            self.execute("CREATE TABLE IF NOT EXISTS schema_migrations (version INTEGER PRIMARY KEY, applied_at TEXT)")
            version_rec = self.fetch_one("SELECT MAX(version) as version FROM schema_migrations")
            current_version = version_rec['version'] if version_rec and version_rec['version'] else 0

            # تطبيق التحديثات المتتالية
            if current_version < 1:
                # الإصدار 1: إنشاء الجداول الأساسية للمنتجات والمبيعات
                print("تطبيق تحديث قاعدة البيانات: الإصدار 1")

                # إنشاء جدول المنتجات
                self.execute('''
                    CREATE TABLE IF NOT EXISTS products (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        code TEXT UNIQUE,
                        name TEXT NOT NULL,
                        description TEXT,
                        category TEXT,
                        type TEXT,
                        unit TEXT,
                        buy_price REAL DEFAULT 0,
                        sell_price REAL DEFAULT 0,
                        min_price REAL,
                        min_qty REAL DEFAULT 0,
                        qty_in_stock REAL DEFAULT 0,
                        location TEXT,
                        image_path TEXT,
                        is_active INTEGER DEFAULT 1,
                        created_at TEXT,
                        updated_at TEXT
                    )
                ''')

                # إنشاء جدول المبيعات
                self.execute('''
                    CREATE TABLE IF NOT EXISTS sales (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        invoice_number TEXT UNIQUE,
                        customer_id INTEGER,
                        customer_name TEXT,
                        total_amount REAL,
                        discount REAL DEFAULT 0,
                        tax REAL DEFAULT 0,
                        final_amount REAL,
                        paid_amount REAL,
                        remaining_amount REAL,
                        payment_method TEXT,
                        sales_date TEXT,
                        status TEXT,
                        notes TEXT,
                        created_at TEXT,
                        updated_at TEXT,
                        FOREIGN KEY (customer_id) REFERENCES customers(id)
                    )
                ''')

                # إنشاء جدول تفاصيل المبيعات
                self.execute('''
                    CREATE TABLE IF NOT EXISTS sale_details (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sale_id INTEGER,
                        product_id INTEGER,
                        product_code TEXT,
                        product_name TEXT,
                        quantity REAL,
                        unit TEXT,
                        unit_price REAL,
                        discount REAL DEFAULT 0,
                        tax REAL DEFAULT 0,
                        total_price REAL,
                        created_at TEXT,
                        updated_at TEXT,
                        FOREIGN KEY (sale_id) REFERENCES sales(id),
                        FOREIGN KEY (product_id) REFERENCES products(id)
                    )
                ''')

                # إنشاء جدول العملاء
                self.execute('''
                    CREATE TABLE IF NOT EXISTS customers (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL,
                        phone TEXT,
                        email TEXT,
                        address TEXT,
                        notes TEXT,
                        is_active INTEGER DEFAULT 1,
                        created_at TEXT,
                        updated_at TEXT
                    )
                ''')

                # تحديث جدول invoice_items لإضافة أعمدة الدفع الجزئي إذا لم تكن موجودة
                try:
                    # التحقق من وجود الأعمدة الجديدة
                    columns_info = self.fetch_all("PRAGMA table_info(invoice_items)")
                    columns = [col['name'] for col in columns_info] if columns_info else []

                    columns_added = False

                    if 'is_paid' not in columns:
                        self.execute("ALTER TABLE invoice_items ADD COLUMN is_paid BOOLEAN DEFAULT 0")
                        print("تم إضافة عمود is_paid إلى جدول invoice_items")
                        columns_added = True

                    if 'paid_amount' not in columns:
                        self.execute("ALTER TABLE invoice_items ADD COLUMN paid_amount REAL DEFAULT 0")
                        print("تم إضافة عمود paid_amount إلى جدول invoice_items")
                        columns_added = True

                    # تحديث البيانات الموجودة إذا تم إضافة أعمدة جديدة
                    if columns_added:
                        # تحديث جميع المنتجات الموجودة لتكون غير مدفوعة بشكل افتراضي
                        self.execute("UPDATE invoice_items SET is_paid = 0, paid_amount = 0 WHERE is_paid IS NULL OR paid_amount IS NULL")
                        print("تم تحديث البيانات الموجودة في جدول invoice_items")

                except Exception as e:
                    print(f"تحذير: فشل في تحديث جدول invoice_items: {e}")

                # إنشاء جدول دفع المنتجات الفردية إذا لم يكن موجوداً
                self.execute('''
                    CREATE TABLE IF NOT EXISTS invoice_item_payments (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        invoice_item_id INTEGER,
                        customer_id INTEGER,
                        amount REAL NOT NULL,
                        payment_date TEXT NOT NULL,
                        notes TEXT,
                        user_id INTEGER,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (invoice_item_id) REFERENCES invoice_items (id),
                        FOREIGN KEY (customer_id) REFERENCES customers (id)
                    )
                ''')

                # إنشاء جدول المستخدمين
                self.execute('''
                    CREATE TABLE IF NOT EXISTS users (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        username TEXT NOT NULL UNIQUE,
                        full_name TEXT NOT NULL,
                        password TEXT NOT NULL,
                        role TEXT NOT NULL DEFAULT 'كاشير',
                        is_active INTEGER DEFAULT 1,
                        last_login TEXT,
                        created_at TEXT,
                        updated_at TEXT
                    )
                ''')

                # إنشاء جدول صلاحيات الأدوار
                self.execute('''
                    CREATE TABLE IF NOT EXISTS role_permissions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        role TEXT NOT NULL,
                        permissions TEXT NOT NULL,
                        created_at TEXT,
                        updated_at TEXT
                    )
                ''')

                # تسجيل تطبيق الإصدار
                now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self.execute("INSERT INTO schema_migrations (version, applied_at) VALUES (?, ?)", (1, now))
                self.commit()

            # اكتب المزيد من التحديثات هنا...

            # الإصدار 2: إضافة جدول صلاحيات المستخدمين
            if current_version < 2:
                print("تطبيق تحديث قاعدة البيانات: الإصدار 2")

                # إنشاء جدول صلاحيات المستخدمين
                self.execute('''
                    CREATE TABLE IF NOT EXISTS user_permissions (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        user_id INTEGER NOT NULL,
                        permissions TEXT NOT NULL,
                        created_at TEXT DEFAULT CURRENT_TIMESTAMP,
                        updated_at TEXT,
                        FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
                    )
                ''')

                # تسجيل تطبيق الإصدار
                now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                self.execute("INSERT INTO schema_migrations (version, applied_at) VALUES (?, ?)", (2, now))
                self.commit()

            # طباعة الإصدار الحالي
            version_rec = self.fetch_one("SELECT MAX(version) as version FROM schema_migrations")
            if version_rec and version_rec['version']:
                print(f"الإصدار الحالي لقاعدة البيانات: {version_rec['version']}")

        except Exception as e:
            self.rollback()
            print(f"خطأ في تحديث تركيب قاعدة البيانات: {str(e)}")
            self.log_error(f"خطأ في تحديث تركيب قاعدة البيانات: {str(e)}")

    def migrate_purchase_data(self):
        """نقل البيانات من جدول purchase_invoices إلى جدول purchases إذا لزم الأمر"""
        try:
            # التحقق من وجود جدول purchase_invoices
            check_table_query = "SELECT name FROM sqlite_master WHERE type='table' AND name='purchase_invoices'"
            table_exists = self.fetch_one(check_table_query)

            if not table_exists:
                print("لا يوجد جدول purchase_invoices، لا حاجة للترحيل.")
                return True

            # التحقق من وجود بيانات في جدول purchase_invoices
            check_data_query = "SELECT COUNT(*) as count FROM purchase_invoices"
            data_count = self.fetch_one(check_data_query)

            if not data_count or data_count.get('count', 0) == 0:
                print("لا توجد بيانات في جدول purchase_invoices، لا حاجة للترحيل.")
                return True

            # التحقق من وجود بيانات في جدول purchases
            check_purchases_query = "SELECT COUNT(*) as count FROM purchases"
            purchases_count = self.fetch_one(check_purchases_query)

            if purchases_count and purchases_count.get('count', 0) > 0:
                print(f"يوجد بالفعل {purchases_count.get('count')} سجل في جدول purchases، تخطي الترحيل لتجنب التكرار.")
                return True

            # نقل البيانات من purchase_invoices إلى purchases
            migration_query = """
                INSERT INTO purchases (
                    reference_number, supplier_id, date, subtotal, tax, discount,
                    total, paid_amount, remaining_amount, payment_method, status, notes,
                    created_at, updated_at
                )
                SELECT
                    reference_number, supplier_id, date, subtotal, tax, discount,
                    total, paid_amount, remaining_amount, payment_method, status, notes,
                    created_at, updated_at
                FROM purchase_invoices
            """

            self.execute(migration_query)

            # نقل عناصر الفواتير
            # أولا، نحصل على تعيين بين المعرّفات القديمة والجديدة
            mapping_query = """
                SELECT pi.id as old_id, p.id as new_id
                FROM purchase_invoices pi
                JOIN purchases p ON pi.reference_number = p.reference_number
            """
            id_mapping = self.fetch_all(mapping_query)

            if id_mapping:
                print(f"تم إيجاد {len(id_mapping)} معرّف للترحيل")

                # نقل البيانات لكل سجل بمعرّفه الجديد
                for mapping in id_mapping:
                    old_id = mapping.get('old_id')
                    new_id = mapping.get('new_id')

                    if old_id and new_id:
                        items_migration_query = """
                            INSERT INTO purchase_items (
                                purchase_id, product_id, product_name, product_code,
                                quantity, unit_price, total_price
                            )
                            SELECT
                                ?, product_id, product_name, product_code,
                                quantity, unit_price, total_price
                            FROM purchase_invoice_items
                            WHERE purchase_invoice_id = ?
                        """

                        self.execute(items_migration_query, (new_id, old_id))

            # حفظ التغييرات
            self.commit()
            print(f"تم ترحيل {data_count.get('count')} فاتورة شراء بنجاح.")
            return True

        except Exception as e:
            self.log_error(f"خطأ أثناء ترحيل بيانات المشتريات: {str(e)}")
            self.rollback()
            return False

    def initialize_default_settings(self):
        """تهيئة الإعدادات الافتراضية في قاعدة البيانات"""
        try:
            # التحقق من وجود جدول الإعدادات
            check_table_query = "SELECT name FROM sqlite_master WHERE type='table' AND name='settings'"
            table_exists = self.fetch_one(check_table_query)

            if not table_exists:
                print("جدول الإعدادات غير موجود، سيتم تخطي تهيئة الإعدادات")
                return

            # الإعدادات الافتراضية
            default_settings = [
                {"name": "company_name", "value": "شركتي", "group_name": "general"},
                {"name": "company_phone", "value": "*********", "group_name": "general"},
                {"name": "company_address", "value": "عنوان الشركة", "group_name": "general"},
                {"name": "tax_rate", "value": "14", "group_name": "invoice"},
                {"name": "currency", "value": "ج.م", "group_name": "invoice"},
                {"name": "invoice_footer_text", "value": "شكراً لتعاملكم معنا", "group_name": "invoice"}
            ]

            # إضافة الإعدادات الافتراضية إذا لم تكن موجودة
            for setting in default_settings:
                # التحقق من وجود الإعداد
                check_query = "SELECT id FROM settings WHERE name = ?"
                exists = self.fetch_one(check_query, (setting["name"],))

                if not exists:
                    # إضافة الإعداد
                    insert_query = """
                        INSERT INTO settings (name, value, group_name, created_at, updated_at)
                        VALUES (?, ?, ?, ?, ?)
                    """
                    now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                    self.execute(insert_query, (
                        setting["name"],
                        setting["value"],
                        setting["group_name"],
                        now,
                        now
                    ))

            # حفظ التغييرات
            self.commit()

        except Exception as e:
            self.log_error(f"خطأ في تهيئة الإعدادات الافتراضية: {str(e)}")
            raise

# تصدير كائن قاعدة البيانات للاستخدام في الوحدات الأخرى
db = Database()