# -*- coding: utf-8 -*-
from PyQt5.QtWidgets import (QW<PERSON>t, QVBoxLayout, QLabel, QPushButton, QHBoxLayout,
                          QFrame, QGridLayout, QSizePolicy, QTableWidget, QTableWidgetItem, QHeaderView,
                          QComboBox, QLineEdit, QDialog, QMessageBox, QDateEdit, QFormLayout,
                          QDoubleSpinBox, QTextEdit, QAbstractItemView, QSpacerItem, QGroupBox,
                          QMenu, QAction, QCompleter)
from PyQt5.QtCore import Qt, QDate, QStringListModel
from PyQt5.QtGui import QFont, QIcon

from styles import AppStyles  # استيراد التنسيقات
import datetime
from models.database import db

# تعريف فئات مساعدة للدعم الكامل للغة العربية
class RTLComboBox(QComboBox):
    """استبدال QComboBox للدعم الكامل للغة العربية"""
    def __init__(self, parent=None, is_editable=False):
        super().__init__(parent)
        # تعيين محاذاة النص للوسط
        self.setEditable(True)
        self.lineEdit().setReadOnly(not is_editable)
        self.lineEdit().setAlignment(Qt.AlignCenter)
        # تحسين مظهر الخط وعدم عرض الإطار
        self.lineEdit().setStyleSheet("background: transparent; border: none;")
        # تمكين فتح القائمة المنسدلة بالنقر
        self.lineEdit().installEventFilter(self)

    def eventFilter(self, obj, event):
        if obj is self.lineEdit() and event.type() == event.MouseButtonPress:
            # فقط إذا كان للقراءة فقط نفتح القائمة المنسدلة عند النقر
            if self.lineEdit().isReadOnly():
                self.showPopup()
                return True
        return super().eventFilter(obj, event)

    def setRealEditable(self, editable):
        """تعيين الكومبوبوكس للسماح بالتحرير الفعلي للنص"""
        self.lineEdit().setReadOnly(not editable)


# تم إزالة فئة AutoCompleteComboBox لأننا نستخدم QLineEdit بدلاً من ها

def apply_rtl_to_all_widgets(widget):
    """تطبيق الاتجاه من اليمين إلى اليسار على جميع العناصر"""
    pass

# ثوابت التطبيق
CURRENCY = "جنيه"  # العملة المستخدمة في التطبيق
CURRENCY_SYMBOL = "ج.م"  # رمز العملة المصرية

class ExpensesModel:
    """نموذج البيانات للمصروفات"""

    @staticmethod
    def add_expense(expense_data):
        """إضافة مصروف جديد"""
        try:
            query = """
                INSERT INTO expenses (
                    date, category, amount, description, created_at, updated_at
                ) VALUES (?, ?, ?, ?, ?, ?)
            """
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            params = (
                expense_data['date'],
                expense_data['category'],
                expense_data['amount'],
                expense_data['description'],
                current_time,
                current_time
            )

            db.execute(query, params)
            db.commit()

            # تحديث أو إضافة الفئة في جدول فئات المصروفات
            ExpensesModel.save_expense_category(expense_data['category'])

            return True
        except Exception as e:
            return False

    @staticmethod
    def get_all_expenses(start_date=None, end_date=None, category=None):
        """الحصول على جميع المصروفات"""
        try:
            query = """
                SELECT id, date, category, amount, description, created_at
                FROM expenses
                WHERE 1=1
            """
            params = []

            # إضافة تصفية حسب التاريخ
            if start_date:
                query += " AND date >= ?"
                params.append(start_date)
            if end_date:
                query += " AND date <= ?"
                params.append(end_date)

            # ترتيب حسب التاريخ بترتيب تنازلي (الأحدث أولاً)
            query += " ORDER BY date DESC"

            expenses = db.fetch_all(query, params)
            return expenses if expenses else []
        except Exception as e:
            return []

    @staticmethod
    def delete_expense(expense_id):
        """حذف مصروف"""
        try:
            query = "DELETE FROM expenses WHERE id = ?"
            db.execute(query, (expense_id,))
            db.commit()
            return True
        except Exception as e:
            print(f"خطأ في حذف المصروف: {str(e)}")
            return False

    @staticmethod
    def get_expense_categories():
        """الحصول على فئات المصروفات من الجدول المخصص"""
        try:
            # التأكد من وجود الجدول أولاً
            ExpensesModel.ensure_categories_table_exists()

            # جلب جميع الفئات من جدول فئات المصروفات مرتبة حسب عدد الاستخدام والاسم
            query = """
                SELECT name FROM expense_categories
                ORDER BY usage_count DESC, name ASC
            """
            categories = db.fetch_all(query)

            if categories:
                category_list = [category['name'] if isinstance(category, dict) else category[0] for category in categories]
                return category_list
            else:
                # إذا لم توجد فئات، لا نضيف فئات افتراضية
                # نترك الجدول فارغاً ليضيف المستخدم فئاته الخاصة
                return []

        except Exception as e:
            print(f"خطأ في جلب فئات المصروفات: {str(e)}")
            # في حالة حدوث خطأ، إرجاع قائمة فارغة
            return []

    @staticmethod
    def save_expense_category(category):
        """حفظ فئة مصروف جديدة في جدول فئات المصروفات"""
        try:
            if not category.strip():
                return False

            # التأكد من وجود الجدول أولاً
            ExpensesModel.ensure_categories_table_exists()

            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # التحقق من وجود الفئة في الجدول
            check_query = "SELECT id, usage_count FROM expense_categories WHERE name = ?"
            existing_category = db.fetch_one(check_query, (category,))

            if existing_category:
                # الفئة موجودة، تحديث عدد الاستخدام
                category_id = existing_category['id'] if isinstance(existing_category, dict) else existing_category[0]
                current_usage = existing_category['usage_count'] if isinstance(existing_category, dict) else existing_category[1]
                new_usage_count = (current_usage or 0) + 1

                update_query = """
                    UPDATE expense_categories
                    SET usage_count = ?, updated_at = ?
                    WHERE id = ?
                """
                success = db.execute(update_query, (new_usage_count, current_time, category_id))
                if success:
                    db.commit()
                    return True
                else:
                    return False
            else:
                # الفئة غير موجودة، إضافتها كفئة جديدة
                insert_query = """
                    INSERT INTO expense_categories (name, is_default, usage_count, created_at, updated_at)
                    VALUES (?, 0, 1, ?, ?)
                """
                success = db.execute(insert_query, (category, current_time, current_time))
                if success:
                    db.commit()
                    return True
                else:
                    return False

        except Exception as e:
            print(f"خطأ في حفظ فئة المصروف: {str(e)}")
            return False

    @staticmethod
    def ensure_categories_table_exists():
        """التأكد من وجود جدول فئات المصروفات"""
        try:
            # التحقق من وجود الجدول
            check_table_query = """
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='expense_categories'
            """
            table_exists = db.fetch_one(check_table_query)

            if not table_exists:
                success = ExpensesModel.create_categories_table()
                # لا نضيف فئات افتراضية - يُترك الجدول فارغاً للمستخدم
                return success
            else:
                return True
        except Exception as e:
            print(f"خطأ في التأكد من وجود جدول فئات المصروفات: {str(e)}")
            return False

    @staticmethod
    def delete_expense_category(category_name):
        """حذف فئة مصروف من الجدول (فقط الفئات غير الافتراضية)"""
        try:
            # التحقق من أن الفئة ليست افتراضية
            check_query = "SELECT id, is_default FROM expense_categories WHERE name = ?"
            category = db.fetch_one(check_query, (category_name,))

            if not category:
                return False, "الفئة غير موجودة"

            is_default = category['is_default'] if isinstance(category, dict) else category[1]
            if is_default:
                return False, "لا يمكن حذف الفئات الافتراضية"

            # حذف الفئة
            delete_query = "DELETE FROM expense_categories WHERE name = ? AND is_default = 0"
            success = db.execute(delete_query, (category_name,))

            if success:
                db.commit()
                return True, "تم حذف الفئة بنجاح"
            else:
                return False, "فشل في حذف الفئة"

        except Exception as e:
            print(f"خطأ في حذف فئة المصروف: {str(e)}")
            return False, f"خطأ في حذف الفئة: {str(e)}"

    @staticmethod
    def get_category_usage_stats():
        """الحصول على إحصائيات استخدام الفئات"""
        try:
            query = """
                SELECT name, usage_count, is_default
                FROM expense_categories
                ORDER BY usage_count DESC, name ASC
            """
            return db.fetch_all(query)
        except Exception as e:
            print(f"خطأ في جلب إحصائيات الفئات: {str(e)}")
            return []

    @staticmethod
    def create_categories_table():
        """إنشاء جدول فئات المصروفات"""
        try:
            create_categories_table_query = """
                CREATE TABLE expense_categories (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    is_default INTEGER DEFAULT 0,
                    usage_count INTEGER DEFAULT 0,
                    created_at TEXT,
                    updated_at TEXT
                )
            """
            success = db.execute(create_categories_table_query)
            if success:
                db.commit()
                return True
            else:
                return False
        except Exception as e:
            print(f"خطأ في إنشاء جدول فئات المصروفات: {str(e)}")
            return False

    @staticmethod
    def remove_default_categories():
        """إزالة جميع الفئات الافتراضية من قاعدة البيانات"""
        try:
            # التأكد من وجود الجدول أولاً
            ExpensesModel.ensure_categories_table_exists()
            
            # طريقة 1: حذف جميع الفئات التي is_default = 1
            delete_query_by_flag = "DELETE FROM expense_categories WHERE is_default = 1"
            success1 = db.execute(delete_query_by_flag)
            
            # طريقة 2: حذف الفئات الافتراضية بالاسم (في حالة وجود فئات مع is_default = 0 ولكن بأسماء افتراضية)
            default_category_names = [
                "إيجار", "كهرباء", "مياه", "غاز", "هاتف", "إنترنت",
                "مواصلات", "وقود", "صيانة", "تنظيف", "أدوات مكتبية",
                "رواتب", "مكافآت", "تأمينات", "ضرائب", "رسوم حكومية",
                "دعاية وإعلان", "استشارات", "تدريب", "مؤتمرات",
                "مواد خام", "تعبئة وتغليف", "شحن وتوصيل", "مصاريف بنكية",
                "أخرى"
            ]
            
            success2 = True
            for category_name in default_category_names:
                delete_query_by_name = "DELETE FROM expense_categories WHERE name = ?"
                result = db.execute(delete_query_by_name, (category_name,))
                if not result:
                    success2 = False
            
            if success1 and success2:
                db.commit()
                return True
            else:
                return False
                
        except Exception as e:
            print(f"خطأ في حذف الفئات الافتراضية: {str(e)}")
            return False

    @staticmethod
    def get_categories_count():
        """الحصول على عدد العئات الموجودة في قاعدة البيانات"""
        try:
            # التأكد من وجود الجدول أولاً
            ExpensesModel.ensure_categories_table_exists()
            
            # عدد الفئات الافتراضية
            default_count_query = "SELECT COUNT(*) FROM expense_categories WHERE is_default = 1"
            default_count = db.fetch_one(default_count_query)
            
            # عدد الفئات المعرفة من قبل المستخدم
            user_count_query = "SELECT COUNT(*) FROM expense_categories WHERE is_default = 0"
            user_count = db.fetch_one(user_count_query)
            
            default_num = default_count[0] if default_count else 0
            user_num = user_count[0] if user_count else 0
            
            return {
                'default_categories': default_num,
                'user_categories': user_num,
                'total_categories': default_num + user_num
            }
            
        except Exception as e:
            print(f"خطأ في جلب عدد الفئات: {str(e)}")
            return {'default_categories': 0, 'user_categories': 0, 'total_categories': 0}

    @staticmethod
    def get_all_categories_with_status():
        """الحصول على جميع الفئات مع حالتها وإحصائياتها"""
        try:
            # التأكد من وجود الجدول أولاً
            ExpensesModel.ensure_categories_table_exists()
            
            query = """
                SELECT name, is_default, usage_count, created_at, updated_at
                FROM expense_categories
                ORDER BY is_default DESC, usage_count DESC, name ASC
            """
            categories = db.fetch_all(query)
            
            result = []
            if categories:
                for category in categories:
                    if isinstance(category, dict):
                        result.append(category)
                    else:
                        # إذا كانت النتيجة tuple
                        result.append({
                            'name': category[0],
                            'is_default': category[1],
                            'usage_count': category[2],
                            'created_at': category[3],
                            'updated_at': category[4]
                        })
            
            return result
            
        except Exception as e:
            print(f"خطأ في جلب قائمة الفئات: {str(e)}")
            return []

class ExpensesView(QWidget):
    """واجهة المستخدم لصفحة المصروفات"""

    def __init__(self):
        super().__init__()

        # التحقق من وجود جدول المصروفات في قاعدة البيانات
        self.ensure_expenses_table_exists()
        
        # إزالة الفئات الافتراضية من قاعدة البيانات
        self.cleanup_default_categories()

        # إعداد التخطيط الرئيسي
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(25, 15, 25, 25)
        main_layout.setSpacing(5)  # Reducir el espaciado global de 10 a 5

        # إضافة عنوان الصفحة
        header_layout = QHBoxLayout()

        page_title = QLabel("المصروفات")
        page_title.setObjectName("page_title")
        page_title.setFont(QFont("Arial", 18, QFont.Bold))
        header_layout.addWidget(page_title)

        # إضافة زر إضافة مصروف جديد
        self.add_expense_btn = QPushButton("إضافة مصروف جديد")
        self.add_expense_btn.setObjectName("action_button")
        self.add_expense_btn.setMinimumHeight(40)
        self.add_expense_btn.setCursor(Qt.PointingHandCursor)
        self.add_expense_btn.clicked.connect(self.add_new_expense)
        header_layout.addStretch()
        header_layout.addWidget(self.add_expense_btn)

        main_layout.addLayout(header_layout)

        # إضافة فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("content_separator")
        main_layout.addWidget(separator)

        # عنوان قسم التصفية
        filter_title = QLabel("خيارات التصفية")
        filter_title.setObjectName("filter_title")
        filter_title.setFont(QFont("Arial", 12, QFont.Bold))
        main_layout.addWidget(filter_title)
        main_layout.addSpacing(0)

        # إطار الفلتر
        filter_frame = QFrame()
        filter_frame.setObjectName("filter_frame")
        filter_frame.setFrameShape(QFrame.StyledPanel)
        filter_frame.setFrameShadow(QFrame.Raised)

        filter_layout = QVBoxLayout(filter_frame)
        filter_layout.setContentsMargins(10, 15, 10, 15)  # هوامش متساوية فوق وأسفل
        filter_layout.setSpacing(0)

        # تخطيط أفقي لتوسيط عناصر الفلتر
        center_layout = QHBoxLayout()
        center_layout.setSpacing(0)
        center_layout.setContentsMargins(0, 0, 0, 0)

        # 1. إضافة كومبوبوكس لليوم
        self.day_combo = RTLComboBox()
        self.day_combo.setObjectName("combo_box")
        self.day_combo.setFixedHeight(26)  # Reducir altura
        self.day_combo.setFixedWidth(50)

        # إضافة الأيام من 1 إلى 31
        self.day_combo.addItem("الكل")
        for day in range(1, 32):
            self.day_combo.addItem(str(day))

        # تعيين "الكل" كافتراضي لعرض جميع مصروفات الشهر
        self.day_combo.setCurrentIndex(0)  # "الكل"

        # 2. إضافة كومبوبوكس للشهر
        self.month_combo = RTLComboBox()
        self.month_combo.setObjectName("combo_box")
        self.month_combo.setFixedHeight(26)  # Reducir altura
        self.month_combo.setFixedWidth(90)

        # إضافة الأشهر العربية
        arabic_months = [
            "يناير", "فبراير", "مارس", "أبريل", "مايو", "يونيو",
            "يوليو", "أغسطس", "سبتمبر", "أكتوبر", "نوفمبر", "ديسمبر"
        ]
        self.month_combo.addItems(arabic_months)

        # تعيين الشهر الحالي
        current_month = QDate.currentDate().month()
        self.month_combo.setCurrentIndex(current_month - 1)

        # 3. إضافة كومبوبوكس للسنة
        self.year_combo = RTLComboBox()
        self.year_combo.setObjectName("combo_box")
        self.year_combo.setFixedHeight(26)  # Reducir altura
        self.year_combo.setFixedWidth(70)

        # إضافة السنوات (من السنة الحالية إلى خمس سنوات قبلها)
        current_year = QDate.currentDate().year()
        for year in range(current_year, current_year - 6, -1):
            self.year_combo.addItem(str(year))

        # إضافة مساحة مرنة لتوسيط العناصر من اليسار
        center_layout.addStretch(1)

        # تخطيط أفقي لعناصر التاريخ
        date_layout = QHBoxLayout()
        date_layout.setSpacing(8)  # مسافة مناسبة بين العناصر
        date_layout.setContentsMargins(0, 0, 0, 0)

        date_layout.addWidget(self.day_combo)
        date_layout.addWidget(self.month_combo)
        date_layout.addWidget(self.year_combo)
        date_layout.addSpacing(15)  # مسافة قبل الزر

        # إنشاء زر توليد التقرير
        self.apply_filter_btn = QPushButton("🔍  عرض")
        self.apply_filter_btn.setFixedSize(100, 30)
        self.apply_filter_btn.setObjectName("filter_button")
        font = QFont("Arial", 10)
        font.setBold(True)
        self.apply_filter_btn.setFont(font)
        self.apply_filter_btn.setCursor(Qt.PointingHandCursor)
        self.apply_filter_btn.clicked.connect(self.filter_expenses)

        date_layout.addWidget(self.apply_filter_btn)

        # إضافة تخطيط التاريخ إلى التخطيط المركزي
        center_layout.addLayout(date_layout)

        # إضافة مساحة مرنة لتوسيط العناصر من اليمين
        center_layout.addStretch(1)

        # إضافة التخطيط المركزي إلى تخطيط الفلتر
        filter_layout.addLayout(center_layout)

        main_layout.addWidget(filter_frame)

        # إنشاء جدول المصروفات
        self.expenses_table = QTableWidget()
        self.expenses_table.setObjectName("data_table")
        self.expenses_table.setColumnCount(5)
        self.expenses_table.setHorizontalHeaderLabels(["التاريخ", "الفئة", "المبلغ", "الوصف", "تاريخ الإضافة"])

        # ضبط خصائص الجدول
        self.expenses_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.expenses_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.expenses_table.setAlternatingRowColors(True)
        self.expenses_table.verticalHeader().setVisible(False)

        # ضبط عرض الأعمدة
        header = self.expenses_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # التاريخ
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)  # الفئة
        header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # المبلغ
        header.setSectionResizeMode(3, QHeaderView.Stretch)          # الوصف
        header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # تاريخ الإضافة

        # تفعيل قائمة السياق
        self.expenses_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.expenses_table.customContextMenuRequested.connect(self.show_context_menu)

        main_layout.addWidget(self.expenses_table)

        # إضافة ملخص المصروفات
        summary_layout = QHBoxLayout()

        self.total_expenses_label = QLabel("إجمالي المصروفات: 0.00 ج.م")
        self.total_expenses_label.setObjectName("stats_label")
        summary_layout.addWidget(self.total_expenses_label)

        self.expenses_count_label = QLabel("عدد المصروفات: 0")
        self.expenses_count_label.setObjectName("stats_label")
        summary_layout.addWidget(self.expenses_count_label)

        summary_layout.addStretch()

        main_layout.addLayout(summary_layout)

        # تحديث الجدول
        self.filter_expenses()

        # تطبيق الأنماط
        self.apply_styles()
        apply_rtl_to_all_widgets(self)

    def ensure_expenses_table_exists(self):
        """التأكد من وجود جدول المصروفات وجدول فئات المصروفات في قاعدة البيانات"""
        try:
            # التحقق من وجود جدول المصروفات
            query = """
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='expenses'
            """
            result = db.fetch_one(query)

            # إذا لم يوجد الجدول، يتم إنشاؤه
            if not result:
                create_table_query = """
                    CREATE TABLE expenses (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        date TEXT NOT NULL,
                        category TEXT NOT NULL,
                        amount REAL NOT NULL,
                        description TEXT,
                        created_at TEXT,
                        updated_at TEXT
                    )
                """
                db.execute(create_table_query)
                db.commit()
                print("تم إنشاء جدول المصروفات بنجاح")

            # التحقق من وجود جدول فئات المصروفات
            query = """
                SELECT name FROM sqlite_master
                WHERE type='table' AND name='expense_categories'
            """
            result = db.fetch_one(query)

            # إذا لم يوجد الجدول، يتم إنشاؤه
            if not result:
                create_categories_table_query = """
                    CREATE TABLE expense_categories (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name TEXT NOT NULL UNIQUE,
                        is_default INTEGER DEFAULT 0,
                        usage_count INTEGER DEFAULT 0,
                        created_at TEXT,
                        updated_at TEXT
                    )
                """
                db.execute(create_categories_table_query)
                db.commit()
                print("تم إنشاء جدول فئات المصروفات بنجاح")

                # الجدول منشأ بنجاح ولا نحتاج إضافة فئات افتراضية
                # self.insert_default_categories()

        except Exception as e:
            print(f"خطأ في التحقق من جداول المصروفات: {str(e)}")

    def insert_default_categories(self):
        """تم تعطيل إدراج الفئات الافتراضية - يترك الجدول فارغاً للمستخدم"""
        # تم تعطيل هذه الدالة لعدم إضافة فئات افتراضية
        pass

    def cleanup_default_categories(self):
        """تنظيف قاعدة البيانات من الفئات الافتراضية"""
        try:
            # جلب عدد الفئات للتحقق
            counts = ExpensesModel.get_categories_count()
            
            if counts['default_categories'] > 0:
                # حذف الفئات الافتراضية
                ExpensesModel.remove_default_categories()
                
        except Exception as e:
            # إزالة رسائل التشخيص لتحسين الأداء
            pass

    def filter_expenses(self):
        """تطبيق فلترة على المصروفات وتحديث الجدول"""
        try:
            # الحصول على قيم الفلتر
            selected_day = self.day_combo.currentText()
            selected_month = self.month_combo.currentIndex() + 1  # تحويل فهرس الشهر إلى رقم (1-12)
            selected_year = self.year_combo.currentText()

            # تحديد تاريخ البداية والنهاية للفلتر
            start_date = None
            end_date = None

            # إذا تم اختيار "الكل" كيوم، نعرض جميع أيام الشهر
            if selected_day == "الكل":
                # تاريخ البداية: اليوم الأول من الشهر
                start_date = f"{selected_year}-{selected_month:02d}-01"

                # تاريخ النهاية: آخر يوم في الشهر
                if selected_month == 12:
                    next_month_year = int(selected_year) + 1
                    next_month = 1
                else:
                    next_month_year = int(selected_year)
                    next_month = selected_month + 1

                # الوقت 00:00:00 من الشهر التالي ناقص ثانية واحدة = آخر يوم في الشهر الحالي
                last_day_date = QDate(next_month_year, next_month, 1).addDays(-1)
                end_date = f"{selected_year}-{selected_month:02d}-{last_day_date.day():02d}"
            else:
                # تحديد تاريخ محدد
                selected_day_int = int(selected_day)
                start_date = f"{selected_year}-{selected_month:02d}-{selected_day_int:02d}"
                end_date = start_date

            # جلب المصروفات مع تطبيق الفلتر
            expenses = ExpensesModel.get_all_expenses(start_date, end_date)

            # تحديث الجدول بالمصروفات المفلترة
            self.populate_expenses_table(expenses)

            # تحديث ملخص المصروفات
            self.update_summary(expenses)
        except Exception as e:
            QMessageBox.warning(self, "خطأ في تصفية المصروفات", f"حدث خطأ أثناء تصفية المصروفات: {str(e)}")

    def reset_filter(self):
        """إعادة ضبط الفلتر إلى القيم الافتراضية"""
        # إعادة ضبط التواريخ
        self.day_combo.setCurrentIndex(0)  # "الكل"

        # تعيين الشهر الحالي
        current_month = QDate.currentDate().month()
        self.month_combo.setCurrentIndex(current_month - 1)

        # تعيين السنة الحالية
        self.year_combo.setCurrentIndex(0)  # أحدث سنة

        # تطبيق الفلتر بالقيم الجديدة
        self.filter_expenses()

    def populate_expenses_table(self, expenses):
        """ملء جدول المصروفات بالبيانات"""
        # تفريغ الجدول
        self.expenses_table.setRowCount(0)

        # إذا لم تكن هناك مصروفات، نعيد مبكراً
        if not expenses:
            return

        # ملء الجدول بالمصروفات
        for row, expense in enumerate(expenses):
            self.expenses_table.insertRow(row)

            # التاريخ
            date_item = QTableWidgetItem(expense['date'])
            date_item.setToolTip(expense['date'])
            date_item.setTextAlignment(Qt.AlignCenter)
            self.expenses_table.setItem(row, 0, date_item)

            # الفئة
            category_item = QTableWidgetItem(expense['category'])
            category_item.setToolTip(expense['category'])
            category_item.setTextAlignment(Qt.AlignCenter)
            self.expenses_table.setItem(row, 1, category_item)

            # المبلغ
            amount_item = QTableWidgetItem(f"{expense['amount']:.2f} {CURRENCY_SYMBOL}")
            amount_item.setToolTip(f"{expense['amount']:.2f} {CURRENCY_SYMBOL}")
            amount_item.setTextAlignment(Qt.AlignCenter)
            self.expenses_table.setItem(row, 2, amount_item)

            # الوصف
            description_item = QTableWidgetItem(expense['description'] or "-")
            description_item.setToolTip(expense['description'] or "-")
            self.expenses_table.setItem(row, 3, description_item)

            # تاريخ الإضافة
            created_at_item = QTableWidgetItem(expense['created_at'])
            created_at_item.setToolTip(expense['created_at'])
            created_at_item.setTextAlignment(Qt.AlignCenter)
            self.expenses_table.setItem(row, 4, created_at_item)

            # تخزين معرف المصروف في البيانات الإضافية للصف
            date_item.setData(Qt.UserRole, expense['id'])

    def update_summary(self, expenses):
        """تحديث ملخص المصروفات"""
        # حساب إجمالي المصروفات
        total_amount = sum(expense['amount'] for expense in expenses)

        # تحديث الملصقات
        self.total_expenses_label.setText(f"إجمالي المصروفات: {total_amount:.2f} {CURRENCY_SYMBOL}")
        self.expenses_count_label.setText(f"عدد المصروفات: {len(expenses)}")

    def show_context_menu(self, position):
        """عرض قائمة السياق عند النقر بزر الماوس الأيمن على المصروفات"""
        # التحقق من وجود صف محدد
        selected_indexes = self.expenses_table.selectedIndexes()
        if not selected_indexes:
            return

        # الحصول على معرف المصروف من الصف المحدد
        row = selected_indexes[0].row()
        expense_id = self.expenses_table.item(row, 0).data(Qt.UserRole)
        expense_category = self.expenses_table.item(row, 1).text()

        # إنشاء قائمة السياق
        context_menu = self.create_context_menu(expense_id, expense_category)

        # عرض القائمة في موقع النقر
        context_menu.exec_(self.expenses_table.viewport().mapToGlobal(position))

    def create_context_menu(self, expense_id, expense_category):
        """إنشاء قائمة السياق للمصروفات"""
        context_menu = QMenu(self)
        context_menu.setLayoutDirection(Qt.RightToLeft)

        # إضافة عنوان القائمة
        title_action = QAction(f"المصروف: {expense_category}", self)
        title_action.setEnabled(False)
        title_font = title_action.font()
        title_font.setBold(True)
        title_action.setFont(title_font)
        context_menu.addAction(title_action)

        # إضافة فاصل
        context_menu.addSeparator()

        # التحقق من صلاحية المستخدم
        main_window = None
        parent = self.parent()
        user_id = None

        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()

        if main_window and main_window.current_user:
            user_id = main_window.current_user.get('id')

        # استيراد وحدة التحكم بالمستخدمين
        from controllers.user_controller import UserController

        # إضافة إجراء الحذف - يتطلب صلاحية
        if user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "حذف مصروف"):
            delete_action = QAction("🗑️ حذف المصروف", self)
            delete_action.triggered.connect(lambda: self.delete_expense(expense_id))
            context_menu.addAction(delete_action)

        return context_menu

    def delete_expense(self, expense_id):
        """حذف مصروف من قاعدة البيانات"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية حذف مصروف
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "حذف مصروف", show_message=True, parent_widget=self):
                    return

            # تأكيد الحذف
            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                "هل أنت متأكد من رغبتك في حذف هذا المصروف؟",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # محاولة حذف المصروف
                success = ExpensesModel.delete_expense(expense_id)

                if success:
                    QMessageBox.information(self, "تم الحذف", "تم حذف المصروف بنجاح")
                    # تحديث الجدول
                    self.filter_expenses()
                else:
                    QMessageBox.warning(self, "خطأ في الحذف", "حدث خطأ أثناء محاولة حذف المصروف")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء محاولة حذف مصروف: {str(e)}")

    def add_new_expense(self):
        """فتح نافذة لإضافة مصروف جديد"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية إضافة مصروف
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "إضافة مصروف", show_message=True, parent_widget=self):
                    return

            dialog = ExpenseDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                # الحصول على بيانات المصروف الجديد
                expense_data = dialog.get_expense_data()

                # إضافة المصروف إلى قاعدة البيانات
                success = ExpensesModel.add_expense(expense_data)

                if success:
                    QMessageBox.information(self, "تمت الإضافة", "تم إضافة المصروف بنجاح")
                    # تحديث الجدول
                    self.filter_expenses()
                else:
                    QMessageBox.warning(self, "خطأ في الإضافة", "حدث خطأ أثناء محاولة إضافة المصروف")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء محاولة إضافة مصروف: {str(e)}")

    def refresh_page(self):
        """تحديث البيانات عند الانتقال إلى الصفحة"""
        self.filter_expenses()

        # تحديث حالة تفعيل الأزرار بناءً على صلاحيات المستخدم
        self.update_buttons_state()

    def update_buttons_state(self):
        """تحديث حالة تفعيل الأزرار بناءً على صلاحيات المستخدم"""
        # التحقق من وجود مستخدم حالي
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()

        if not main_window or not main_window.current_user:
            return

        user_id = main_window.current_user.get('id')
        username = main_window.current_user.get('username')

        # المستخدم admin له جميع الصلاحيات
        if username == 'admin':
            # تفعيل زر إضافة مصروف جديد
            self.add_expense_btn.setEnabled(True)
            self.add_expense_btn.setStyleSheet("")
            return

        # استيراد وحدة التحكم بالمستخدمين
        from controllers.user_controller import UserController

        # تحديث حالة زر إضافة مصروف جديد
        has_add_expense_permission = UserController.check_permission(user_id, "إضافة مصروف")
        self.add_expense_btn.setEnabled(has_add_expense_permission)
        if not has_add_expense_permission:
            self.add_expense_btn.setStyleSheet("""
                background-color: #f0f0f0;
                color: #999999;
                border: 1px solid #cccccc;
            """)
        else:
            self.add_expense_btn.setStyleSheet("")

    def apply_styles(self):
        """تطبيق الأنماط على عناصر الصفحة"""
        self.setStyleSheet("""
            QWidget {
                background-color: #f9f9f9;
                font-family: 'Readex Pro', 'Tahoma', 'Arial';
            }

            #page_title {
                color: #333;
                font-size: 24px;
                padding: 10px;
                font-weight: bold;
            }

            #filter_title {
                color: #444;
                font-size: 14px;
                padding: 0px;
                font-weight: bold;
                margin-bottom: 2px;
            }

            #filter_frame {
                background-color: #f0f7ff;
                border: 1px solid #cce5ff;
                border-radius: 6px;
                padding: 0px 10px 6px 10px;
                margin-bottom: 0px;
            }

            #field_label {
                color: #555;
                font-weight: bold;
                padding-right: 5px;
            }

            #stats_label {
                color: #555;
                font-weight: bold;
                margin-right: 15px;
            }

            QDateEdit, QComboBox {
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 0px 5px;
                background-color: white;
                min-height: 28px;
                text-align: center;
            }

            /* إخفاء زر القائمة المنسدلة في الكومبوبوكس */
            QComboBox::drop-down {
                width: 0px;
                border: none;
            }

            /* إزالة السهم من الكومبوبوكس */
            QComboBox::down-arrow {
                width: 0px;
                height: 0px;
                border: none;
            }

            /* جعل النص في وسط الكومبوبوكس */
            QComboBox {
                text-align: center;
                padding-left: 0px;
                padding-right: 0px;
            }

            #action_button {
                background-color: #4285f4;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 13px;
            }

            #action_button:hover {
                background-color: #3367d6;
            }

            #secondary_button {
                background-color: #f1f3f4;
                color: #202124;
                border: 1px solid #dadce0;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 13px;
            }

            #secondary_button:hover {
                background-color: #e8eaed;
            }

            #reset_button {
                background-color: #fef2f2;
                color: #b91c1c;
                border: 1px solid #fee2e2;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 13px;
            }

            #reset_button:hover {
                background-color: #fee2e2;
            }

            #filter_button {
                background-color: #4285f4; /* اللون الأزرق ليتماشي مع باقي أزرار البرنامج */
                color: white;
                border: none;
                border-radius: 4px; /* Reducir el radio del borde para que coincida con comboboxes */
                padding: 0px 16px; /* Mantener padding horizontal */
                margin-top: 3px; /* Añadir un pequeño margen superior */
                font-size: 12px; /* Ajustar tamaño de fuente */
                font-weight: bold;
                box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1); /* Reducir sombra */
            }

            #filter_button:hover {
                background-color: #3367d6; /* أزرق أكثر قتامة عند تمرير الماوس */
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
            }

            #filter_button:pressed {
                background-color: #2a56c6; /* أزرق أكثر قتامة عند الضغط */
                box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
            }

            #data_table {
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                background-color: white;
                font-size: 13px;
                gridline-color: #e2e8f0;
            }

            #data_table QHeaderView::section {
                background-color: #f1f5f9;
                color: #475569;
                font-weight: bold;
                border: none;
                padding: 10px;
                border-bottom: 1px solid #cbd5e1;
                border-right: 1px solid #e2e8f0;
            }

            #data_table::item {
                padding: 8px;
                border-bottom: 1px solid #f1f5f9;
            }

            #data_table::item:selected {
                background-color: rgba(59, 130, 246, 0.15);
                color: #000000;
            }
        """)

class ExpenseDialog(QDialog):
    """نافذة حوار لإضافة مصروف جديد"""

    def __init__(self, parent=None):
        super().__init__(parent)

        # تعيين خصائص النافذة
        self.setWindowTitle("إضافة مصروف جديد")
        self.setMinimumWidth(400)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setModal(True)

        # إعداد التخطيط
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # عنوان النافذة
        title_label = QLabel("إضافة مصروف جديد")
        title_label.setObjectName("dialog_title")
        title_label.setAlignment(Qt.AlignCenter)
        layout.addWidget(title_label)

        # فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("separator")
        layout.addWidget(separator)

        # نموذج لإدخال بيانات المصروف
        form_layout = QFormLayout()
        form_layout.setSpacing(10)
        form_layout.setLabelAlignment(Qt.AlignRight)
        form_layout.setFormAlignment(Qt.AlignRight)

        # حقل التاريخ
        self.date_edit = QDateEdit()
        self.date_edit.setObjectName("date_edit")
        self.date_edit.setCalendarPopup(True)
        self.date_edit.setDate(QDate.currentDate())
        self.date_edit.setMinimumHeight(35)
        form_layout.addRow("التاريخ:", self.date_edit)

        # حقل الفئة كمربع نص مع الاقتراح التلقائي
        self.category_edit = QLineEdit()
        self.category_edit.setObjectName("category_edit")
        self.category_edit.setPlaceholderText("أدخل فئة المصروف")
        self.category_edit.setMinimumHeight(35)
        
        # إعداد الاقتراح التلقائي للفئات
        self.setup_category_autocomplete()
        
        form_layout.addRow("الفئة:", self.category_edit)

        # حقل المبلغ
        self.amount_spinbox = QDoubleSpinBox()
        self.amount_spinbox.setObjectName("amount_spinbox")
        self.amount_spinbox.setMinimum(0.01)
        self.amount_spinbox.setMaximum(1000000.00)
        self.amount_spinbox.setValue(0.00)
        self.amount_spinbox.setSingleStep(1.00)
        self.amount_spinbox.setDecimals(2)
        self.amount_spinbox.setSuffix(f" {CURRENCY_SYMBOL}")
        self.amount_spinbox.setButtonSymbols(QDoubleSpinBox.NoButtons)  # إزالة أسهم زيادة ونقصان
        self.amount_spinbox.setMinimumHeight(35)
        form_layout.addRow("المبلغ:", self.amount_spinbox)

        # حقل الوصف
        self.description_edit = QTextEdit()
        self.description_edit.setObjectName("description_edit")
        self.description_edit.setPlaceholderText("أدخل وصفاً للمصروف (اختياري)")
        self.description_edit.setMinimumHeight(80)
        form_layout.addRow("الوصف:", self.description_edit)

        layout.addLayout(form_layout)

        # أزرار الإجراءات
        buttons_layout = QHBoxLayout()
        buttons_layout.setSpacing(10)

        # زر الإلغاء
        self.cancel_btn = QPushButton("إلغاء")
        self.cancel_btn.setObjectName("cancel_button")
        self.cancel_btn.setMinimumHeight(40)
        self.cancel_btn.clicked.connect(self.reject)

        # زر الحفظ
        self.save_btn = QPushButton("حفظ")
        self.save_btn.setObjectName("save_button")
        self.save_btn.setMinimumHeight(40)
        self.save_btn.clicked.connect(self.accept)

        buttons_layout.addWidget(self.cancel_btn)
        buttons_layout.addWidget(self.save_btn)

        layout.addLayout(buttons_layout)

        # تطبيق الأنماط
        self.apply_styles()

        # تركيز على حقل الفئة لتسهيل الكتابة
        self.category_edit.setFocus()

    def setup_category_autocomplete(self):
        """إعداد الاقتراح التلقائي لفئات المصروفات"""
        try:
            # جلب الفئات الموجودة من قاعدة البيانات
            categories = ExpensesModel.get_expense_categories()
            
            # إنشاء مكمل تلقائي
            self.completer = QCompleter(categories)
            self.completer.setCaseSensitivity(Qt.CaseInsensitive)
            self.completer.setFilterMode(Qt.MatchContains)
            self.completer.setCompletionMode(QCompleter.PopupCompletion)
            self.completer.setMaxVisibleItems(10)
            
            # ربط المكمل بحقل الفئة
            self.category_edit.setCompleter(self.completer)
        except Exception as e:
            print(f"خطأ في إعداد الاقتراح التلقائي: {str(e)}")

    def refresh_autocomplete_suggestions(self):
        """تحديث قائمة اقتراحات الفئات"""
        try:
            # جلب الفئات المحدثة من قاعدة البيانات
            categories = ExpensesModel.get_expense_categories()
            
            # تحديث نموذج المكمل
            if hasattr(self, 'completer') and self.completer:
                model = QStringListModel(categories)
                self.completer.setModel(model)
        except Exception as e:
            print(f"خطأ في تحديث اقتراحات الفئات: {str(e)}")

    def accept(self):
        """تنفيذ عملية القبول عند النقر على زر الحفظ"""
        # التحقق من صحة البيانات
        category = self.category_edit.text().strip()
        amount = self.amount_spinbox.value()

        if not category:
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال فئة للمصروف")
            self.category_edit.setFocus()
            return

        if amount <= 0:
            QMessageBox.warning(self, "تحذير", "الرجاء إدخال مبلغ صحيح")
            self.amount_spinbox.setFocus()
            return

        # حفظ الفئة في قاعدة البيانات
        ExpensesModel.save_expense_category(category)

        # قبول الحوار
        super().accept()

    def get_expense_data(self):
        """الحصول على بيانات المصروف المدخلة"""
        return {
            'date': self.date_edit.date().toString("yyyy-MM-dd"),
            'category': self.category_edit.text(),
            'amount': self.amount_spinbox.value(),
            'description': self.description_edit.toPlainText()
        }

    def apply_styles(self):
        """تطبيق الأنماط على النافذة"""
        self.setStyleSheet("""
            QDialog {
                background-color: white;
                font-family: 'Readex Pro', 'Tahoma', 'Arial';
            }

            #dialog_title {
                color: #333;
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 10px;
            }

            #separator {
                background-color: #eee;
                max-height: 1px;
            }

            QLabel {
                color: #333;
                font-weight: bold;
            }

            QDateEdit, QLineEdit, QDoubleSpinBox, QTextEdit {
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 5px;
                background-color: #f9f9f9;
                font-size: 14px;
            }

            /* تنسيق حقل الفئة */
            QLineEdit {
                text-align: center;
                padding-left: 5px;
                padding-right: 5px;
            }

            QDateEdit:focus, QLineEdit:focus, QDoubleSpinBox:focus, QTextEdit:focus {
                border-color: #3b82f6;
                background-color: white;
            }

            #save_button {
                background-color: #3b82f6;
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 16px;
                font-weight: bold;
            }

            #save_button:hover {
                background-color: #2563eb;
            }

            #cancel_button {
                background-color: white;
                color: #3b82f6;
                border: 1px solid #3b82f6;
                border-radius: 4px;
                padding: 8px 16px;
            }

            #cancel_button:hover {
                background-color: #f0f7ff;
            }
        """)