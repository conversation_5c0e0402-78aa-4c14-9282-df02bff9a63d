"""
وحدة واجهة العملاء - توفر واجهة لإدارة قائمة العملاء والتعامل معهم
"""

from PyQt5.QtWidgets import (QWidget, QVBoxLayout, QLabel, QPushButton, QHBoxLayout, QFrame,
                             QTableWidget, QTableWidgetItem, QHeaderView, QComboBox, QLineEdit,
                             QGridLayout, QSpacerItem, QSizePolicy, QAbstractItemView, QDialog,
                             QFormLayout, QMessageBox, QDoubleSpinBox, QSpinBox, QMenu, QAction,
                             QTabWidget, QDateEdit, QDialogButtonBox, QScrollArea, QInputDialog, QCheckBox)
from PyQt5.QtCore import Qt, QSize, QDate
from PyQt5.QtGui import QFont, QIcon, QColor, QCursor

# Define RTL helper classes directly
class RTLComboBox(QComboBox):
    """Replacement class for RTLComboBox"""
    pass

def apply_rtl_to_all_widgets(widget):
    """Replacement function for apply_rtl_to_all_widgets"""
    pass

from styles import AppStyles  # استيراد التنسيقات من ملف styles.py
from models.customers import CustomerModel  # استيراد نموذج العملاء
from models.database import db  # استيراد قاعدة البيانات
from utils.date_utils import DateTimeUtils
import datetime
import sqlite3
from utils.custom_widgets import show_information

class CustomersView(QWidget):
    def __init__(self):
        super().__init__()

        # إعداد التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(25, 25, 25, 25)

        # إضافة عنوان الصفحة
        header_layout = QHBoxLayout()
        page_title = QLabel("العملاء")
        page_title.setObjectName("page_title")
        page_title.setFont(QFont("Arial", 20, QFont.Bold))
        header_layout.addWidget(page_title)

        # إضافة زر لإضافة عميل جديد
        self.new_customer_btn = QPushButton("👥  عميل جديد")
        self.new_customer_btn.setFixedSize(180, 40)
        self.new_customer_btn.setObjectName("action_button")
        self.new_customer_btn.setFont(QFont("Arial", 11))
        self.new_customer_btn.setCursor(Qt.PointingHandCursor)
        self.new_customer_btn.clicked.connect(self.add_new_customer)
        self.new_customer_btn.setToolTip("إضافة عميل جديد إلى قاعدة البيانات")
        header_layout.addStretch()
        header_layout.addWidget(self.new_customer_btn)

        layout.addLayout(header_layout)

        # إضافة فاصل
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("content_separator")
        layout.addWidget(separator)
        layout.addSpacing(20)

        # إضافة منطقة البحث والتصفية
        search_layout = QHBoxLayout()

        # مربع البحث
        search_label = QLabel("بحث:")
        search_label.setObjectName("field_label")
        self.search_input = QLineEdit()
        self.search_input.setObjectName("search_input")
        self.search_input.setPlaceholderText("ابحث عن عميل...")
        self.search_input.setClearButtonEnabled(True)
        self.search_input.setMinimumHeight(40)
        self.search_input.textChanged.connect(self.filter_customers)

        # فلتر تاريخ آخر زيارة
        date_label = QLabel("تاريخ آخر زيارة:")
        date_label.setObjectName("field_label")

        self.date_filter = RTLComboBox()
        self.date_filter.setObjectName("combo_box")
        self.date_filter.addItems(["الكل", "آخر أسبوع", "آخر شهر", "آخر 3 أشهر", "آخر سنة"])
        self.date_filter.setFixedHeight(40)
        self.date_filter.setMinimumWidth(120)
        self.date_filter.setLayoutDirection(Qt.RightToLeft)
        self.date_filter.currentIndexChanged.connect(self.filter_customers)

        # إضافة عناصر التصفية إلى التخطيط
        search_layout.addWidget(search_label)
        search_layout.addWidget(self.search_input)
        search_layout.addSpacing(20)
        search_layout.addWidget(date_label)
        search_layout.addWidget(self.date_filter)

        layout.addLayout(search_layout)
        layout.addSpacing(15)

        # إضافة جدول العملاء
        self.customers_table = QTableWidget()
        self.customers_table.setObjectName("customers_table")
        self.customers_table.setColumnCount(7)
        self.customers_table.setHorizontalHeaderLabels(["#", "اسم العميل", "رقم الهاتف", "البريد الإلكتروني", "إجمالي المشتريات", "آخر زيارة", "دين العميل"])
        self.customers_table.horizontalHeader().setSectionResizeMode(QHeaderView.Stretch)
        self.customers_table.horizontalHeader().setSectionResizeMode(0, QHeaderView.ResizeToContents)
        self.customers_table.verticalHeader().setVisible(False)
        self.customers_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.customers_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.customers_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.customers_table.setAlternatingRowColors(False)

        # Establecer un estilo específico para la tabla con un color de fondo uniforme para todas las filas
        self.customers_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
            }
            QTableWidget::item {
                background-color: #f8fafc;  /* Color de fondo más oscuro para todas las filas */
            }
            QTableWidget::item:selected {
                background-color: rgba(59, 130, 246, 0.3);
                color: #0f172a;
            }
        """)

        # إضافة قائمة السياق
        self.customers_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.customers_table.customContextMenuRequested.connect(self.show_context_menu)

        # إضافة النقر المزدوج لعرض تفاصيل العميل
        self.customers_table.doubleClicked.connect(self.on_table_double_clicked)

        # تعديل: تكبير حجم صفوف الجدول لتكون أكثر قابلية للقراءة
        self.customers_table.verticalHeader().setDefaultSectionSize(40)

        layout.addWidget(self.customers_table)

        # إضافة إحصائيات العملاء
        stats_layout = QHBoxLayout()

        # عدد العملاء
        self.total_customers_label = QLabel("إجمالي العملاء: 0")
        self.total_customers_label.setObjectName("stats_label")
        stats_layout.addWidget(self.total_customers_label)

        stats_layout.addStretch()

        # إجمالي المبيعات
        self.total_sales_label = QLabel("إجمالي المبيعات: 0.00 ج.م")
        self.total_sales_label.setObjectName("stats_label")
        stats_layout.addWidget(self.total_sales_label)

        stats_layout.addSpacing(20)

        # إجمالي الديون
        self.total_debt_label = QLabel("إجمالي الديون: 0.00 ج.م")
        self.total_debt_label.setObjectName("stats_label")
        # تلوين إجمالي الديون بالأحمر
        self.total_debt_label.setStyleSheet("color: #e74c3c; font-weight: bold;")
        stats_layout.addWidget(self.total_debt_label)

        layout.addLayout(stats_layout)

        # إضافة تعليمات للمستخدم
        hint_label = QLabel("ملاحظة: يمكنك النقر مرتين على أي عميل لعرض تفاصيله وتعاملاته، أو النقر بزر الماوس الأيمن للوصول إلى قائمة الخيارات")
        hint_label.setObjectName("hint_label")
        layout.addWidget(hint_label)

        # ملء جدول العملاء بالبيانات
        self.populate_customers_table()

        # تطبيق الأنماط
        self.apply_styles()
        # Aplicar configuración RTL a todos los widgets
        apply_rtl_to_all_widgets(self)

    def show_context_menu(self, position):
        """عرض قائمة السياق عند النقر بزر الماوس الأيمن"""
        # التحقق من وجود صف محدد
        selected_indexes = self.customers_table.selectedIndexes()
        if not selected_indexes:
            return

        # الحصول على صف العميل المحدد
        row = selected_indexes[0].row()
        customer_id = self.customers_table.item(row, 0).data(Qt.UserRole)
        customer_name = self.customers_table.item(row, 1).text()

        # إنشاء قائمة السياق
        context_menu = QMenu(self)
        context_menu.setLayoutDirection(Qt.RightToLeft)

        # إضافة عنوان العميل
        title_action = QAction(f"العميل: {customer_name}", self)
        title_action.setEnabled(False)
        title_font = title_action.font()
        title_font.setBold(True)
        title_action.setFont(title_font)
        context_menu.addAction(title_action)

        # إضافة فاصل
        context_menu.addSeparator()

        # الحصول على معرف المستخدم الحالي
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()

        user_id = None
        if main_window and main_window.current_user:
            user_id = main_window.current_user.get('id')

        # استيراد وحدة التحكم بالمستخدمين
        from controllers.user_controller import UserController

        # إضافة إجراءات مع التحقق من الصلاحيات
        # عرض تفاصيل العميل - يتطلب صلاحية
        if user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "عرض تفاصيل العميل"):
            view_action = QAction("👁️  عرض تفاصيل العميل", self)
            view_action.triggered.connect(lambda: self.view_customer_details(customer_id))
            context_menu.addAction(view_action)

        # تعديل بيانات العميل - يتطلب صلاحية
        if user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "تعديل عميل"):
            edit_action = QAction("✏️  تعديل بيانات العميل", self)
            edit_action.triggered.connect(lambda: self.edit_customer(customer_id))
            context_menu.addAction(edit_action)

        # إنشاء فاتورة جديدة - يتطلب صلاحية
        if user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "إضافة عملية بيع"):
            add_invoice_action = QAction("📄  إنشاء فاتورة جديدة", self)
            add_invoice_action.triggered.connect(lambda: self.create_invoice_for_customer(customer_id))
            context_menu.addAction(add_invoice_action)

        # إضافة فاصل
        context_menu.addSeparator()

        # حذف العميل - يتطلب صلاحية
        if user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "حذف عميل"):
            delete_action = QAction("🗑️  حذف العميل", self)
            delete_action.triggered.connect(lambda: self.delete_customer(customer_id))
            context_menu.addAction(delete_action)

        # عرض القائمة في موقع النقر
        context_menu.exec_(self.customers_table.mapToGlobal(position))

    def on_table_double_clicked(self, index):
        """معالجة حدث النقر المزدوج على صف في الجدول"""
        # التحقق من صلاحية المستخدم
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()

        if main_window and main_window.current_user:
            user_id = main_window.current_user.get('id')
            # التحقق من صلاحية عرض تفاصيل العميل
            from controllers.user_controller import UserController
            if not UserController.check_permission(user_id, "عرض تفاصيل العميل", show_message=True, parent_widget=self):
                return

        row = index.row()
        customer_id = self.customers_table.item(row, 0).data(Qt.UserRole)
        self.view_customer_details(customer_id)

    def populate_customers_table(self):
        """ملء جدول العملاء بالبيانات من قاعدة البيانات"""
        # تنظيف الجدول أولاً
        self.customers_table.clearContents()
        self.customers_table.setRowCount(0)

        # متغيرات للإحصائيات
        total_customers = 0
        total_sales = 0
        total_debt = 0

        # الحصول على بيانات العملاء من قاعدة البيانات
        customers_data = CustomerModel.get_all_customers()

        # إضافة بيانات العملاء للجدول
        for customer in customers_data:
            row = self.customers_table.rowCount()
            self.customers_table.insertRow(row)

            # رقم المسلسل
            serial_item = QTableWidgetItem(str(row + 1))
            serial_item.setTextAlignment(Qt.AlignCenter)
            # تخزين معرف العميل في البيانات المخفية
            serial_item.setData(Qt.UserRole, customer["id"])
            self.customers_table.setItem(row, 0, serial_item)

            # اسم العميل
            name_item = QTableWidgetItem(customer["name"])
            name_item.setTextAlignment(Qt.AlignCenter)
            name_item.setToolTip(customer["name"])  # إضافة tooltip
            self.customers_table.setItem(row, 1, name_item)

            # رقم الهاتف
            phone_item = QTableWidgetItem(customer["phone"] if customer["phone"] else "")
            phone_item.setTextAlignment(Qt.AlignCenter)
            phone_item.setToolTip(customer["phone"] if customer["phone"] else "")  # إضافة tooltip
            self.customers_table.setItem(row, 2, phone_item)

            # البريد الإلكتروني
            email_item = QTableWidgetItem(customer["email"] if customer["email"] else "")
            email_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            email_item.setToolTip(customer["email"] if customer["email"] else "")  # إضافة tooltip
            self.customers_table.setItem(row, 3, email_item)

            # إجمالي المشتريات - تنسيق الرقم مثل صفحة المنتجات
            formatted_price = self.format_price(customer['total_purchases'])
            total_purchases = QTableWidgetItem(f"{formatted_price} ج.م")
            total_purchases.setTextAlignment(Qt.AlignLeft | Qt.AlignVCenter)
            total_purchases.setToolTip(f"{formatted_price} ج.م")  # إضافة tooltip
            self.customers_table.setItem(row, 4, total_purchases)

            # آخر زيارة
            last_visit = customer["last_purchase"] if customer["last_purchase"] else "-"
            last_visit_item = QTableWidgetItem(DateTimeUtils.format_date_for_table(last_visit))
            last_visit_item.setTextAlignment(Qt.AlignCenter)
            last_visit_item.setToolTip(DateTimeUtils.format_date_for_table(last_visit))  # إضافة tooltip
            self.customers_table.setItem(row, 5, last_visit_item)

            # دين العميل
            customer_debt = CustomerModel.get_customer_total_debt(customer["id"])
            debt_formatted = self.format_price(customer_debt)
            debt_item = QTableWidgetItem(debt_formatted)
            debt_item.setTextAlignment(Qt.AlignCenter)
            debt_item.setToolTip(debt_formatted)  # إضافة tooltip
            # تلوين الدين بالأحمر إذا كان أكبر من صفر
            if customer_debt > 0:
                debt_item.setForeground(QColor("#e74c3c"))  # لون أحمر للديون
            self.customers_table.setItem(row, 6, debt_item)

            # تحديث الإحصائيات
            total_customers += 1
            total_sales += customer["total_purchases"]
            total_debt += customer_debt

        # تحديث الإحصائيات في الواجهة
        self.update_customer_stats(total_customers, total_sales, total_debt)

    def format_price(self, price):
        """تنسيق السعر بإضافة فواصل الآلاف وتقريب الأرقام العشرية"""
        if price == 0:
            return "0.00"

        # التأكد من أن السعر هو رقم
        try:
            price_float = float(price)
        except (ValueError, TypeError):
            return str(price)

        # تقريب الرقم إلى رقمين عشريين وتحويله إلى نص
        price_formatted = f"{price_float:.2f}"

        # تقسيم الرقم إلى جزء صحيح وجزء عشري
        whole, decimal = price_formatted.split(".")

        # إضافة فواصل الآلاف إلى الجزء الصحيح
        whole_with_commas = ""
        for i, digit in enumerate(reversed(whole)):
            if i > 0 and i % 3 == 0:
                whole_with_commas = "," + whole_with_commas
            whole_with_commas = digit + whole_with_commas

        # دمج الجزأين مرة أخرى
        result = whole_with_commas + "." + decimal

        return result

    def filter_customers(self):
        """تصفية العملاء في الجدول بناءً على النص المدخل والتاريخ"""
        search_text = self.search_input.text().strip()
        date_filter = self.date_filter.currentText()

        # الحصول على العملاء المصفى
        filtered_customers = CustomerModel.search_customers(search_text, date_filter)

        # تحديث الجدول بالنتائج
        self.customers_table.clearContents()
        self.customers_table.setRowCount(0)

        total_customers = 0
        total_sales = 0
        total_debt = 0

        # إضافة العملاء إلى الجدول
        for customer in filtered_customers:
            row = self.customers_table.rowCount()
            self.customers_table.insertRow(row)

            # رقم المسلسل
            serial_item = QTableWidgetItem(str(row + 1))
            serial_item.setTextAlignment(Qt.AlignCenter)
            # تخزين معرف العميل في البيانات المخفية
            serial_item.setData(Qt.UserRole, customer["id"])
            self.customers_table.setItem(row, 0, serial_item)

            # اسم العميل
            name_item = QTableWidgetItem(customer["name"])
            name_item.setTextAlignment(Qt.AlignCenter)
            name_item.setToolTip(customer["name"])  # إضافة tooltip
            self.customers_table.setItem(row, 1, name_item)

            # رقم الهاتف
            phone_item = QTableWidgetItem(customer["phone"] if customer["phone"] else "")
            phone_item.setTextAlignment(Qt.AlignCenter)
            phone_item.setToolTip(customer["phone"] if customer["phone"] else "")  # إضافة tooltip
            self.customers_table.setItem(row, 2, phone_item)

            # البريد الإلكتروني
            email_item = QTableWidgetItem(customer["email"] if customer["email"] else "")
            email_item.setTextAlignment(Qt.AlignRight | Qt.AlignVCenter)
            email_item.setToolTip(customer["email"] if customer["email"] else "")  # إضافة tooltip
            self.customers_table.setItem(row, 3, email_item)

            # إجمالي المشتريات - تنسيق الرقم
            formatted_price = self.format_price(customer['total_purchases'])
            total_sales_item = QTableWidgetItem(formatted_price)
            total_sales_item.setTextAlignment(Qt.AlignCenter)
            self.customers_table.setItem(row, 4, total_sales_item)

            # آخر زيارة
            last_visit = customer["last_purchase"] if customer["last_purchase"] else "-"
            last_visit_item = QTableWidgetItem(DateTimeUtils.format_date_for_table(last_visit))
            last_visit_item.setTextAlignment(Qt.AlignCenter)
            last_visit_item.setToolTip(DateTimeUtils.format_date_for_table(last_visit))  # إضافة tooltip
            self.customers_table.setItem(row, 5, last_visit_item)

            # دين العميل
            customer_debt = CustomerModel.get_customer_total_debt(customer["id"])
            debt_formatted = self.format_price(customer_debt)
            debt_item = QTableWidgetItem(debt_formatted)
            debt_item.setTextAlignment(Qt.AlignCenter)
            debt_item.setToolTip(debt_formatted)  # إضافة tooltip
            # تلوين الدين بالأحمر إذا كان أكبر من صفر
            if customer_debt > 0:
                debt_item.setForeground(QColor("#e74c3c"))  # لون أحمر للديون
            self.customers_table.setItem(row, 6, debt_item)

            # تحديث إجمالي العملاء والمبيعات والديون
            total_customers += 1
            total_sales += customer["total_purchases"]
            total_debt += customer_debt

        # تحديث إحصائيات العملاء
        self.update_customer_stats(total_customers, total_sales, total_debt)

    def refresh_customers_table(self):
        """تحديث جدول العملاء - يستدعى من النوافذ الأخرى عند حدوث تغييرات"""
        self.populate_customers_table()

    def update_customer_stats(self, customer_count, total_sales, total_debt=0):
        """تحديث إحصائيات العملاء في الواجهة"""
        self.total_customers_label.setText(f"إجمالي العملاء: {customer_count}")
        formatted_sales = self.format_price(total_sales)
        self.total_sales_label.setText(f"إجمالي المبيعات: {formatted_sales} ج.م")
        formatted_debt = self.format_price(total_debt)
        self.total_debt_label.setText(f"إجمالي الديون: {formatted_debt} ج.م")

    def get_customer_by_id(self, customer_id):
        """الحصول على بيانات العميل من قاعدة البيانات باستخدام المعرف"""
        return CustomerModel.get_customer_by_id(customer_id)

    def get_customer_transactions(self, customer_id):
        """الحصول على معاملات العميل من قاعدة البيانات"""
        # هذه الطريقة تحتاج لتعديل لترتبط بقاعدة البيانات الحقيقية
        return CustomerModel.get_customer_invoices(customer_id) if hasattr(CustomerModel, 'get_customer_invoices') else []

    def view_customer_details(self, customer_id):
        """عرض تفاصيل العميل بالمعرف المحدد"""
        # التحقق من صلاحية المستخدم
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()

        if main_window and main_window.current_user:
            user_id = main_window.current_user.get('id')
            # التحقق من صلاحية عرض تفاصيل العميل
            from controllers.user_controller import UserController
            if not UserController.check_permission(user_id, "عرض تفاصيل العميل", show_message=True, parent_widget=self):
                return

        # الحصول على بيانات العميل من قاعدة البيانات
        customer = self.get_customer_by_id(customer_id)

        if not customer:
            QMessageBox.warning(self, "تحذير", "لم يتم العثور على العميل!")
            return

        # الحصول على معاملات العميل (للحفاظ على التوافقية مع الكود الآخر)
        transactions = self.get_customer_transactions(customer_id)

        # عرض نافذة تفاصيل العميل مع عرض المنتجات
        dialog = CustomerDetailsDialog(self, customer, transactions)
        dialog.exec_()

    def add_new_customer(self):
        """فتح نافذة إضافة عميل جديد"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية إضافة عميل
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "إضافة عميل", show_message=True, parent_widget=self):
                    return

            dialog = CustomerDialog(self)
            if dialog.exec_() == QDialog.Accepted:
                # الحصول على بيانات العميل الجديد
                customer_data = dialog.get_customer_data()

                # إضافة العميل إلى قاعدة البيانات
                customer_id = CustomerModel.add_customer(customer_data)

                if customer_id:
                    show_information(self, "تم بنجاح", "تم إضافة العميل الجديد بنجاح.")
                    # تحديث جدول العملاء
                    self.populate_customers_table()
                else:
                    QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء إضافة العميل الجديد!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إضافة عميل جديد: {str(e)}")

    def edit_customer(self, customer_id):
        """فتح نافذة تعديل بيانات العميل المحدد"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية تعديل عميل
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "تعديل عميل", show_message=True, parent_widget=self):
                    return

            # الحصول على بيانات العميل من قاعدة البيانات
            customer = self.get_customer_by_id(customer_id)

            if not customer:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على العميل!")
                return

            # فتح نافذة تعديل العميل
            dialog = CustomerDialog(self, customer)
            if dialog.exec_() == QDialog.Accepted:
                # الحصول على البيانات المعدلة
                customer_data = dialog.get_customer_data()

                # تحديث بيانات العميل في قاعدة البيانات
                success = CustomerModel.update_customer(customer_id, customer_data)

                if success:
                    show_information(self, "تم بنجاح", "تم تحديث بيانات العميل بنجاح.")
                    # تحديث جدول العملاء
                    self.populate_customers_table()
                else:
                    QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء تحديث بيانات العميل!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء تعديل بيانات العميل: {str(e)}")

    def delete_customer(self, customer_id):
        """حذف العميل المحدد من قاعدة البيانات"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية حذف عميل
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "حذف عميل", show_message=True, parent_widget=self):
                    return

            # التأكد من رغبة المستخدم في حذف العميل
            confirmation = QMessageBox.question(
                self,
                "تأكيد الحذف",
                "هل أنت متأكد من رغبتك في حذف هذا العميل؟ سيتم إزالة جميع بياناته من النظام.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if confirmation == QMessageBox.Yes:
                # حذف العميل من قاعدة البيانات
                success = CustomerModel.delete_customer(customer_id)

                if success:
                    show_information(self, "تم بنجاح", "تم حذف العميل بنجاح.")
                    # تحديث جدول العملاء
                    self.populate_customers_table()
                else:
                    QMessageBox.critical(self, "خطأ", "حدث خطأ أثناء محاولة حذف العميل!")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف العميل: {str(e)}")

    def create_invoice_for_customer(self, customer_id):
        """إنشاء فاتورة جديدة للعميل المحدد"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window and main_window.current_user:
                user_id = main_window.current_user.get('id')
                # التحقق من صلاحية إضافة عملية بيع
                from controllers.user_controller import UserController
                if not UserController.check_permission(user_id, "إضافة عملية بيع", show_message=True, parent_widget=self):
                    return

            # الحصول على بيانات العميل
            customer = self.get_customer_by_id(customer_id)

            if not customer:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على العميل!")
                return

            # هنا يمكن فتح نافذة إنشاء فاتورة جديدة وتمرير بيانات العميل إليها
            # يمكن تنفيذ ذلك في التحديثات المستقبلية
            QMessageBox.information(self, "إشعار", f"سيتم فتح شاشة إنشاء فاتورة جديدة للعميل: {customer['name']}")
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إنشاء فاتورة جديدة: {str(e)}")

    def apply_styles(self):
        """تطبيق الأنماط على مكونات الصفحة"""
        # استخدام التنسيقات من ملف الستايلات - استخدام get_all_view_styles مثل صفحة التقارير والمنتجات
        self.setObjectName("customers_view")
        self.setStyleSheet(AppStyles.get_all_view_styles())

        # تنسيق ملصق التلميح
        hint_label_style = """
            #hint_label {
                color: #64748b;
                font-style: italic;
                padding: 5px;
                font-size: 11px;
            }
        """

        # تنسيق ملصقات الإحصائيات - تعديل التنسيق ليطابق صفحة التقارير
        stats_label_style = """
            #stats_label {
                background-color: #eef2ff;
                color: #2563eb;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
                border: 1px solid #dbeafe;
                margin-top: 10px;
            }
        """

        # تنسيق إضافي للأزرار والجدول مثل صفحة التقارير والمنتجات
        additional_styles = """
            /* تنسيق أزرار الإجراءات مثل صفحة التقارير */
            #action_button {
                background-color: #3b82f6;
                color: white;
                border: 1px solid #60a5fa;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                text-align: center;
            }

            #action_button:hover {
                background-color: #2563eb;
                border: 1px solid #3b82f6;
            }

            #action_button:pressed {
                background-color: #1d4ed8;
                border: 1px solid #2563eb;
            }

            #secondary_button {
                background-color: white;
                color: #3b82f6;
                border: 1px solid #3b82f6;
                border-radius: 6px;
                padding: 8px 16px;
                font-weight: bold;
                text-align: center;
            }

            #secondary_button:hover {
                background-color: #e2e8f0;
                border: 1px solid #2563eb;
                color: #2563eb;
            }

            #secondary_button:pressed {
                background-color: #f1f5f9;
                border: 1px solid #1d4ed8;
                color: #1d4ed8;
            }

            /* تنسيق الجدول مثل صفحة التقارير */
            QTableView, QTableWidget {
                border: 1px solid #e2e8f0;
                background-color: white;
                gridline-color: #e2e8f0;
                border-radius: 6px;
                selection-background-color: rgba(59, 130, 246, 0.2);
                selection-color: #0f172a;
            }

            QTableView::item, QTableWidget::item {
                padding: 8px;
                border-bottom: 1px solid #e2e8f0;
            }

            QTableView::item:selected, QTableWidget::item:selected {
                background-color: rgba(59, 130, 246, 0.2);
                color: #0f172a;
            }

            QHeaderView::section {
                background-color: #f1f5f9;
                padding: 10px;
                border: none;
                border-bottom: 1px solid #cbd5e1;
                font-weight: bold;
                color: #334155;
            }
        """

        # تطبيق التنسيقات
        self.setStyleSheet(self.styleSheet() + hint_label_style + stats_label_style + additional_styles)

    def refresh_page(self):
        """تحديث بيانات الصفحة عند الانتقال إليها أو عند فتح التطبيق"""
        # تحديث بيانات العملاء في الجدول
        self.populate_customers_table()

        # تحديث حالة تفعيل الأزرار بناءً على صلاحيات المستخدم
        self.update_buttons_state()

        print("تم تحديث صفحة العملاء")

    def update_buttons_state(self):
        """تحديث حالة تفعيل الأزرار بناءً على صلاحيات المستخدم"""
        # التحقق من وجود مستخدم حالي
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()

        if not main_window or not main_window.current_user:
            return

        user_id = main_window.current_user.get('id')
        username = main_window.current_user.get('username')

        # المستخدم admin له جميع الصلاحيات
        if username == 'admin':
            # تفعيل زر إضافة عميل جديد
            self.new_customer_btn.setEnabled(True)
            self.new_customer_btn.setStyleSheet("")
            return

        # استيراد وحدة التحكم بالمستخدمين
        from controllers.user_controller import UserController

        # تحديث حالة زر إضافة عميل جديد
        has_add_customer_permission = UserController.check_permission(user_id, "إضافة عميل")
        self.new_customer_btn.setEnabled(has_add_customer_permission)
        if not has_add_customer_permission:
            self.new_customer_btn.setStyleSheet("""
                background-color: #f0f0f0;
                color: #999999;
                border: 1px solid #cccccc;
            """)
        else:
            self.new_customer_btn.setStyleSheet("")


class CustomerDialog(QDialog):
    """نافذة إضافة أو تعديل عميل"""
    def __init__(self, parent=None, customer_data=None):
        super().__init__(parent)

        # تعيين عنوان النافذة حسب الوضع (إضافة/تعديل)
        self.is_edit_mode = customer_data is not None
        if self.is_edit_mode:
            self.setWindowTitle("تعديل بيانات العميل")
        else:
            self.setWindowTitle("إضافة عميل جديد")

        self.setMinimumWidth(400)
        self.setLayoutDirection(Qt.RightToLeft)

        # إنشاء التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # إنشاء نموذج إدخال البيانات
        form_layout = QFormLayout()
        form_layout.setLabelAlignment(Qt.AlignRight)
        form_layout.setSpacing(15)

        # حقل اسم العميل
        self.name_input = QLineEdit()
        self.name_input.setObjectName("search_input")
        self.name_input.setPlaceholderText("أدخل اسم العميل")
        form_layout.addRow("اسم العميل:", self.name_input)

        # حقل رقم الهاتف
        self.phone_input = QLineEdit()
        self.phone_input.setObjectName("search_input")
        self.phone_input.setPlaceholderText("أدخل رقم الهاتف")
        form_layout.addRow("رقم الهاتف:", self.phone_input)

        # حقل البريد الإلكتروني
        self.email_input = QLineEdit()
        self.email_input.setObjectName("search_input")
        self.email_input.setPlaceholderText("أدخل البريد الإلكتروني")
        form_layout.addRow("البريد الإلكتروني:", self.email_input)

        # حقل العنوان
        self.address_input = QLineEdit()
        self.address_input.setObjectName("search_input")
        self.address_input.setPlaceholderText("أدخل العنوان")
        form_layout.addRow("العنوان:", self.address_input)

        layout.addLayout(form_layout)
        layout.addSpacing(20)

        # إضافة أزرار الحفظ والإلغاء
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)

        # تعريب الأزرار
        ok_btn = buttons.button(QDialogButtonBox.Ok)
        cancel_btn = buttons.button(QDialogButtonBox.Cancel)
        ok_btn.setText("حفظ")
        cancel_btn.setText("إلغاء")
        # تعيين objectName مميز لكل زر
        ok_btn.setObjectName("primary_button")
        cancel_btn.setObjectName("secondary_button")

        layout.addWidget(buttons)

        # تعبئة البيانات إذا كنا في وضع التعديل
        if self.is_edit_mode:
            self.name_input.setText(customer_data["name"])
            self.phone_input.setText(customer_data["phone"])
            self.email_input.setText(customer_data["email"])
            self.address_input.setText(customer_data["address"])

        # تطبيق الأنماط
        self.apply_styles()
        # تطبيق تنسيقات الحوارات المخصصة (تم نقلها إلى apply_styles ولا داعي لتكرارها هنا)
        # from styles import AppStyles
        # self.setStyleSheet(self.styleSheet() + AppStyles.get_dialog_style())
        # Aplicar configuración RTL a todos los widgets
        apply_rtl_to_all_widgets(self)
        # إعادة تعيين objectName بعد كل شيء
        ok_btn.setObjectName("primary_button")
        cancel_btn.setObjectName("secondary_button")
        # إعادة تعيين setStyleSheet بعد تعيين objectName
        self.setStyleSheet("")
        self.apply_styles()
        # تعيين ستايل مباشر كحل أخير
        ok_btn.setStyleSheet("background-color: #0078d7 !important; color: white !important; border: 1px solid #0078d7 !important; font-weight: bold !important;")

        # توحيد ارتفاع الزرين
        height = max(ok_btn.sizeHint().height(), cancel_btn.sizeHint().height())
        ok_btn.setMinimumHeight(height)
        cancel_btn.setMinimumHeight(height)
        ok_btn.setMaximumHeight(height)
        cancel_btn.setMaximumHeight(height)

    def get_customer_data(self):
        """الحصول على بيانات العميل المدخلة"""
        return {
            "name": self.name_input.text(),
            "phone": self.phone_input.text(),
            "email": self.email_input.text(),
            "address": self.address_input.text(),
        }

    def apply_styles(self):
        """تطبيق الأنماط على النافذة (حوار إضافة/تعديل عميل) بدون تكرار أو تعارض"""
        from styles import AppStyles
        # فقط ستايل الحوارات والأزرار المنبثقة
        dialog_styles = AppStyles.get_dialog_style()
        additional_styles = """
            QPushButton#primary_button {
                background-color: #0078d7;
                color: white;
                border: 1px solid #0078d7;
                border-radius: 2px;
                padding: 5px 12px;
                font-weight: bold;
            }
            QPushButton#primary_button:hover {
                background-color: #1a86d9;
                border: 1px solid #1a86d9;
            }
            QPushButton#primary_button:pressed {
                background-color: #006cc1;
                border: 1px solid #006cc1;
            }
            QPushButton#secondary_button {
                background-color: #e1e1e1;
                color: #000000;
                border: 1px solid #adadad;
                border-radius: 2px;
                padding: 5px 12px;
            }
            QPushButton#secondary_button:hover {
                background-color: #e5f1fb;
                border: 1px solid #0078d7;
            }
            QPushButton#secondary_button:pressed {
                background-color: #cce4f7;
                border: 1px solid #0078d7;
            }
            #field_label {
                color: #3b82f6;
                font-weight: bold;
            }
            #section_title {
                color: #1e3a8a;
                font-weight: bold;
            }
            #filter_container {
                padding: 0px;
                margin: 0px;
                background-color: transparent;
            }
            #action_button {
                margin-top: 0px;
                padding-top: 0px;
                padding-bottom: 0px;
                margin-bottom: 0px;
            }
            QTableWidget {
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                background-color: white;
            }
            QTableWidget::item:selected {
                background-color: rgba(59, 130, 246, 0.3);
                color: #0f172a;
            }
        """
        self.setStyleSheet(dialog_styles + additional_styles)


class CustomerDetailsDialog(QDialog):
    """نافذة عرض تفاصيل العميل وتعاملاته"""
    def __init__(self, parent, customer, transactions):
        super().__init__(parent)

        self.customer = customer
        self.transactions = transactions

        # جلب الشهر الحالي والسنة الحالية
        current_date = datetime.datetime.now()
        self.selected_month = current_date.month
        self.selected_year = current_date.year

        # جلب جميع المنتجات المشتراة من طرف العميل
        from models.customers import CustomerModel
        raw_products = CustomerModel.get_customer_products(customer['id'])

        # إضافة حقل invoice_status لكل منتج للتوافق مع فلتر حالة الدفع
        self.products = []
        for product in raw_products:
            product_dict = dict(product)  # تحويل إلى قاموس عادي
            product_dict['invoice_status'] = product_dict.get('status', 'غير مدفوعة')  # إضافة حقل invoice_status
            self.products.append(product_dict)

        self.all_products = self.products.copy()  # نسخة من جميع المنتجات للرجوع إليها عند التصفية

        # الحصول على إجمالي الديون المستحقة للعميل
        self.customer_debt = CustomerModel.get_customer_total_debt(customer['id'])

        self.setWindowTitle(f"تفاصيل العميل: {customer['name']}")
        self.setMinimumSize(800, 375)
        self.setLayoutDirection(Qt.RightToLeft)

        # إنشاء التخطيط الرئيسي
        layout = QVBoxLayout(self)

        # قسم بيانات العميل
        customer_section = QFrame()
        customer_section.setObjectName("filter_frame")
        customer_section.setFrameShape(QFrame.StyledPanel)
        customer_section_layout = QVBoxLayout(customer_section)
        customer_section_layout.setContentsMargins(10, 3, 10, 3)  # Reducir márgenes verticales de 5,5 a 3,3
        customer_section_layout.setSpacing(2)  # Reducir espaciado entre elementos de 3 a 2

        # تخطيط العنوان والفاصل في سطر واحد أفقي
        title_layout = QHBoxLayout()
        title_layout.setSpacing(5)

        # عنوان القسم
        section_title = QLabel("معلومات العميل")
        section_title.setObjectName("section_title")
        section_title.setFont(QFont("Arial", 10, QFont.Bold))
        title_layout.addWidget(section_title)

        # فاصل أفقي
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("filter_separator")
        title_layout.addWidget(separator, 1)

        customer_section_layout.addLayout(title_layout)

        # بيانات العميل
        customer_info_layout = QGridLayout()
        customer_info_layout.setHorizontalSpacing(0)
        customer_info_layout.setVerticalSpacing(2)  # Reducir el espaciado vertical de 5 a 2

        # الصف الأول من البيانات
        name_label = QLabel("اسم العميل:")
        name_label.setObjectName("field_label")
        name_label.setFont(QFont("Arial", 9))

        name_value = QLabel(customer["name"])
        name_value.setFont(QFont("Arial", 9, QFont.Bold))

        phone_label = QLabel("رقم الهاتف:")
        phone_label.setObjectName("field_label")
        phone_label.setFont(QFont("Arial", 9))

        phone_value = QLabel(customer["phone"])
        phone_value.setFont(QFont("Arial", 9))

        # Modificar la forma en que se colocan los widgets para eliminar el espacio entre etiquetas y valores
        name_layout = QHBoxLayout()
        name_layout.setSpacing(0)  # Sin espacio entre la etiqueta y el valor
        name_layout.setContentsMargins(0, 0, 0, 0)  # Sin márgenes en el layout
        name_layout.addWidget(name_label)
        name_layout.addWidget(name_value)
        name_layout.addStretch(1)  # Agregar espacio flexible al final

        phone_layout = QHBoxLayout()
        phone_layout.setSpacing(0)  # Sin espacio entre la etiqueta y el valor
        phone_layout.setContentsMargins(0, 0, 0, 0)  # Sin márgenes en el layout
        phone_layout.addWidget(phone_label)
        phone_layout.addWidget(phone_value)
        phone_layout.addStretch(1)  # Agregar espacio flexible al final

        # Agregar los layouts a la cuadrícula
        temp_widget_name = QWidget()
        temp_widget_name.setLayout(name_layout)
        temp_widget_name.setContentsMargins(0, 0, 0, 0)  # Sin márgenes en el widget

        temp_widget_phone = QWidget()
        temp_widget_phone.setLayout(phone_layout)
        temp_widget_phone.setContentsMargins(0, 0, 0, 0)  # Sin márgenes en el widget

        customer_info_layout.addWidget(temp_widget_name, 0, 0)
        customer_info_layout.addWidget(temp_widget_phone, 0, 1)

        # الصف الثاني من البيانات
        email_label = QLabel("البريد الإلكتروني:")
        email_label.setObjectName("field_label")
        email_label.setFont(QFont("Arial", 9))

        email_value = QLabel(customer["email"])
        email_value.setFont(QFont("Arial", 9))

        address_label = QLabel("العنوان:")
        address_label.setObjectName("field_label")
        address_label.setFont(QFont("Arial", 9))

        address_value = QLabel(customer["address"])
        address_value.setFont(QFont("Arial", 9))

        # Crear layouts horizontales sin espaciado para email y dirección
        email_layout = QHBoxLayout()
        email_layout.setSpacing(0)
        email_layout.setContentsMargins(0, 0, 0, 0)  # Sin márgenes en el layout
        email_layout.addWidget(email_label)
        email_layout.addWidget(email_value)
        email_layout.addStretch(1)

        address_layout = QHBoxLayout()
        address_layout.setSpacing(0)
        address_layout.setContentsMargins(0, 0, 0, 0)  # Sin márgenes en el layout
        address_layout.addWidget(address_label)
        address_layout.addWidget(address_value)
        address_layout.addStretch(1)

        # Agregar los layouts a la cuadrícula
        temp_widget_email = QWidget()
        temp_widget_email.setLayout(email_layout)
        temp_widget_email.setContentsMargins(0, 0, 0, 0)  # Sin márgenes en el widget

        temp_widget_address = QWidget()
        temp_widget_address.setLayout(address_layout)
        temp_widget_address.setContentsMargins(0, 0, 0, 0)  # Sin márgenes en el widget

        customer_info_layout.addWidget(temp_widget_email, 1, 0)
        customer_info_layout.addWidget(temp_widget_address, 1, 1)

        # الصف الثالث من البيانات
        total_purchases_label = QLabel("إجمالي المشتريات:")
        total_purchases_label.setObjectName("field_label")
        total_purchases_label.setFont(QFont("Arial", 9))

        self.total_purchases_value = QLabel("*****")
        self.total_purchases_value.setFont(QFont("Arial", 9))
        self.total_purchases_value.setCursor(Qt.PointingHandCursor)
        self.total_purchases_value.setToolTip("انقر للإخفاء/الإظهار")
        self.total_purchases_value.mousePressEvent = self.toggle_total_purchases_visibility
        self._total_purchases_hidden = True

        visit_count_label = QLabel("عدد الزيارات:")
        visit_count_label.setObjectName("field_label")
        visit_count_label.setFont(QFont("Arial", 9))

        visit_count_value = QLabel(str(customer["visit_count"]))
        visit_count_value.setFont(QFont("Arial", 9))

        last_purchase_label = QLabel("آخر زيارة:")
        last_purchase_label.setObjectName("field_label")
        last_purchase_label.setFont(QFont("Arial", 9))

        last_purchase_value = QLabel(customer["last_purchase"] if customer["last_purchase"] else "لا يوجد")
        last_purchase_value.setFont(QFont("Arial", 9))

        # Crear layouts horizontales sin espaciado para la tercera fila
        purchases_layout = QHBoxLayout()
        purchases_layout.setSpacing(0)
        purchases_layout.setContentsMargins(0, 0, 0, 0)  # Sin márgenes en el layout
        purchases_layout.addWidget(total_purchases_label)
        purchases_layout.addWidget(self.total_purchases_value)
        purchases_layout.addStretch(1)

        visits_layout = QHBoxLayout()
        visits_layout.setSpacing(0)
        visits_layout.setContentsMargins(0, 0, 0, 0)  # Sin márgenes en el layout
        visits_layout.addWidget(visit_count_label)
        visits_layout.addWidget(visit_count_value)
        visits_layout.addStretch(1)

        last_visit_layout = QHBoxLayout()
        last_visit_layout.setSpacing(0)
        last_visit_layout.setContentsMargins(0, 0, 0, 0)  # Sin márgenes en el layout
        last_visit_layout.addWidget(last_purchase_label)
        last_visit_layout.addWidget(last_purchase_value)
        last_visit_layout.addStretch(1)

        # Agregar los layouts a la cuadrícula
        temp_widget_purchases = QWidget()
        temp_widget_purchases.setLayout(purchases_layout)
        temp_widget_purchases.setContentsMargins(0, 0, 0, 0)  # Sin márgenes en el widget

        temp_widget_visits = QWidget()
        temp_widget_visits.setLayout(visits_layout)
        temp_widget_visits.setContentsMargins(0, 0, 0, 0)  # Sin márgenes en el widget

        temp_widget_last_visit = QWidget()
        temp_widget_last_visit.setLayout(last_visit_layout)
        temp_widget_last_visit.setContentsMargins(0, 0, 0, 0)  # Sin márgenes en el widget

        customer_info_layout.addWidget(temp_widget_purchases, 2, 0)
        customer_info_layout.addWidget(temp_widget_visits, 2, 1)
        customer_info_layout.addWidget(temp_widget_last_visit, 2, 2)

        customer_section_layout.addLayout(customer_info_layout)
        layout.addWidget(customer_section)

        # قسم المنتجات المشتراة
        layout.addSpacing(5)  # Reducir espaciado de 10 a 5

        products_section = QFrame()
        products_section.setObjectName("filter_frame")
        products_section.setFrameShape(QFrame.StyledPanel)
        products_section_layout = QVBoxLayout(products_section)
        products_section_layout.setContentsMargins(10, 5, 10, 5)  # Reducir márgenes
        products_section_layout.setSpacing(3)  # Reducir espaciado

        # تخطيط العنوان والفاصل في سطر واحد أفقي - similar al sección de cliente
        title_products_layout = QHBoxLayout()
        title_products_layout.setSpacing(5)

        # عنوان القسم
        products_title = QLabel("المنتجات المشتراة")
        products_title.setObjectName("section_title")
        products_title.setFont(QFont("Arial", 10, QFont.Bold))  # Reducir de 12 a 10
        title_products_layout.addWidget(products_title)

        # فاصل أفقي
        separator2 = QFrame()
        separator2.setFrameShape(QFrame.HLine)
        separator2.setFrameShadow(QFrame.Sunken)
        separator2.setObjectName("filter_separator")
        title_products_layout.addWidget(separator2, 1)  # El separador ocupa el espacio restante

        products_section_layout.addLayout(title_products_layout)

        # إضافة إطار خفيف حول عناصر التصفية
        filter_frame = QWidget()  # Cambiar de QFrame a QWidget
        filter_frame.setObjectName("filter_container")  # Cambiar el ID del objeto
        filter_frame.setContentsMargins(0, 0, 0, 0)  # Establecer márgenes del widget a cero
        filter_frame_layout = QHBoxLayout(filter_frame)
        filter_frame_layout.setContentsMargins(0, 0, 0, 0)  # Eliminar todos los márgenes
        filter_frame_layout.setSpacing(10)

        # تصفية الشهر
        month_label = QLabel("الشهر:")
        month_label.setObjectName("field_label")

        self.month_combo = RTLComboBox()
        self.month_combo.setObjectName("combo_box")
        self.month_combo.addItem("كل الشهور", None)
        # إضافة الشهور
        months = [
            ("يناير", 1), ("فبراير", 2), ("مارس", 3), ("إبريل", 4),
            ("مايو", 5), ("يونيو", 6), ("يوليو", 7), ("أغسطس", 8),
            ("سبتمبر", 9), ("أكتوبر", 10), ("نوفمبر", 11), ("ديسمبر", 12)
        ]
        for month_name, month_num in months:
            self.month_combo.addItem(month_name, month_num)

        self.month_combo.setFixedHeight(35)

        # تصفية السنة
        year_label = QLabel("السنة:")
        year_label.setObjectName("field_label")

        self.year_combo = RTLComboBox()
        self.year_combo.setObjectName("combo_box")
        self.year_combo.addItem("كل السنوات", None)
        # إضافة السنوات من 2020 إلى السنة الحالية
        current_year = datetime.datetime.now().year
        for year in range(current_year, 2019, -1):
            self.year_combo.addItem(str(year), year)

        self.year_combo.setFixedHeight(35)

        # مربع تعليم لعرض المنتجات المدفوعة
        self.show_paid_checkbox = QCheckBox("عرض المنتجات المدفوعة")
        self.show_paid_checkbox.setObjectName("filter_checkbox")
        self.show_paid_checkbox.setChecked(False)  # افتراضياً غير معلم (عرض غير المدفوعة فقط)
        self.show_paid_checkbox.setFont(QFont("Arial", 9))
        # إضافة event handler للـ checkbox
        self.show_paid_checkbox.stateChanged.connect(self.apply_filter_silently)
        self.show_paid_checkbox.setStyleSheet("""
            QCheckBox#filter_checkbox {
                color: #374151;
                font-weight: normal;
            }
            QCheckBox#filter_checkbox::indicator {
                width: 16px;
                height: 16px;
            }
            QCheckBox#filter_checkbox::indicator:unchecked {
                border: 2px solid #d1d5db;
                background-color: white;
                border-radius: 3px;
            }
            QCheckBox#filter_checkbox::indicator:checked {
                border: 2px solid #3b82f6;
                background-color: #3b82f6;
                border-radius: 3px;
                image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iMTIiIHZpZXdCb3g9IjAgMCAxMiAxMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEwIDNMNC41IDguNUwyIDYiIHN0cm9rZT0id2hpdGUiIHN0cm9rZS13aWR0aD0iMiIgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIiBzdHJva2UtbGluZWpvaW49InJvdW5kIi8+Cjwvc3ZnPgo=);
            }
        """)

        # زر تطبيق التصفية
        apply_filter_button = QPushButton("تطبيق")
        apply_filter_button.setObjectName("action_button")
        apply_filter_button.clicked.connect(self.apply_filter)
        apply_filter_button.setFixedHeight(35)
        apply_filter_button.setFont(QFont("Arial", 10))
        apply_filter_button.setStyleSheet("margin-top: 0px; padding-top: 0px;")

        # إضافة عناصر التصفية إلى التخطيط الإطار
        filter_frame_layout.addWidget(month_label)
        filter_frame_layout.addWidget(self.month_combo)
        filter_frame_layout.addSpacing(10)
        filter_frame_layout.addWidget(year_label)
        filter_frame_layout.addWidget(self.year_combo)
        filter_frame_layout.addSpacing(15)
        filter_frame_layout.addWidget(self.show_paid_checkbox)
        filter_frame_layout.addSpacing(20)
        filter_frame_layout.addWidget(apply_filter_button)
        filter_frame_layout.addStretch(1)

        # إضافة الإطار إلى تخطيط قسم المنتجات
        products_section_layout.addWidget(filter_frame)

        # Eliminar cualquier espacio adicional entre el filtro y la tabla
        products_section_layout.setSpacing(2)  # Reducir el espaciado entre elementos

        # جدول المنتجات المشتراة
        self.products_table = QTableWidget()
        self.products_table.setColumnCount(8)  # زيادة عدد الأعمدة من 7 إلى 8
        self.products_table.setHorizontalHeaderLabels(["#", "رقم الفاتورة", "اسم المنتج", "كود المنتج", "الكمية", "السعر", "حالة الدفع", "التاريخ"])
        self.products_table.setMinimumHeight(375)

        # إضافة قائمة السياق للجدول
        self.products_table.setContextMenuPolicy(Qt.CustomContextMenu)
        self.products_table.customContextMenuRequested.connect(self.show_product_context_menu)

        # تعديل عرض الأعمدة لضمان ظهور أسماء المنتجات بشكل كامل
        header = self.products_table.horizontalHeader()

        # عمود المسلسل - عرض ثابت صغير
        header.setSectionResizeMode(0, QHeaderView.Interactive) # المسلسل
        self.products_table.setColumnWidth(0, 40)  # عرض صغير للمسلسل

        # تقليل عرض الأعمدة الأخرى لتوفير مساحة أكبر لأسماء المنتجات
        header.setSectionResizeMode(1, QHeaderView.Interactive) # رقم الفاتورة
        self.products_table.setColumnWidth(1, 95)  # زيادة من 85 إلى 95 لإظهار اسم العمود بالكامل

        # عمود اسم المنتج - أكبر عرض لضمان ظهور الأسماء الطويلة
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        self.products_table.setMinimumWidth(1000)  # زيادة العرض أكثر لإظهار عمود المسلسل بوضوح

        header.setSectionResizeMode(3, QHeaderView.Interactive) # كود المنتج
        self.products_table.setColumnWidth(3, 90)  # زيادة من 80 إلى 90 لإظهار اسم العمود بالكامل

        header.setSectionResizeMode(4, QHeaderView.Interactive) # الكمية
        self.products_table.setColumnWidth(4, 60)  # زيادة من 50 إلى 60 لإظهار اسم العمود بالكامل

        header.setSectionResizeMode(5, QHeaderView.Interactive) # السعر
        self.products_table.setColumnWidth(5, 90)  # تقليل من 100 إلى 90

        header.setSectionResizeMode(6, QHeaderView.Interactive) # حالة الدفع
        self.products_table.setColumnWidth(6, 100)  # تقليل من 120 إلى 100

        header.setSectionResizeMode(7, QHeaderView.Interactive) # التاريخ
        self.products_table.setColumnWidth(7, 180)  # زيادة من 140 إلى 180 لإظهار التاريخ والوقت بالكامل

        self.products_table.verticalHeader().setVisible(False)
        self.products_table.setEditTriggers(QAbstractItemView.NoEditTriggers)

        # تعديل سلوك التحديد لإصلاح مشكلة ألوان الصفوف المحددة
        self.products_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.products_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.products_table.setAlternatingRowColors(False)

        # تطبيق أنماط مخصصة لضمان ظهور لون التحديد بشكل صحيح وتوحيد لون الخلفية
        self.products_table.setStyleSheet("""
            QTableWidget {
                background-color: white;
            }
            QTableWidget::item {
                background-color: #f8fafc;
                padding: 5px;
                word-wrap: break-word;
            }
            QTableWidget::item:selected {
                background-color: rgba(59, 130, 246, 0.3);
                color: #0f172a;
            }
            QTableWidget::item:selected:active {
                background-color: rgba(59, 130, 246, 0.4);
                color: #0f172a;
            }
        """)

        # ضبط ارتفاع الصفوف لضمان ظهور النص بشكل كامل
        self.products_table.verticalHeader().setDefaultSectionSize(28)  # تقليل من 32 إلى 28 لمظهر أكثر إحكاماً

        # تفعيل التفاف النص في الخلايا
        self.products_table.setWordWrap(True)

        # ملء جدول المنتجات
        self.populate_products_table()

        products_section_layout.addWidget(self.products_table)
        layout.addWidget(products_section)

        # إضافة قسم حالة الرصيد (الديون والمستحقات)
        layout.addSpacing(5)

        balance_section = QFrame()
        balance_section.setObjectName("filter_frame")
        balance_section.setFrameShape(QFrame.StyledPanel)
        balance_section_layout = QVBoxLayout(balance_section)
        balance_section_layout.setContentsMargins(8, 4, 8, 4)  # Reducir los márgenes de 10,5,10,5 a 8,4,8,4
        balance_section_layout.setSpacing(2)  # Reducir el espaciado vertical de 3 a 2

        # تخطيط العنوان والفاصل في سطر واحد أفقي
        title_layout = QHBoxLayout()
        title_layout.setContentsMargins(0, 0, 0, 0)  # Eliminar márgenes innecesarios

        # عنوان القسم - تصغير الخط
        balance_title = QLabel("حالة الرصيد")
        balance_title.setObjectName("section_title")
        balance_title.setFont(QFont("Arial", 10, QFont.Bold))
        title_layout.addWidget(balance_title)

        # فاصل أفقي
        separator3 = QFrame()
        separator3.setFrameShape(QFrame.HLine)
        separator3.setFrameShadow(QFrame.Sunken)
        separator3.setObjectName("filter_separator")
        title_layout.addWidget(separator3, 1)

        balance_section_layout.addLayout(title_layout)

        # إضافة فريم لعرض مجموع مشتريات العميل باللون الأخضر (مع نقله إلى أقصى اليسار)
        # تم إزالة هذا الفريم لأن المعلومات موجودة بالفعل في بيانات العميل

        # إطار المبلغ المدين (الدين على العميل) - باللون الأحمر
        debt_frame = QFrame()
        debt_frame.setObjectName("debt_frame")
        debt_frame.setFrameShape(QFrame.StyledPanel)
        debt_frame.setMaximumWidth(200)  # تحديد الحد الأقصى للعرض
        debt_frame.setStyleSheet("""
            #debt_frame {
                background-color: rgba(254, 226, 226, 0.7);
                border: 1px solid #dc2626;
                border-radius: 4px;
                padding: 3px;
            }
        """)

        debt_layout = QVBoxLayout(debt_frame)
        debt_layout.setContentsMargins(3, 2, 3, 2)  # Reducir los márgenes de 5,3,5,3 a 3,2,3,2
        debt_layout.setSpacing(1)  # Reducir el espaciado de 2 a 1

        debt_title = QLabel("الدين على العميل")
        debt_title.setStyleSheet("color: #dc2626; font-weight: bold; font-size: 11px;")
        debt_title.setAlignment(Qt.AlignCenter)

        # استخدام قيمة الدين الحقيقية المحسوبة من قاعدة البيانات
        total_debt = self.customer_debt

        self.debt_value = QLabel(f"{total_debt:.2f} ج.م")
        self.debt_value.setStyleSheet("color: #dc2626; font-size: 14px; font-weight: bold;")
        self.debt_value.setAlignment(Qt.AlignCenter)

        debt_layout.addWidget(debt_title)
        debt_layout.addWidget(self.debt_value)

        # تخطيط للفريم الأحمر وزر دفع الدين
        debt_container = QHBoxLayout()
        debt_container.setSpacing(6)  # Reducir el espaciado de 10 a 6
        debt_container.setContentsMargins(0, 0, 0, 0)  # Eliminar márgenes innecesarios

        # إضافة زر دفع الدين - يتطلب صلاحية إدارة ديون العملاء
        payment_button = QPushButton("💰  دفع")
        payment_button.setObjectName("action_button")
        payment_button.setCursor(Qt.PointingHandCursor)
        payment_button.setMinimumHeight(60)
        payment_button.setMaximumHeight(60)
        payment_button.setMinimumWidth(100)
        payment_button.setToolTip("تسجيل دفعة جديدة لسداد دين العميل")
        
        # التحقق من صلاحية إدارة ديون العملاء
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()
        
        user_id = None
        if main_window and main_window.current_user:
            user_id = main_window.current_user.get('id')
        
        from controllers.user_controller import UserController
        has_debt_permission = user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "إدارة ديون العملاء")
        
        if has_debt_permission:
            payment_button.clicked.connect(self.show_payment_history)
            payment_button.setStyleSheet("""
                QPushButton#action_button {
                    background-color: #3b82f6;
                    color: white;
                    border: 1px solid #60a5fa;
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-weight: bold;
                    font-size: 12px;
                }
                QPushButton#action_button:hover {
                    background-color: #2563eb;
                }
            """)
        else:
            payment_button.setEnabled(False)
            payment_button.setToolTip("ليس لديك صلاحية إدارة ديون العملاء")
            payment_button.setStyleSheet("""
                QPushButton#action_button {
                    background-color: #f0f0f0;
                    color: #999999;
                    border: 1px solid #cccccc;
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-weight: bold;
                    font-size: 12px;
                }
            """)

        # إضافة الفريم الأحمر وزر الدفع إلى الحاوية
        debt_container.addWidget(debt_frame)
        debt_container.addWidget(payment_button)

        # إضافة زر عرض سجل المدفوعات - يتطلب صلاحية إدارة ديون العملاء
        payment_history_button = QPushButton("📋  سجل المدفوعات")
        payment_history_button.setMinimumHeight(60)
        payment_history_button.setMaximumHeight(60)
        payment_history_button.setMinimumWidth(120)
        payment_history_button.setObjectName("action_button")
        payment_history_button.setCursor(Qt.PointingHandCursor)
        payment_history_button.setToolTip("عرض سجل جميع مدفوعات العميل")
        
        if has_debt_permission:
            payment_history_button.clicked.connect(self.show_customer_payments)
            payment_history_button.setStyleSheet("""
                QPushButton#action_button {
                    background-color: #0ea5e9;
                    color: white;
                    border: 1px solid #38bdf8;
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-weight: bold;
                    font-size: 12px;
                }
                QPushButton#action_button:hover {
                    background-color: #0284c7;
                }
            """)
        else:
            payment_history_button.setEnabled(False)
            payment_history_button.setToolTip("ليس لديك صلاحية إدارة ديون العملاء")
            payment_history_button.setStyleSheet("""
                QPushButton#action_button {
                    background-color: #f0f0f0;
                    color: #999999;
                    border: 1px solid #cccccc;
                    border-radius: 4px;
                    padding: 4px 8px;
                    font-weight: bold;
                    font-size: 12px;
                }
            """)
        debt_container.addWidget(payment_history_button)

        # إضافة زر طباعة المشتريات المحددة
        print_button = QPushButton("🖨️  طباعة المشتريات")
        print_button.setMinimumHeight(60)
        print_button.setMaximumHeight(60)
        print_button.setMinimumWidth(120)
        print_button.setObjectName("action_button")
        print_button.setCursor(Qt.PointingHandCursor)
        print_button.clicked.connect(self.print_selected_purchases)
        print_button.setToolTip("طباعة المشتريات المعروضة في الجدول")
        print_button.setStyleSheet("""
            QPushButton#action_button {
                background-color: #0891b2;
                color: white;
                border: 1px solid #22d3ee;
                border-radius: 4px;
                padding: 4px 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton#action_button:hover {
                background-color: #0e7490;
            }
        """)
        debt_container.addWidget(print_button)

        # إضافة زر الإغلاق في نفس الصف مع زر دفع الدين ولكن في أقصى اليسار
        close_button = QPushButton("إغلاق")
        close_button.clicked.connect(self.close)
        close_button.setMinimumWidth(100)
        close_button.setMinimumHeight(60)
        close_button.setMaximumHeight(60)
        close_button.setObjectName("action_button")
        close_button.setCursor(Qt.PointingHandCursor)
        close_button.setToolTip("إغلاق نافذة تفاصيل العميل")
        close_button.setStyleSheet("""
            QPushButton#action_button {
                background-color: #ef4444;
                color: white;
                border: 1px solid #f87171;
                border-radius: 4px;
                padding: 4px 8px;
                font-weight: bold;
                font-size: 12px;
            }
            QPushButton#action_button:hover {
                background-color: #dc2626;
            }
        """)

        # إنشاء تخطيط للإطارات المالية
        balance_info_layout = QHBoxLayout()
        balance_info_layout.setSpacing(10)  # Reducir el espaciado
        balance_info_layout.setContentsMargins(0, 0, 0, 0)  # Eliminar márgenes

        # تجميع العناصر في أقصى اليمين واليسار
        balance_info_layout.addLayout(debt_container)  # الفريم الأحمر وزر الدفع في أقصى اليمين
        balance_info_layout.addStretch(1)  # مساحة مرنة (لدفع زر الإغلاق إلى اليسار)
        balance_info_layout.addWidget(close_button)  # زر الإغلاق في أقصى اليسار

        # إضافة التخطيط إلى قسم الرصيد
        balance_section_layout.addLayout(balance_info_layout)

        # إضافة قسم الرصيد إلى التخطيط الرئيسي
        layout.addWidget(balance_section)
        layout.addSpacing(5)

        # تطبيق الأنماط
        self.apply_styles()

        # ضبط الشهر الحالي كقيمة افتراضية
        # تحديد الشهر الحالي (مع مراعاة أن الخيار الأول في القائمة هو "كل الشهور")
        current_month_index = current_date.month  # نحصل على رقم الشهر الحالي (1-12)
        self.month_combo.setCurrentIndex(current_month_index)  # تحديد الشهر الحالي

        # تطبيق التصفية تلقائيًا عند فتح النافذة لعرض منتجات الشهر الحالي
        self.apply_filter_silently()

        # ضبط حجم النافذة بشكل متوازن لعرض أسماء المنتجات بوضوح
        self.resize(1050, 700)  # ارتفاع مثالي لعرض المزيد من البيانات بشكل مريح

        # Aplicar configuración RTL a todos los widgets
        apply_rtl_to_all_widgets(self)

    def populate_products_table(self):
        """ملء جدول المنتجات بالبيانات"""
        # تنظيف الجدول
        self.products_table.clearContents()
        self.products_table.setRowCount(0)

        if not self.products:
            row = self.products_table.rowCount()
            self.products_table.insertRow(row)
            empty_item = QTableWidgetItem("لا توجد منتجات مشتراة سابقًا")
            empty_item.setTextAlignment(Qt.AlignCenter)
            self.products_table.setSpan(row, 0, 1, 8) # تعديل عدد الأعمدة للتطابق مع إجمالي الأعمدة الجديد
            self.products_table.setItem(row, 0, empty_item)
            return

        # إنشاء صفوف المنتجات
        for index, product in enumerate(self.products, 1):  # بدء العد من 1
            row = self.products_table.rowCount()
            self.products_table.insertRow(row)

            # المسلسل
            serial_number = QTableWidgetItem(str(index))
            serial_number.setTextAlignment(Qt.AlignCenter)
            self.products_table.setItem(row, 0, serial_number)

            # رقم الفاتورة
            invoice_ref = QTableWidgetItem(product["reference_number"])
            invoice_ref.setTextAlignment(Qt.AlignCenter)
            invoice_ref.setData(Qt.UserRole, product["invoice_id"])  # تخزين معرف الفاتورة كبيانات مخفية
            invoice_ref.setToolTip(product["reference_number"])  # إضافة tooltip
            self.products_table.setItem(row, 1, invoice_ref)

            # اسم المنتج
            product_name = QTableWidgetItem(product["product_name"])
            product_name.setTextAlignment(Qt.AlignCenter)
            product_name.setData(Qt.UserRole, product["item_id"])  # تخزين معرف المنتج في الفاتورة
            # إضافة tooltip لعرض الاسم الكامل عند التمرير
            product_name.setToolTip(product["product_name"])
            self.products_table.setItem(row, 2, product_name)

            # كود المنتج
            product_code = QTableWidgetItem(product["product_code"])
            product_code.setTextAlignment(Qt.AlignCenter)
            product_code.setToolTip(product["product_code"])  # إضافة tooltip
            self.products_table.setItem(row, 3, product_code)

            # الكمية
            quantity = QTableWidgetItem(str(product["quantity"]))
            quantity.setTextAlignment(Qt.AlignCenter)
            quantity.setToolTip(str(product["quantity"]))  # إضافة tooltip
            self.products_table.setItem(row, 4, quantity)

            # سعر الوحدة
            unit_price = QTableWidgetItem(f"{product['unit_price']:.2f} ج.م")
            unit_price.setTextAlignment(Qt.AlignCenter)
            unit_price.setToolTip(f"{product['unit_price']:.2f} ج.م")  # إضافة tooltip
            self.products_table.setItem(row, 5, unit_price)

            # حالة الدفع - إصلاح المنطق لاستخدام حالة الفاتورة بدلاً من حالة المنتج الفردي
            invoice_status = product.get("status", "غير مدفوعة")
            is_paid = product.get("is_paid", False)
            paid_amount = product.get("paid_amount", 0)
            total_price = product.get("total_price", 0)

            # استخدام حالة الفاتورة كمرجع أساسي
            if invoice_status == "مدفوعة":
                payment_status = QTableWidgetItem("مدفوع")
                payment_status.setForeground(QColor("#22c55e"))  # أخضر
                payment_status.setToolTip("مدفوع")  # إضافة tooltip
            elif invoice_status == "مدفوع جزئياً":
                # في حالة الدفع الجزئي، نتحقق من حالة المنتج الفردي
                if is_paid or paid_amount >= total_price:
                    payment_status = QTableWidgetItem("مدفوع")
                    payment_status.setForeground(QColor("#22c55e"))  # أخضر
                    payment_status.setToolTip("مدفوع")  # إضافة tooltip
                elif paid_amount > 0:
                    payment_status = QTableWidgetItem("مدفوع جزئياً")
                    payment_status.setForeground(QColor("#f59e0b"))  # برتقالي
                    payment_status.setToolTip("مدفوع جزئياً")  # إضافة tooltip
                else:
                    payment_status = QTableWidgetItem("غير مدفوع")
                    payment_status.setForeground(QColor("#ef4444"))  # أحمر
                    payment_status.setToolTip("غير مدفوع")  # إضافة tooltip
            else:  # invoice_status == "غير مدفوعة"
                payment_status = QTableWidgetItem("غير مدفوع")
                payment_status.setForeground(QColor("#ef4444"))  # أحمر
                payment_status.setToolTip("غير مدفوع")  # إضافة tooltip

            payment_status.setTextAlignment(Qt.AlignCenter)
            self.products_table.setItem(row, 6, payment_status)

            # تاريخ الشراء - استخدام تنسيق التاريخ والوقت المحسن
            from utils.date_utils import DateTimeUtils
            formatted_date = DateTimeUtils.format_date_for_table(product["date"])
            date = QTableWidgetItem(formatted_date)
            date.setTextAlignment(Qt.AlignCenter)
            date.setToolTip(formatted_date)  # إضافة tooltip
            self.products_table.setItem(row, 7, date)

        # تعديل ارتفاع الصفوف تلقائياً لضمان ظهور النص بشكل كامل
        self.products_table.resizeRowsToContents()

    def show_product_context_menu(self, position):
        """عرض قائمة السياق للمنتجات"""
        # التحقق من وجود صف محدد
        selected_indexes = self.products_table.selectedIndexes()
        if not selected_indexes:
            return

        # الحصول على رقم الصف
        row = selected_indexes[0].row()

        # التحقق من أن الصف يحتوي على بيانات منتج
        if row >= len(self.products):
            return

        product = self.products[row]

        # التحقق من أن الفاتورة غير مدفوعة أو مدفوعة جزئياً
        invoice_status = product.get("status", "غير مدفوعة")
        is_paid = product.get("is_paid", False)
        paid_amount = product.get("paid_amount", 0)
        total_price = product.get("total_price", 0)

        # إنشاء قائمة السياق
        context_menu = QMenu(self)
        context_menu.setLayoutDirection(Qt.RightToLeft)

        # إضافة عنوان المنتج
        title_action = QAction(f"المنتج: {product['product_name']}", self)
        title_action.setEnabled(False)
        title_font = title_action.font()
        title_font.setBold(True)
        title_action.setFont(title_font)
        context_menu.addAction(title_action)

        # إضافة فاصل
        context_menu.addSeparator()

        # التحقق من صلاحية إدارة ديون العملاء للدفع
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()
        
        user_id = None
        if main_window and main_window.current_user:
            user_id = main_window.current_user.get('id')
        
        from controllers.user_controller import UserController
        has_debt_permission = user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "إدارة ديون العملاء")

        # إضافة خيار الدفع إذا كان المنتج غير مدفوع بالكامل والمستخدم لديه صلاحية
        if not is_paid and paid_amount < total_price and invoice_status in ["غير مدفوعة", "مدفوع جزئياً"]:
            remaining_amount = total_price - paid_amount
            if has_debt_permission:
                pay_action = QAction(f"💰  دفع المنتج ({remaining_amount:.2f} ج.م)", self)
                pay_action.triggered.connect(lambda: self.pay_product(product))
                context_menu.addAction(pay_action)
            else:
                # إضافة خيار معطل للإشارة إلى عدم وجود صلاحية
                pay_action = QAction(f"💰  دفع المنتج ({remaining_amount:.2f} ج.م) - غير مسموح", self)
                pay_action.setEnabled(False)
                pay_action.setToolTip("ليس لديك صلاحية إدارة ديون العملاء")
                context_menu.addAction(pay_action)

        # إضافة خيار عرض تفاصيل الفاتورة
        invoice_action = QAction("📄  عرض تفاصيل الفاتورة", self)
        invoice_action.triggered.connect(lambda: self.show_invoice_details(product["invoice_id"]))
        context_menu.addAction(invoice_action)

        # عرض القائمة في موقع النقر
        context_menu.exec_(self.products_table.mapToGlobal(position))

    def pay_product(self, product):
        """دفع منتج فردي من فاتورة"""
        # التحقق من صلاحية إدارة ديون العملاء
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()
        
        user_id = None
        if main_window and main_window.current_user:
            user_id = main_window.current_user.get('id')
        
        from controllers.user_controller import UserController
        has_debt_permission = user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "إدارة ديون العملاء")
        
        if not has_debt_permission:
            QMessageBox.warning(
                self,
                "صلاحية غير كافية",
                "ليس لديك صلاحية إدارة ديون العملاء."
            )
            return
        
        try:
            from models.invoices import InvoiceModel

            # حساب المبلغ المتبقي للمنتج
            total_price = product.get("total_price", 0)
            paid_amount = product.get("paid_amount", 0)
            remaining_amount = total_price - paid_amount

            if remaining_amount <= 0:
                show_information(self, "تم الدفع", "تم دفع هذا المنتج بالكامل مسبقاً.")
                return

            # إنشاء نافذة حوار لتأكيد الدفع
            confirm = QMessageBox.question(
                self,
                "تأكيد دفع المنتج",
                f"هل أنت متأكد من دفع المنتج '{product['product_name']}' بقيمة {remaining_amount:.2f} ج.م؟\n\n"
                f"سيتم خصم هذا المبلغ من إجمالي دين العميل وتحديث حالة الفاتورة.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if confirm == QMessageBox.Yes:
                # دفع المنتج
                item_id = product.get("item_id")
                customer_id = self.customer["id"]
                notes = f"دفع المنتج '{product['product_name']}' من الفاتورة رقم {product['reference_number']}"

                success, message = InvoiceModel.pay_invoice_item(item_id, customer_id, remaining_amount, notes)

                if success:
                    show_information(self, "تم الدفع", f"تم دفع المنتج '{product['product_name']}' بنجاح بقيمة {remaining_amount:.2f} ج.م")

                    # تحديث البيانات
                    self.refresh_data()

                    # تحديث جدول العملاء في النافذة الرئيسية
                    self.refresh_parent_customers_table()

                else:
                    QMessageBox.critical(
                        self,
                        "خطأ في الدفع",
                        f"فشل في دفع المنتج: {message}"
                    )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء دفع المنتج: {str(e)}"
            )
            print(f"[ERROR] خطأ في دفع المنتج: {str(e)}")

    def show_invoice_details(self, invoice_id):
        """عرض تفاصيل الفاتورة"""
        try:
            # البحث عن نافذة الفواتير في النافذة الرئيسية
            parent = self.parent()
            while parent:
                if hasattr(parent, 'show_invoice_details'):
                    parent.show_invoice_details(invoice_id)
                    break
                elif hasattr(parent, 'parent'):
                    parent = parent.parent()
                else:
                    show_information(
                        self,
                        "تفاصيل الفاتورة",
                        f"معرف الفاتورة: {invoice_id}\n\nيمكنك الانتقال إلى تاب الفواتير لعرض التفاصيل الكاملة."
                    )
                    break
        except Exception as e:
            print(f"[ERROR] خطأ في عرض تفاصيل الفاتورة: {str(e)}")

    def refresh_data(self):
        """تحديث بيانات النافذة"""
        try:
            # إعادة تحميل منتجات العميل
            from models.customers import CustomerModel
            raw_products = CustomerModel.get_customer_products(self.customer['id'])

            # إضافة حقل invoice_status لكل منتج للتوافق مع فلتر حالة الدفع
            self.products = []
            for product in raw_products:
                product_dict = dict(product)  # تحويل إلى قاموس عادي
                product_dict['invoice_status'] = product_dict.get('status', 'غير مدفوعة')  # إضافة حقل invoice_status
                self.products.append(product_dict)

            self.all_products = self.products.copy()  # نسخة من جميع المنتجات للرجوع إليها عند التصفية

            # إعادة تحميل قيمة الدين
            self.customer_debt = CustomerModel.get_customer_total_debt(self.customer['id'])
            self.debt_value.setText(f"{self.customer_debt:.2f} ج.م")

            # تطبيق الفلتر مرة أخرى لإظهار البيانات المحدثة
            self.apply_filter_silently()

        except Exception as e:
            print(f"[ERROR] خطأ في تحديث البيانات: {str(e)}")

    def show_payment_history(self):
        """عرض سجل المدفوعات للعميل"""
        # Llamamos al nuevo método para pagar deudas directamente
        self.pay_customer()

    def pay_customer(self):
        """دفع مبلغ لتسوية دين العميل"""
        # التحقق من صلاحية إدارة ديون العملاء
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()
        
        user_id = None
        if main_window and main_window.current_user:
            user_id = main_window.current_user.get('id')
        
        from controllers.user_controller import UserController
        has_debt_permission = user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "إدارة ديون العملاء")
        
        if not has_debt_permission:
            QMessageBox.warning(
                self,
                "صلاحية غير كافية",
                "ليس لديك صلاحية إدارة ديون العملاء."
            )
            return
        
        # الحصول على إجمالي الدين المستحق على العميل
        total_debt = self.customer_debt

        if total_debt <= 0:
            show_information(
                self,
                "لا يوجد دين",
                "لا يوجد دين مستحق على هذا العميل."
            )
            return

        # إنشاء نافذة حوار لإدخال المبلغ مع تطبيق التنسيقات
        from styles import AppStyles

        # إنشاء QInputDialog وتطبيق التنسيقات عليه
        input_dialog = QInputDialog(self)
        input_dialog.setWindowTitle("سداد دين العميل")
        input_dialog.setLabelText(f"أدخل المبلغ المراد دفعه (الحد الأقصى: {total_debt:.2f} ج.م):")
        input_dialog.setInputMode(QInputDialog.DoubleInput)
        input_dialog.setDoubleValue(min(total_debt, 100))
        input_dialog.setDoubleRange(1, total_debt)
        input_dialog.setDoubleDecimals(2)

        # تطبيق التنسيقات على النافذة
        input_dialog.setStyleSheet(AppStyles.get_input_dialog_style())

        # عرض النافذة والحصول على النتيجة
        ok = input_dialog.exec_() == QInputDialog.Accepted
        amount = input_dialog.doubleValue() if ok else 0

        if not ok or amount <= 0:
            return

        # إنشاء نافذة حوار لتأكيد الدفع
        confirm = QMessageBox.question(
            self,
            "تأكيد الدفع",
            f"هل أنت متأكد من تسجيل مبلغ {amount:.2f} ج.م كدفعة من العميل {self.customer['name']}؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if confirm == QMessageBox.Yes:
            try:
                # إنشاء جدول سجل المدفوعات إذا لم يكن موجودًا
                db.execute("""
                    CREATE TABLE IF NOT EXISTS payments (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        customer_id INTEGER NOT NULL,
                        amount REAL NOT NULL,
                        payment_date TEXT NOT NULL,
                        notes TEXT,
                        created_at TEXT,
                        updated_at TEXT,
                        FOREIGN KEY (customer_id) REFERENCES customers (id)
                    )
                """)

                # الحصول على التاريخ والوقت الحالي
                now = datetime.datetime.now()
                payment_date = now.strftime("%Y/%m/%d %H:%M:%S")

                # إضافة السجل في جدول المدفوعات
                payment_query = """
                    INSERT INTO payments (customer_id, amount, payment_date, notes, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?)
                """
                notes = f"سداد دين العميل {self.customer['name']} بقيمة {amount:.2f} ج.م"
                created_at = updated_at = payment_date
                db.execute(payment_query, (self.customer['id'], amount, payment_date, notes, created_at, updated_at))

                # استرجاع الفواتير غير المدفوعة بالكامل لهذا العميل (تشمل غير مدفوعة ومدفوع جزئياً)
                unpaid_invoices_query = """
                    SELECT id, total, paid_amount, remaining_amount
                    FROM invoices
                    WHERE customer_id = ? AND status IN ('غير مدفوعة', 'مدفوع جزئياً')
                    ORDER BY date ASC
                """
                unpaid_invoices = db.fetch_all(unpaid_invoices_query, (self.customer['id'],))

                if not unpaid_invoices:
                    show_information(self, "تنبيه", "لا توجد فواتير غير مدفوعة لتطبيق الدفعة عليها.")
                    # رغم عدم وجود فواتير، نقوم بتسجيل المدفوعات في السجل
                    db.commit()
                    show_information(
                        self,
                        "تم الدفع",
                        f"تم تسجيل دفعة بمبلغ {amount:.2f} ج.م من العميل {self.customer['name']} بنجاح وتم تسجيلها في سجل المدفوعات."
                    )
                    return

                # مبلغ الدفع المتبقي للتوزيع
                remaining_payment = amount

                # تطبيق الدفع على الفواتير بالترتيب (الأقدم أولاً)
                applied_invoices = []

                for invoice in unpaid_invoices:
                    if remaining_payment <= 0:
                        break

                    invoice_id = invoice['id']
                    invoice_remaining = invoice['remaining_amount']

                    # المبلغ الذي سيتم دفعه لهذه الفاتورة
                    payment_for_this_invoice = min(remaining_payment, invoice_remaining)

                    # تحديث الفاتورة
                    new_paid_amount = invoice['paid_amount'] + payment_for_this_invoice
                    new_remaining_amount = invoice['remaining_amount'] - payment_for_this_invoice

                    # تحديد حالة الفاتورة بناءً على المبلغ المتبقي
                    if new_remaining_amount <= 0:
                        new_status = "مدفوعة"
                    elif new_paid_amount > 0:
                        new_status = "مدفوع جزئياً"
                    else:
                        new_status = "غير مدفوعة"

                    # تحديث الفاتورة باستخدام النموذج لضمان تحديث حالة المنتجات
                    from models.invoices import InvoiceModel
                    update_data = {
                        'paid_amount': new_paid_amount,
                        'remaining_amount': new_remaining_amount,
                        'status': new_status
                    }
                    InvoiceModel.update_invoice(invoice_id, update_data)

                    # إضافة تفاصيل للملاحظات
                    applied_invoices.append({
                        "id": invoice_id,
                        "amount_paid": payment_for_this_invoice,
                        "new_status": new_status
                    })

                    # تقليل المبلغ المتبقي للدفع
                    remaining_payment -= payment_for_this_invoice

                # تحديث ملاحظات السجل بتفاصيل الفواتير المدفوعة
                if applied_invoices:
                    details = "\n".join([
                        f"- فاتورة رقم {p['id']}: {p['amount_paid']:.2f} ج.م ({p['new_status']})"
                        for p in applied_invoices
                    ])
                    updated_notes = f"{notes}\n\nتفاصيل الدفع:\n{details}"

                    # تحديث ملاحظات السجل
                    update_notes_query = """
                        UPDATE payments
                        SET notes = ?
                        WHERE customer_id = ? AND payment_date = ?
                    """
                    db.execute(update_notes_query, (updated_notes, self.customer['id'], payment_date))

                # حفظ التغييرات في قاعدة البيانات
                db.commit()

                # إعادة تحميل قيمة الدين
                self.customer_debt = CustomerModel.get_customer_total_debt(self.customer['id'])
                self.debt_value.setText(f"{self.customer_debt:.2f} ج.م")

                show_information(
                    self,
                    "تم الدفع",
                    f"تم تسجيل دفعة بمبلغ {amount:.2f} ج.م من العميل {self.customer['name']} بنجاح وتم تسجيلها في سجل المدفوعات."
                )

                # تحديث جدول العملاء في النافذة الرئيسية
                self.refresh_parent_customers_table()

            except Exception as e:
                db.rollback()
                QMessageBox.critical(
                    self,
                    "خطأ",
                    f"حدث خطأ أثناء عملية الدفع: {str(e)}"
                )
                print(f"[ERROR] خطأ في سداد دين العميل: {str(e)}")

    def refresh_parent_customers_table(self):
        """تحديث جدول العملاء في النافذة الرئيسية"""
        try:
            # البحث عن تاب العملاء في النافذة الرئيسية
            parent = self.parent()
            while parent:
                if hasattr(parent, 'refresh_customers_table'):
                    parent.refresh_customers_table()
                    break
                elif hasattr(parent, 'parent'):
                    parent = parent.parent()
                else:
                    break
        except Exception as e:
            print(f"[ERROR] خطأ في تحديث جدول العملاء: {str(e)}")



    def show_customer_payments(self):
        """عرض سجل مدفوعات العميل (من جدول payments)"""
        # التحقق من صلاحية إدارة ديون العملاء
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()
        
        user_id = None
        if main_window and main_window.current_user:
            user_id = main_window.current_user.get('id')
        
        from controllers.user_controller import UserController
        has_debt_permission = user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "إدارة ديون العملاء")
        
        if not has_debt_permission:
            QMessageBox.warning(
                self,
                "صلاحية غير كافية",
                "ليس لديك صلاحية إدارة ديون العملاء."
            )
            return
        
        try:
            # التحقق من وجود الجدول أولا
            table_exists = db.fetch_one("SELECT name FROM sqlite_master WHERE type='table' AND name='payments'")

            if not table_exists:
                show_information(
                    self,
                    "سجل المدفوعات",
                    f"لا توجد مدفوعات مسجلة للعميل {self.customer['name']} بعد."
                )
                return

            # استعلام عن سجل المدفوعات للعميل المحدد من جدول payments
            payments_query = """
                SELECT p.id, p.amount, p.payment_date, p.notes, c.name as customer_name
                FROM payments p
                LEFT JOIN customers c ON p.customer_id = c.id
                WHERE p.customer_id = ?
                ORDER BY p.payment_date DESC
            """
            payments = db.fetch_all(payments_query, (self.customer['id'],))

            if not payments:
                show_information(
                    self,
                    "سجل المدفوعات",
                    f"لا توجد مدفوعات مسجلة للعميل {self.customer['name']} بعد."
                )
                return

            # إنشاء نافذة حوار لعرض سجل المدفوعات
            dialog = QDialog(self)
            dialog.setWindowTitle(f"سجل مدفوعات العميل: {self.customer['name']}")
            dialog.setMinimumSize(600, 400)
            dialog.setLayoutDirection(Qt.RightToLeft)

            # التخطيط الرئيسي
            layout = QVBoxLayout(dialog)

            # عنوان الصفحة
            title_layout = QHBoxLayout()
            page_title = QLabel(f"سجل مدفوعات العميل: {self.customer['name']}")
            page_title.setObjectName("page_title")
            page_title.setFont(QFont("Arial", 14, QFont.Bold))
            title_layout.addWidget(page_title)
            title_layout.addStretch()

            layout.addLayout(title_layout)

            # فاصل
            separator = QFrame()
            separator.setFrameShape(QFrame.HLine)
            separator.setFrameShadow(QFrame.Sunken)
            separator.setObjectName("content_separator")
            layout.addWidget(separator)
            layout.addSpacing(10)

            # جدول المدفوعات
            payments_table = QTableWidget()
            payments_table.setColumnCount(5)
            payments_table.setHorizontalHeaderLabels(["#", "المبلغ", "تاريخ الدفع", "الملاحظات", "العميل"])

            # إضافة قائمة السياق للجدول
            payments_table.setContextMenuPolicy(Qt.CustomContextMenu)
            payments_table.customContextMenuRequested.connect(lambda pos: self.show_payment_context_menu(pos, payments_table, payments))

            # ضبط عرض الأعمدة
            header = payments_table.horizontalHeader()
            header.setSectionResizeMode(QHeaderView.Stretch)
            header.setSectionResizeMode(0, QHeaderView.ResizeToContents)  # رقم العملية
            header.setSectionResizeMode(2, QHeaderView.ResizeToContents)  # تاريخ الدفع
            header.setSectionResizeMode(4, QHeaderView.ResizeToContents)  # اسم العميل

            payments_table.verticalHeader().setVisible(False)
            payments_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
            payments_table.setSelectionBehavior(QAbstractItemView.SelectRows)
            payments_table.setSelectionMode(QAbstractItemView.SingleSelection)
            payments_table.setAlternatingRowColors(False)

            # ملء البيانات
            for row, payment in enumerate(payments):
                payments_table.insertRow(row)

                # رقم العملية
                id_item = QTableWidgetItem(str(payment['id']))
                id_item.setTextAlignment(Qt.AlignCenter)
                id_item.setToolTip(str(payment['id']))  # إضافة tooltip
                payments_table.setItem(row, 0, id_item)

                # المبلغ
                amount_item = QTableWidgetItem(f"{payment['amount']:.2f} ج.م")
                amount_item.setTextAlignment(Qt.AlignCenter)
                amount_item.setToolTip(f"{payment['amount']:.2f} ج.م")  # إضافة tooltip
                payments_table.setItem(row, 1, amount_item)

                # تاريخ الدفع
                date_item = QTableWidgetItem(payment['payment_date'])
                date_item.setTextAlignment(Qt.AlignCenter)
                date_item.setToolTip(payment['payment_date'])  # إضافة tooltip
                payments_table.setItem(row, 2, date_item)

                # الملاحظات
                notes = payment['notes'] if payment['notes'] else ""
                if len(notes) > 50:
                    notes = notes[:50] + "..."
                notes_item = QTableWidgetItem(notes)
                notes_item.setToolTip(payment['notes'] if payment['notes'] else "")  # إضافة tooltip للنص الكامل
                payments_table.setItem(row, 3, notes_item)

                # اسم العميل
                customer_name = payment['customer_name'] if payment['customer_name'] else f"عميل #{self.customer['id']}"
                customer_item = QTableWidgetItem(customer_name)
                customer_item.setToolTip(customer_name)  # إضافة tooltip
                payments_table.setItem(row, 4, customer_item)

            layout.addWidget(payments_table)

            # إضافة إحصائيات
            stats_layout = QHBoxLayout()

            # عدد المدفوعات
            payments_count = len(payments)
            payments_count_label = QLabel(f"عدد المدفوعات: {payments_count}")
            payments_count_label.setObjectName("stats_label")
            stats_layout.addWidget(payments_count_label)

            stats_layout.addStretch()

            # إجمالي المدفوعات
            total_payments = sum(payment['amount'] for payment in payments)
            total_payments_label = QLabel(f"إجمالي المدفوعات: {total_payments:.2f} ج.م")
            total_payments_label.setObjectName("stats_label")
            stats_layout.addWidget(total_payments_label)

            layout.addLayout(stats_layout)
            layout.addSpacing(10)

            # أزرار أسفل النافذة
            buttons_layout = QHBoxLayout()
            buttons_layout.addStretch()

            # زر الإغلاق
            close_button = QPushButton("إغلاق")
            close_button.setFixedSize(120, 40)
            close_button.setObjectName("action_button")
            close_button.setStyleSheet("""
                QPushButton#action_button {
                    background-color: #dc2626;
                    color: white;
                    border: 1px solid #f87171;
                    border-radius: 4px;
                    padding: 8px;
                    font-weight: bold;
                }
                QPushButton#action_button:hover {
                    background-color: #b91c1c;
                }
            """)
            close_button.clicked.connect(dialog.close)

            buttons_layout.addWidget(close_button)
            layout.addLayout(buttons_layout)

            # عرض النافذة
            dialog.exec_()

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء فتح سجل المدفوعات: {str(e)}"
            )
            print(f"[ERROR] خطأ في فتح سجل المدفوعات: {str(e)}")

    def show_payment_context_menu(self, position, table, payments):
        """عرض قائمة السياق لصف في جدول المدفوعات"""
        # التحقق من وجود صف محدد
        selected_indexes = table.selectedIndexes()
        if not selected_indexes:
            return

        # الحصول على رقم الصف والمعرف
        row = selected_indexes[0].row()
        payment_id = int(table.item(row, 0).text())
        amount = float(table.item(row, 1).text().split()[0])

        # التحقق من صلاحية إدارة ديون العملاء للحذف
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()
        
        user_id = None
        if main_window and main_window.current_user:
            user_id = main_window.current_user.get('id')
        
        from controllers.user_controller import UserController
        has_debt_permission = user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "إدارة ديون العملاء")

        # إنشاء قائمة السياق
        context_menu = QMenu(self)
        context_menu.setLayoutDirection(Qt.RightToLeft)

        # إضافة العنوان
        title_action = QAction(f"الدفعة رقم: {payment_id}", self)
        title_action.setEnabled(False)
        title_font = title_action.font()
        title_font.setBold(True)
        title_action.setFont(title_font)
        context_menu.addAction(title_action)

        # إضافة فاصل
        context_menu.addSeparator()

        # إضافة خيار الحذف - يتطلب صلاحية إدارة ديون العملاء
        if has_debt_permission:
            delete_action = QAction("🗑️  حذف الدفعة", self)
            delete_action.triggered.connect(lambda: self.delete_payment(payment_id, amount, table, payments))
            context_menu.addAction(delete_action)
        else:
            # إضافة خيار معطل للإشارة إلى عدم وجود صلاحية
            delete_action = QAction("🗑️  حذف الدفعة (غير مسموح)", self)
            delete_action.setEnabled(False)
            delete_action.setToolTip("ليس لديك صلاحية إدارة ديون العملاء")
            context_menu.addAction(delete_action)

        # عرض القائمة في موقع النقر
        context_menu.exec_(table.mapToGlobal(position))

    def delete_payment(self, payment_id, amount, table, payments):
        """حذف دفعة وإعادة المبلغ إلى ديون العميل"""
        # التحقق من صلاحية إدارة ديون العملاء
        main_window = None
        parent = self.parent()
        while parent:
            if hasattr(parent, 'current_user'):
                main_window = parent
                break
            parent = parent.parent()
        
        user_id = None
        if main_window and main_window.current_user:
            user_id = main_window.current_user.get('id')
        
        from controllers.user_controller import UserController
        has_debt_permission = user_id is None or main_window.current_user.get('username') == 'admin' or UserController.check_permission(user_id, "إدارة ديون العملاء")
        
        if not has_debt_permission:
            QMessageBox.warning(
                self,
                "صلاحية غير كافية",
                "ليس لديك صلاحية إدارة ديون العملاء."
            )
            return
        
        # استفسار للتأكيد
        confirm = QMessageBox.question(
            self,
            "تأكيد الحذف",
            f"هل أنت متأكد من حذف الدفعة رقم {payment_id} بقيمة {amount:.2f} ج.م؟\nسيتم إعادة هذا المبلغ إلى إجمالي الدين على العميل.",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if confirm != QMessageBox.Yes:
            return

        try:
            # البحث عن تفاصيل الدفعة في قاعدة البيانات
            payment_info_query = """
                SELECT p.id, p.customer_id, p.amount, p.payment_date, p.notes
                FROM payments p
                WHERE p.id = ?
            """
            payment_info = db.fetch_one(payment_info_query, (payment_id,))

            if not payment_info:
                QMessageBox.warning(self, "تحذير", "لم يتم العثور على معلومات الدفعة!")
                return

            # استرجاع الفواتير المعدلة من ملاحظات الدفعة (إذا كانت موجودة)
            notes = payment_info['notes']
            invoice_details = []

            if notes and "تفاصيل الدفع:" in notes:
                try:
                    # استخراج معرفات الفواتير وقيم الدفع
                    lines = notes.split("\n")
                    details_start = False

                    for line in lines:
                        if "تفاصيل الدفع:" in line:
                            details_start = True
                            continue

                        if details_start and line.strip().startswith("- فاتورة رقم"):
                            # استخراج معرف الفاتورة والمبلغ المدفوع
                            parts = line.strip().split(":")
                            if len(parts) >= 2:
                                invoice_id_part = parts[0].strip().replace("- فاتورة رقم", "").strip()
                                try:
                                    invoice_id = int(invoice_id_part)
                                    amount_part = parts[1].strip().split()[0]
                                    amount_paid = float(amount_part)

                                    invoice_details.append({
                                        "invoice_id": invoice_id,
                                        "amount_paid": amount_paid
                                    })
                                except (ValueError, IndexError):
                                    continue
                except:
                    # إذا فشل تحليل الملاحظات، نتجاهل ونستمر
                    pass

            # إعادة تعديل حالة الفواتير التي تم دفعها
            if invoice_details:
                for detail in invoice_details:
                    invoice_id = detail["invoice_id"]
                    amount_paid = detail["amount_paid"]

                    # الحصول على حالة الفاتورة الحالية
                    invoice_query = """
                        SELECT id, paid_amount, remaining_amount, status
                        FROM invoices
                        WHERE id = ?
                    """
                    invoice = db.fetch_one(invoice_query, (invoice_id,))

                    if invoice:
                        # عكس تأثير الدفع
                        new_paid_amount = max(0, invoice['paid_amount'] - amount_paid)
                        new_remaining_amount = invoice['remaining_amount'] + amount_paid
                        new_status = "غير مدفوعة"  # إعادة الحالة إلى غير مدفوعة لأننا قمنا بإلغاء الدفع

                        # تحديث الفاتورة باستخدام النموذج لضمان تحديث حالة المنتجات
                        from models.invoices import InvoiceModel
                        update_data = {
                            'paid_amount': new_paid_amount,
                            'remaining_amount': new_remaining_amount,
                            'status': new_status
                        }
                        InvoiceModel.update_invoice(invoice_id, update_data)

            # حذف سجل الدفعة
            delete_query = "DELETE FROM payments WHERE id = ?"
            db.execute(delete_query, (payment_id,))

            # حفظ التغييرات
            db.commit()

            # تحديث واجهة المستخدم
            # 1. تحديث قيمة الدين في واجهة تفاصيل العميل
            self.customer_debt = CustomerModel.get_customer_total_debt(self.customer['id'])
            self.debt_value.setText(f"{self.customer_debt:.2f} ج.م")

            # 2. تحديث جدول المدفوعات
            # البحث عن الدفعة في القائمة المحلية وحذفها
            for i, p in enumerate(payments):
                if p['id'] == payment_id:
                    payments.pop(i)
                    break

            # إعادة ملء الجدول
            table.clearContents()
            table.setRowCount(0)

            for row, payment in enumerate(payments):
                table.insertRow(row)

                # رقم العملية
                id_item = QTableWidgetItem(str(payment['id']))
                id_item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row, 0, id_item)

                # المبلغ
                amount_item = QTableWidgetItem(f"{payment['amount']:.2f} ج.م")
                amount_item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row, 1, amount_item)

                # تاريخ الدفع
                date_item = QTableWidgetItem(payment['payment_date'])
                date_item.setTextAlignment(Qt.AlignCenter)
                table.setItem(row, 2, date_item)

                # الملاحظات
                notes = payment['notes'] if payment['notes'] else ""
                if len(notes) > 50:
                    notes = notes[:50] + "..."
                notes_item = QTableWidgetItem(notes)
                table.setItem(row, 3, notes_item)

                # اسم العميل
                customer_name = payment['customer_name'] if payment['customer_name'] else f"عميل #{payment['customer_id']}"
                customer_item = QTableWidgetItem(customer_name)
                table.setItem(row, 4, customer_item)

            # 3. تحديث إحصائيات المدفوعات في نافذة سجل المدفوعات
            # نظرًا لتعقيدات الوصول إلى عناصر واجهة المستخدم الخاصة بالنافذة المنبثقة،
            # نعرض بدلاً من ذلك رسالة نجاح ونقترح على المستخدم إعادة فتح النافذة

            show_information(
                self,
                "تم بنجاح",
                f"تم حذف الدفعة بنجاح وإعادة المبلغ {amount:.2f} ج.م إلى دين العميل.\n"
                f"تم تحديث إجمالي الدين: {self.customer_debt:.2f} ج.م"
            )

        except Exception as e:
            db.rollback()
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء محاولة حذف الدفعة: {str(e)}"
            )
            print(f"[ERROR] خطأ في حذف دفعة العميل: {str(e)}")

    def refresh_customer_data(self):
        """تحديث بيانات العميل في النافذة"""
        try:
            # تحديث دين العميل
            self.customer_debt = CustomerModel.get_customer_total_debt(self.customer['id'])
            self.debt_value.setText(f"{self.customer_debt:.2f} ج.م")

            # تحديث لون النص حسب حالة الدين
            if self.customer_debt > 0:
                self.debt_value.setStyleSheet("color: #e74c3c; font-weight: bold;")  # أحمر للديون
            else:
                self.debt_value.setStyleSheet("color: #27ae60; font-weight: bold;")  # أخضر لعدم وجود ديون

        except Exception as e:
            print(f"[ERROR] خطأ في تحديث بيانات العميل: {str(e)}")

    def apply_styles(self):
        """تطبيق الأنماط على النافذة"""
        self.setStyleSheet(AppStyles.get_all_view_styles())

        # ستايل إضافي للأزرار المنبثقة
        additional_styles = """
            QPushButton#primary_button {
                background-color: #0078d7;
                color: white;
                border: 1px solid #0078d7;
                border-radius: 2px;
                padding: 5px 12px;
                font-weight: bold;
            }
            QPushButton#primary_button:hover {
                background-color: #1a86d9;
                border: 1px solid #1a86d9;
            }
            QPushButton#primary_button:pressed {
                background-color: #006cc1;
                border: 1px solid #006cc1;
            }
            QPushButton#secondary_button {
                background-color: #e1e1e1;
                color: #000000;
                border: 1px solid #adadad;
                border-radius: 2px;
                padding: 5px 12px;
            }
            QPushButton#secondary_button:hover {
                background-color: #e5f1fb;
                border: 1px solid #0078d7;
            }
            QPushButton#secondary_button:pressed {
                background-color: #cce4f7;
                border: 1px solid #0078d7;
            }
            #field_label {
                color: #3b82f6;
                font-weight: bold;
            }
            #section_title {
                color: #1e3a8a;
                font-weight: bold;
            }
            #filter_container {
                padding: 0px;
                margin: 0px;
                background-color: transparent;
            }
            #action_button {
                margin-top: 0px;
                padding-top: 0px;
                padding-bottom: 0px;
                margin-bottom: 0px;
            }
            QTableWidget {
                border: 1px solid #e2e8f0;
                border-radius: 6px;
                background-color: white;
            }
            QTableWidget::item:selected {
                background-color: rgba(59, 130, 246, 0.3);
                color: #0f172a;
            }
        """
        self.setStyleSheet(self.styleSheet() + additional_styles)

    def apply_filter_silently(self):
        """تطبيق التصفية بدون عرض رسائل"""
        self.selected_month = self.month_combo.currentData()
        self.selected_year = self.year_combo.currentData()
        show_paid = self.show_paid_checkbox.isChecked()

        # إعادة ضبط البيانات ثم تطبيق التصفية
        self.products = self.all_products.copy()

        # تصفية حسب حالة الدفع أولاً
        if not show_paid:
            # عرض المنتجات غير المدفوعة فقط (الافتراضي)
            # استخدام حالة الفاتورة كمرجع أساسي مع التحقق من حالة المنتج الفردي في حالة الدفع الجزئي
            filtered_products = []
            for product in self.products:
                invoice_status = product.get("status", "غير مدفوعة")
                is_paid = product.get("is_paid", False)
                paid_amount = product.get("paid_amount", 0)
                total_price = product.get("total_price", 0)

                # تحديد ما إذا كان المنتج غير مدفوع
                is_product_unpaid = False

                if invoice_status == "غير مدفوعة":
                    is_product_unpaid = True
                elif invoice_status == "مدفوع جزئياً":
                    # في حالة الدفع الجزئي، نتحقق من حالة المنتج الفردي
                    if not is_paid and paid_amount < total_price:
                        is_product_unpaid = True
                # إذا كانت الفاتورة مدفوعة، فالمنتج مدفوع

                if is_product_unpaid:
                    filtered_products.append(product)

            self.products = filtered_products
        # إذا كان المربع معلم، نعرض جميع المنتجات (مدفوعة وغير مدفوعة)

        # تصفية حسب الشهر والسنة إذا تم تحديدهما
        if self.selected_month or self.selected_year:
            filtered_products = []
            for product in self.products:
                try:
                    # تحليل التاريخ من صيغة "YYYY/MM/DD"
                    date_parts = product["date"].split("/")
                    product_year = int(date_parts[0])
                    product_month = int(date_parts[1])

                    # تطبيق تصفية السنة
                    if self.selected_year and product_year != self.selected_year:
                        continue

                    # تطبيق تصفية الشهر
                    if self.selected_month and product_month != self.selected_month:
                        continue

                    # إذا وصلنا هنا، فإن المنتج يطابق معايير التصفية
                    filtered_products.append(product)
                except (ValueError, IndexError):
                    # تخطي المنتجات بتواريخ غير صالحة
                    continue

            self.products = filtered_products

        # تحديث الجدول بدون إظهار رسائل
        self.populate_products_table()

    def apply_filter(self):
        """تطبيق تصفية الشهر والسنة وحالة الدفع على المنتجات المشتراة"""
        self.selected_month = self.month_combo.currentData()
        self.selected_year = self.year_combo.currentData()
        show_paid = self.show_paid_checkbox.isChecked()

        # إعادة ضبط البيانات ثم تطبيق التصفية
        self.products = self.all_products.copy()

        # تصفية حسب حالة الدفع أولاً
        if not show_paid:
            # عرض المنتجات غير المدفوعة فقط (الافتراضي)
            # استخدام حالة الفاتورة كمرجع أساسي مع التحقق من حالة المنتج الفردي في حالة الدفع الجزئي
            filtered_products = []
            for product in self.products:
                invoice_status = product.get("status", "غير مدفوعة")
                is_paid = product.get("is_paid", False)
                paid_amount = product.get("paid_amount", 0)
                total_price = product.get("total_price", 0)

                # تحديد ما إذا كان المنتج غير مدفوع
                is_product_unpaid = False

                if invoice_status == "غير مدفوعة":
                    is_product_unpaid = True
                elif invoice_status == "مدفوع جزئياً":
                    # في حالة الدفع الجزئي، نتحقق من حالة المنتج الفردي
                    if not is_paid and paid_amount < total_price:
                        is_product_unpaid = True
                # إذا كانت الفاتورة مدفوعة، فالمنتج مدفوع

                if is_product_unpaid:
                    filtered_products.append(product)

            self.products = filtered_products
        # إذا كان المربع معلم، نعرض جميع المنتجات (مدفوعة وغير مدفوعة)

        # تصفية حسب الشهر والسنة إذا تم تحديدهما
        if self.selected_month or self.selected_year:
            filtered_products = []
            for product in self.products:
                try:
                    # تحليل التاريخ من صيغة "YYYY/MM/DD"
                    date_parts = product["date"].split("/")
                    product_year = int(date_parts[0])
                    product_month = int(date_parts[1])

                    # تطبيق تصفية السنة
                    if self.selected_year and product_year != self.selected_year:
                        continue

                    # تطبيق تصفية الشهر
                    if self.selected_month and product_month != self.selected_month:
                        continue

                    # إذا وصلنا هنا، فإن المنتج يطابق معايير التصفية
                    filtered_products.append(product)
                except (ValueError, IndexError):
                    # تخطي المنتجات بتواريخ غير صالحة
                    continue

            self.products = filtered_products

        # تحديث الجدول
        self.populate_products_table()

        # عرض رسالة بمعلومات التصفية
        filter_info = ""
        if self.selected_month:
            month_name = self.month_combo.currentText()
            filter_info += f"الشهر: {month_name}"

        if self.selected_year:
            if filter_info:
                filter_info += " - "
            filter_info += f"السنة: {self.selected_year}"

        # إضافة معلومات حالة الدفع
        payment_status = "جميع المنتجات" if show_paid else "المنتجات غير المدفوعة فقط"
        if filter_info:
            filter_info += f" - {payment_status}"
        else:
            filter_info = payment_status

        if len(self.products) > 0:
            show_information(
                self,
                "نتائج التصفية",
                f"تم عرض {filter_info} ({len(self.products)} منتج)"
            )
        else:
            show_information(
                self,
                "نتائج التصفية",
                f"لا توجد منتجات تطابق معايير التصفية: {filter_info}"
            )

    def reset_filter(self):
        """إعادة ضبط تصفية المنتجات إلى الوضع الافتراضي"""
        self.selected_month = None
        self.selected_year = None
        self.month_combo.setCurrentIndex(0)
        self.year_combo.setCurrentIndex(0)
        self.products = self.all_products.copy()
        self.populate_products_table()

    def toggle_total_purchases_visibility(self, event):
        if not hasattr(self, '_total_purchases_hidden'):
            self._total_purchases_hidden = False
        if self._total_purchases_hidden:
            self.total_purchases_value.setText(f"{self.customer['total_purchases']:.2f} ج.م")
            self._total_purchases_hidden = False
        else:
            self.total_purchases_value.setText("*****")
            self._total_purchases_hidden = True

    def print_selected_purchases(self):
        """طباعة المشتريات المعروضة في الجدول"""
        try:
            # طباعة جميع المشتريات المعروضة في الجدول
            items_to_print = self.products

            if not items_to_print:
                QMessageBox.warning(
                    self,
                    "تنبيه",
                    "لا توجد مشتريات للطباعة!"
                )
                return

            # تجهيز بيانات الطباعة
            from PyQt5.QtCore import QSettings, QSizeF
            from PyQt5.QtPrintSupport import QPrinter
            settings = QSettings("MyCompany", "SmartManager")
            
            company_info = {
                'name': settings.value("company_name", "اسم الشركة"),
                'phone': settings.value("company_phone", "رقم الهاتف"),
                'address': settings.value("company_address", "عنوان الشركة"),
                'notes': settings.value("invoice_notes", "شكراً لتعاملكم معنا")
            }

            # تجهيز بيانات الفاتورة
            invoice_data = {
                'reference_number': f"CUST-{self.customer['id']}-{datetime.datetime.now().strftime('%Y%m%d%H%M%S')}",
                'date': datetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S"),
                'customer_name': self.customer['name'],
                'total': sum(item['total_price'] for item in items_to_print)
            }

            # إنشاء كائن الطباعة الحرارية
            from utils.thermal_printer_helper import ESCPOSInvoicePrinter
            printer = ESCPOSInvoicePrinter()

            # الحصول على اسم الطابعة من الإعدادات
            printer_name = settings.value("thermal_printer", "الطابعة الافتراضية")

            # حساب ارتفاع الورقة المطلوب بناءً على عدد العناصر
            # نحسب الارتفاع الأساسي للرأس والتذييل
            base_height = 220  # زيادة ارتفاع الرأس والتذييل ليتناسب مع الهوامش الجديدة
            item_height = 25   # زيادة ارتفاع كل عنصر ليتناسب مع المسافات الجديدة
            
            # حساب الارتفاع الكلي المطلوب
            total_height = base_height + (len(items_to_print) * item_height)
            
            # إضافة مساحة إضافية للهوامش والتباعد
            total_height += 50

            # قراءة عرض الورقة من الإعدادات
            paper_width = settings.value("invoice_design/paper_width", 58, type=int)

            # إنشاء طابعة مخصصة بالأبعاد المحسوبة
            custom_printer = QPrinter(QPrinter.HighResolution)
            custom_printer.setOutputFormat(QPrinter.NativeFormat)
            custom_printer.setPageSize(QPrinter.Custom)
            custom_printer.setPaperSize(QSizeF(paper_width, total_height), QPrinter.Millimeter)
            
            if printer_name and printer_name != "الطابعة الافتراضية":
                custom_printer.setPrinterName(printer_name)

            # طباعة الفاتورة
            success = printer.print_invoice_with_enhanced_visual_design(
                invoice_data, 
                items_to_print, 
                company_info, 
                printer_name,
                custom_printer  # تمرير الطابعة المخصصة
            )

            if success:
                QMessageBox.information(
                    self,
                    "نجاح",
                    "تمت طباعة المشتريات بنجاح!"
                )
            else:
                QMessageBox.warning(
                    self,
                    "تحذير",
                    "حدث خطأ أثناء الطباعة. يرجى التحقق من إعدادات الطابعة."
                )

        except Exception as e:
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء الطباعة: {str(e)}"
            )
            print(f"[ERROR] خطأ في طباعة المشتريات: {str(e)}")