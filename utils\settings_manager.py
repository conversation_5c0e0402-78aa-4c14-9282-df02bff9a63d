#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Settings Manager - مدير الإعدادات المباشر
يوفر نظام إدارة إعدادات مركزي مع إشعارات فورية للمكونات المتأثرة
"""

import os
import sys
from typing import Any, Dict, List, Callable, Optional
from datetime import datetime
from PyQt5.QtCore import QObject, pyqtSignal, QSettings
from PyQt5.QtWidgets import QMessageBox


class SettingsManager(QObject):
    """
    مدير الإعدادات المركزي
    يدير جميع إعدادات التطبيق ويرسل إشعارات عند تغييرها
    """
    
    # إشارات للإعدادات المختلفة
    company_info_changed = pyqtSignal(dict)  # تغيير معلومات الشركة
    currency_changed = pyqtSignal(str)  # تغيير العملة
    printer_settings_changed = pyqtSignal(dict)  # تغيير إعدادات الطباعة
    invoice_settings_changed = pyqtSignal(dict)  # تغيير إعدادات الفواتير
    backup_settings_changed = pyqtSignal(dict)  # تغيير إعدادات النسخ الاحتياطي
    theme_changed = pyqtSignal(str)  # تغيير الثيم
    language_changed = pyqtSignal(str)  # تغيير اللغة
    ui_settings_changed = pyqtSignal(dict)  # تغيير إعدادات الواجهة
    
    def __init__(self):
        super().__init__()
        
        # إنشاء كائن QSettings
        self.settings = QSettings("MyCompany", "SmartManager")
        
        # قاموس لتخزين المكونات المسجلة
        self.registered_components = {
            'company': [],
            'currency': [],
            'printer': [],
            'invoice': [],
            'backup': [],
            'theme': [],
            'language': [],
            'ui': []
        }
        
        # مخطط التحقق من صحة الإعدادات
        self.validation_schema = {
            'company': {
                'name': {'type': str, 'required': True, 'min_length': 1},
                'address': {'type': str, 'required': False},
                'phone': {'type': str, 'required': False},
                'logo_path': {'type': str, 'required': False}
            },
            'currency': {
                'symbol': {'type': str, 'required': True, 'min_length': 1},
                'code': {'type': str, 'required': False},
                'decimal_places': {'type': int, 'required': False, 'min': 0, 'max': 4}
            },
            'printer': {
                'default_printer': {'type': str, 'required': False},
                'auto_print_invoice': {'type': bool, 'required': False},
                'paper_size': {'type': str, 'required': False},
                'paper_width': {'type': int, 'required': False, 'valid_values': [58, 80, 210]}
            },
            'invoice': {
                'notes': {'type': str, 'required': False},
                'auto_number': {'type': bool, 'required': False},
                'template': {'type': str, 'required': False}
            },
            'backup': {
                'auto_backup': {'type': bool, 'required': False},
                'interval_days': {'type': int, 'required': False, 'min': 1, 'max': 365},
                'location': {'type': str, 'required': False}
            },
            'ui': {
                'theme': {'type': str, 'required': False},
                'language': {'type': str, 'required': False},
                'font_size': {'type': int, 'required': False, 'min': 8, 'max': 24}
            }
        }
        
        # الإعدادات الافتراضية
        self.default_settings = {
            'company': {
                'name': 'شركتي',
                'address': 'عنوان الشركة',
                'phone': '01xxxxxxxxx',
                'logo_path': ''
            },
            'currency': {
                'symbol': 'ج.م',
                'code': 'EGP',
                'decimal_places': 2
            },
            'printer': {
                'default_printer': 'الطابعة الافتراضية',
                'auto_print_invoice': True,
                'paper_size': 'A4',
                'paper_width': 80
            },
            'invoice': {
                'notes': 'شكراً لتعاملكم معنا',
                'auto_number': True,
                'template': 'default'
            },
            'backup': {
                'auto_backup': True,
                'interval_days': 7,
                'location': 'backups/'
            },
            'ui': {
                'theme': 'فاتح',
                'language': 'العربية',
                'font_size': 11
            }
        }
    
    def get_setting(self, category: str, key: str, default: Any = None) -> Any:
        """
        الحصول على قيمة إعداد معين
        
        Args:
            category: فئة الإعداد (company, currency, إلخ)
            key: مفتاح الإعداد
            default: القيمة الافتراضية إذا لم يوجد الإعداد
            
        Returns:
            قيمة الإعداد أو القيمة الافتراضية
        """
        try:
            # تحديد القيمة الافتراضية من المخطط إذا لم تُمرر
            if default is None and category in self.default_settings:
                if key in self.default_settings[category]:
                    default = self.default_settings[category][key]
            
            # الحصول على القيمة من QSettings
            self.settings.beginGroup(category.title())
            value = self.settings.value(key, default)
            self.settings.endGroup()
            
            return value
            
        except Exception as e:
            print(f"خطأ في الحصول على الإعداد {category}.{key}: {str(e)}")
            return default
    
    def set_setting(self, category: str, key: str, value: Any, apply_immediately: bool = True) -> bool:
        """
        تعيين قيمة إعداد معين
        
        Args:
            category: فئة الإعداد
            key: مفتاح الإعداد
            value: القيمة الجديدة
            apply_immediately: تطبيق التغيير فوراً
            
        Returns:
            True إذا تم الحفظ بنجاح، False في حالة الخطأ
        """
        try:
            # التحقق من صحة البيانات
            if not self._validate_setting(category, key, value):
                return False
            
            # الحصول على القيمة القديمة
            old_value = self.get_setting(category, key)
            
            # حفظ القيمة الجديدة
            self.settings.beginGroup(category.title())
            self.settings.setValue(key, value)
            self.settings.endGroup()
            
            # تطبيق التغيير فوراً إذا طُلب ذلك
            if apply_immediately:
                self._notify_category_change(category, {key: value}, {key: old_value})
            
            return True
            
        except Exception as e:
            print(f"خطأ في حفظ الإعداد {category}.{key}: {str(e)}")
            return False
    
    def apply_settings_category(self, category: str, settings_dict: Dict[str, Any]) -> bool:
        """
        تطبيق مجموعة من الإعدادات لفئة معينة
        
        Args:
            category: فئة الإعدادات
            settings_dict: قاموس الإعدادات الجديدة
            
        Returns:
            True إذا تم التطبيق بنجاح
        """
        try:
            old_values = {}
            
            # حفظ القيم القديمة
            for key in settings_dict.keys():
                old_values[key] = self.get_setting(category, key)
            
            # حفظ القيم الجديدة
            self.settings.beginGroup(category.title())
            for key, value in settings_dict.items():
                if self._validate_setting(category, key, value):
                    self.settings.setValue(key, value)
                else:
                    print(f"تجاهل إعداد غير صحيح: {category}.{key} = {value}")
            self.settings.endGroup()
            
            # إرسال إشعار بالتغيير
            self._notify_category_change(category, settings_dict, old_values)
            
            return True
            
        except Exception as e:
            print(f"خطأ في تطبيق إعدادات الفئة {category}: {str(e)}")
            return False
    
    def register_component(self, component: QObject, settings_categories: List[str], 
                          callback: Callable = None) -> bool:
        """
        تسجيل مكون للاستماع لتغييرات إعدادات معينة
        
        Args:
            component: المكون المراد تسجيله
            settings_categories: قائمة فئات الإعدادات المراد الاستماع لها
            callback: دالة callback اختيارية (إذا لم تُمرر، سيتم البحث عن دوال بأسماء محددة)
            
        Returns:
            True إذا تم التسجيل بنجاح
        """
        try:
            for category in settings_categories:
                if category in self.registered_components:
                    # تجنب التسجيل المكرر
                    component_info = {
                        'component': component,
                        'callback': callback
                    }
                    
                    # التحقق من عدم وجود المكون مسبقاً
                    existing = [c for c in self.registered_components[category] 
                              if c['component'] == component]
                    
                    if not existing:
                        self.registered_components[category].append(component_info)
                        print(f"تم تسجيل مكون للاستماع لتغييرات {category}")
                    
            return True
            
        except Exception as e:
            print(f"خطأ في تسجيل المكون: {str(e)}")
            return False
    
    def unregister_component(self, component: QObject, settings_categories: List[str] = None) -> bool:
        """
        إلغاء تسجيل مكون
        
        Args:
            component: المكون المراد إلغاء تسجيله
            settings_categories: فئات الإعدادات (إذا لم تُمرر، سيتم إلغاء التسجيل من جميع الفئات)
            
        Returns:
            True إذا تم إلغاء التسجيل بنجاح
        """
        try:
            categories_to_process = settings_categories or list(self.registered_components.keys())
            
            for category in categories_to_process:
                if category in self.registered_components:
                    # إزالة المكون من القائمة
                    self.registered_components[category] = [
                        c for c in self.registered_components[category] 
                        if c['component'] != component
                    ]
                    
            return True
            
        except Exception as e:
            print(f"خطأ في إلغاء تسجيل المكون: {str(e)}")
            return False
    
    def _validate_setting(self, category: str, key: str, value: Any) -> bool:
        """
        التحقق من صحة إعداد معين
        
        Args:
            category: فئة الإعداد
            key: مفتاح الإعداد
            value: القيمة المراد التحقق منها
            
        Returns:
            True إذا كانت القيمة صحيحة
        """
        try:
            if category not in self.validation_schema:
                return True  # لا توجد قواعد تحقق لهذه الفئة
            
            if key not in self.validation_schema[category]:
                return True  # لا توجد قواعد تحقق لهذا المفتاح
            
            rules = self.validation_schema[category][key]
            
            # التحقق من النوع
            if 'type' in rules and not isinstance(value, rules['type']):
                print(f"نوع البيانات غير صحيح لـ {category}.{key}: متوقع {rules['type']}, وُجد {type(value)}")
                return False
            
            # التحقق من الطول الأدنى للنصوص
            if 'min_length' in rules and isinstance(value, str):
                if len(value) < rules['min_length']:
                    print(f"النص قصير جداً لـ {category}.{key}: الحد الأدنى {rules['min_length']}")
                    return False
            
            # التحقق من القيم الرقمية
            if isinstance(value, (int, float)):
                if 'min' in rules and value < rules['min']:
                    print(f"القيمة صغيرة جداً لـ {category}.{key}: الحد الأدنى {rules['min']}")
                    return False
                if 'max' in rules and value > rules['max']:
                    print(f"القيمة كبيرة جداً لـ {category}.{key}: الحد الأقصى {rules['max']}")
                    return False
            
            # التحقق من القيم المسموحة
            if 'valid_values' in rules:
                if value not in rules['valid_values']:
                    print(f"قيمة غير مسموحة لـ {category}.{key}: {value}, القيم المسموحة: {rules['valid_values']}")
                    return False
            
            return True
            
        except Exception as e:
            print(f"خطأ في التحقق من صحة الإعداد {category}.{key}: {str(e)}")
            return False
    
    def _notify_category_change(self, category: str, new_values: Dict[str, Any], 
                               old_values: Dict[str, Any]) -> None:
        """
        إرسال إشعارات للمكونات المسجلة عند تغيير إعدادات فئة معينة
        
        Args:
            category: فئة الإعدادات المتغيرة
            new_values: القيم الجديدة
            old_values: القيم القديمة
        """
        try:
            # إرسال الإشارة المناسبة حسب الفئة
            if category == 'company':
                self.company_info_changed.emit(new_values)
            elif category == 'currency':
                if 'symbol' in new_values:
                    self.currency_changed.emit(new_values['symbol'])
            elif category == 'printer':
                self.printer_settings_changed.emit(new_values)
            elif category == 'invoice':
                self.invoice_settings_changed.emit(new_values)
            elif category == 'backup':
                self.backup_settings_changed.emit(new_values)
            elif category == 'ui':
                self.ui_settings_changed.emit(new_values)
                if 'theme' in new_values:
                    self.theme_changed.emit(new_values['theme'])
                if 'language' in new_values:
                    self.language_changed.emit(new_values['language'])
            
            # إشعار المكونات المسجلة
            self._notify_registered_components(category, new_values, old_values)
            
        except Exception as e:
            print(f"خطأ في إرسال إشعارات تغيير الفئة {category}: {str(e)}")
    
    def notify_components(self, category: str, new_values: Dict[str, Any], 
                         old_values: Dict[str, Any] = None) -> Dict[str, bool]:
        """
        إشعار المكونات المسجلة لفئة معينة مع تتبع النتائج
        
        Args:
            category: فئة الإعدادات
            new_values: القيم الجديدة
            old_values: القيم القديمة (اختياري)
            
        Returns:
            قاموس يحتوي على نتائج إشعار كل مكون (True/False)
        """
        results = {}
        
        try:
            if category not in self.registered_components:
                print(f"لا توجد مكونات مسجلة لفئة {category}")
                return results
            
            print(f"إشعار {len(self.registered_components[category])} مكون لتغيير {category}")
            
            # إنشاء نسخة من القائمة لتجنب مشاكل التعديل أثناء التكرار
            components_list = list(self.registered_components[category])
            
            for i, component_info in enumerate(components_list):
                component = component_info['component']
                callback = component_info['callback']
                component_name = f"{type(component).__name__}_{i}"
                
                try:
                    # التحقق من أن المكون ما زال صالحاً
                    if not self._is_component_valid(component):
                        print(f"إزالة مكون غير صالح: {component_name}")
                        self._remove_invalid_component(category, component)
                        results[component_name] = False
                        continue
                    
                    # تنفيذ الإشعار
                    success = self._execute_component_notification(
                        component, callback, category, new_values, old_values
                    )
                    
                    results[component_name] = success
                    
                    if success:
                        print(f"تم إشعار المكون {component_name} بنجاح")
                    else:
                        print(f"فشل في إشعار المكون {component_name}")
                        
                except Exception as e:
                    print(f"خطأ في إشعار مكون {component_name} لتغيير {category}: {str(e)}")
                    results[component_name] = False
                    # لا نتوقف عند فشل مكون واحد
                    continue
            
            # إحصائيات النتائج
            successful = sum(1 for success in results.values() if success)
            total = len(results)
            print(f"تم إشعار {successful}/{total} مكون بنجاح لفئة {category}")
            
            return results
                    
        except Exception as e:
            print(f"خطأ عام في إشعار المكونات المسجلة لـ {category}: {str(e)}")
            return results
    
    def _notify_registered_components(self, category: str, new_values: Dict[str, Any], 
                                    old_values: Dict[str, Any]) -> None:
        """
        دالة مساعدة للتوافق مع الكود القديم - تستدعي notify_components
        """
        self.notify_components(category, new_values, old_values)
    
    def _is_component_valid(self, component: QObject) -> bool:
        """
        التحقق من صحة المكون
        
        Args:
            component: المكون المراد التحقق منه
            
        Returns:
            True إذا كان المكون صالحاً
        """
        try:
            # التحقق من أن المكون ما زال موجوداً وصالحاً
            if component is None:
                return False
            
            # التحقق من أن المكون لم يتم حذفه
            if hasattr(component, 'isWidgetType') and component.isWidgetType():
                # للـ widgets، التحقق من أنها لم تُحذف
                try:
                    # محاولة الوصول لخاصية أساسية
                    _ = component.objectName()
                    return True
                except RuntimeError:
                    # المكون تم حذفه
                    return False
            
            # للكائنات الأخرى، التحقق الأساسي
            return hasattr(component, '__class__')
            
        except Exception as e:
            print(f"خطأ في التحقق من صحة المكون: {str(e)}")
            return False
    
    def _remove_invalid_component(self, category: str, invalid_component: QObject) -> None:
        """
        إزالة مكون غير صالح من قائمة المكونات المسجلة
        
        Args:
            category: فئة الإعدادات
            invalid_component: المكون غير الصالح
        """
        try:
            if category in self.registered_components:
                self.registered_components[category] = [
                    c for c in self.registered_components[category] 
                    if c['component'] != invalid_component
                ]
                print(f"تم إزالة مكون غير صالح من فئة {category}")
                
        except Exception as e:
            print(f"خطأ في إزالة المكون غير الصالح: {str(e)}")
    
    def _execute_component_notification(self, component: QObject, callback: Callable,
                                      category: str, new_values: Dict[str, Any], 
                                      old_values: Dict[str, Any]) -> bool:
        """
        تنفيذ إشعار مكون معين
        
        Args:
            component: المكون
            callback: دالة callback
            category: فئة الإعدادات
            new_values: القيم الجديدة
            old_values: القيم القديمة
            
        Returns:
            True إذا تم الإشعار بنجاح
        """
        try:
            if callback:
                # استخدام callback محدد
                if callable(callback):
                    callback(new_values, old_values)
                    return True
                else:
                    print(f"callback غير قابل للاستدعاء للمكون {type(component).__name__}")
                    return False
            else:
                # البحث عن دالة callback بالاسم المتوقع
                method_name = f"on_{category}_settings_changed"
                if hasattr(component, method_name):
                    method = getattr(component, method_name)
                    if callable(method):
                        method(new_values, old_values)
                        return True
                    else:
                        print(f"الدالة {method_name} غير قابلة للاستدعاء في {type(component).__name__}")
                        return False
                else:
                    # محاولة البحث عن دالة عامة
                    generic_method_name = "on_settings_changed"
                    if hasattr(component, generic_method_name):
                        method = getattr(component, generic_method_name)
                        if callable(method):
                            method(category, new_values, old_values)
                            return True
                    
                    print(f"لم يتم العثور على دالة {method_name} أو {generic_method_name} في {type(component).__name__}")
                    return False
                    
        except Exception as e:
            print(f"خطأ في تنفيذ إشعار المكون: {str(e)}")
            return False
    
    def get_all_settings(self) -> Dict[str, Dict[str, Any]]:
        """
        الحصول على جميع الإعدادات
        
        Returns:
            قاموس يحتوي على جميع الإعدادات مقسمة حسب الفئات
        """
        try:
            all_settings = {}
            
            for category in self.default_settings.keys():
                all_settings[category] = {}
                for key in self.default_settings[category].keys():
                    all_settings[category][key] = self.get_setting(category, key)
            
            return all_settings
            
        except Exception as e:
            print(f"خطأ في الحصول على جميع الإعدادات: {str(e)}")
            return {}
    
    def reset_category_to_defaults(self, category: str) -> bool:
        """
        إعادة تعيين فئة إعدادات إلى القيم الافتراضية
        
        Args:
            category: فئة الإعدادات
            
        Returns:
            True إذا تم الإعادة بنجاح
        """
        try:
            if category not in self.default_settings:
                print(f"فئة الإعدادات غير موجودة: {category}")
                return False
            
            return self.apply_settings_category(category, self.default_settings[category])
            
        except Exception as e:
            print(f"خطأ في إعادة تعيين إعدادات {category}: {str(e)}")
            return False
    
    def get_registered_components_count(self, category: str = None) -> Dict[str, int]:
        """
        الحصول على عدد المكونات المسجلة
        
        Args:
            category: فئة معينة (اختياري)
            
        Returns:
            قاموس يحتوي على عدد المكونات لكل فئة
        """
        try:
            if category:
                if category in self.registered_components:
                    return {category: len(self.registered_components[category])}
                else:
                    return {category: 0}
            else:
                return {cat: len(components) for cat, components in self.registered_components.items()}
                
        except Exception as e:
            print(f"خطأ في الحصول على عدد المكونات المسجلة: {str(e)}")
            return {}
    
    def cleanup_invalid_components(self) -> Dict[str, int]:
        """
        تنظيف المكونات غير الصالحة من جميع الفئات
        
        Returns:
            قاموس يحتوي على عدد المكونات المحذوفة لكل فئة
        """
        removed_counts = {}
        
        try:
            for category in list(self.registered_components.keys()):
                original_count = len(self.registered_components[category])
                
                # تصفية المكونات الصالحة فقط
                valid_components = []
                for component_info in self.registered_components[category]:
                    if self._is_component_valid(component_info['component']):
                        valid_components.append(component_info)
                
                self.registered_components[category] = valid_components
                removed_count = original_count - len(valid_components)
                removed_counts[category] = removed_count
                
                if removed_count > 0:
                    print(f"تم إزالة {removed_count} مكون غير صالح من فئة {category}")
            
            total_removed = sum(removed_counts.values())
            if total_removed > 0:
                print(f"تم تنظيف {total_removed} مكون غير صالح إجمالياً")
            
            return removed_counts
            
        except Exception as e:
            print(f"خطأ في تنظيف المكونات غير الصالحة: {str(e)}")
            return {}
    
    def register_multiple_components(self, components_config: List[Dict]) -> Dict[str, bool]:
        """
        تسجيل عدة مكونات دفعة واحدة
        
        Args:
            components_config: قائمة من قواميس تحتوي على معلومات المكونات
                              كل قاموس يحتوي على: component, categories, callback (اختياري)
                              
        Returns:
            قاموس يحتوي على نتائج تسجيل كل مكون
        """
        results = {}
        
        try:
            for i, config in enumerate(components_config):
                component_name = f"component_{i}"
                
                try:
                    component = config.get('component')
                    categories = config.get('categories', [])
                    callback = config.get('callback')
                    
                    if not component:
                        results[component_name] = False
                        print(f"مكون غير صالح في الفهرس {i}")
                        continue
                    
                    if not categories:
                        results[component_name] = False
                        print(f"لا توجد فئات محددة للمكون في الفهرس {i}")
                        continue
                    
                    # تسجيل المكون
                    success = self.register_component(component, categories, callback)
                    results[component_name] = success
                    
                    if success:
                        print(f"تم تسجيل المكون {i} للفئات: {', '.join(categories)}")
                    else:
                        print(f"فشل في تسجيل المكون {i}")
                        
                except Exception as e:
                    results[component_name] = False
                    print(f"خطأ في تسجيل المكون {i}: {str(e)}")
                    continue
            
            successful = sum(1 for success in results.values() if success)
            total = len(results)
            print(f"تم تسجيل {successful}/{total} مكون بنجاح")
            
            return results
            
        except Exception as e:
            print(f"خطأ في تسجيل المكونات المتعددة: {str(e)}")
            return {}
    
    def get_component_registration_info(self, component: QObject) -> Dict[str, List[str]]:
        """
        الحصول على معلومات تسجيل مكون معين
        
        Args:
            component: المكون المراد البحث عنه
            
        Returns:
            قاموس يحتوي على الفئات المسجل فيها المكون
        """
        try:
            registration_info = {'categories': []}
            
            for category, components_list in self.registered_components.items():
                for component_info in components_list:
                    if component_info['component'] == component:
                        registration_info['categories'].append(category)
                        break
            
            return registration_info
            
        except Exception as e:
            print(f"خطأ في الحصول على معلومات تسجيل المكون: {str(e)}")
            return {'categories': []}
    
    def get_paper_width_safe(self) -> int:
        """
        الحصول على عرض الورق بطريقة آمنة مع التحقق من صحة القيمة
        
        Returns:
            int: عرض الورق بالمليمتر (58, 80, أو 210)
        """
        try:
            # قراءة الإعداد من المفتاح الموحد
            paper_width = self.get_setting("printer", "paper_width", 80)
            
            # التحقق من نوع البيانات وتحويلها إذا لزم الأمر
            if isinstance(paper_width, str):
                try:
                    paper_width = int(paper_width)
                except ValueError:
                    print(f"قيمة paper_width غير صحيحة (نص): {paper_width}, استخدام 80 كافتراضي")
                    paper_width = 80
            
            # التحقق من صحة القيمة
            valid_sizes = [58, 80, 210]
            if paper_width not in valid_sizes:
                print(f"قيمة paper_width غير مدعومة: {paper_width}, استخدام 80 كافتراضي")
                paper_width = 80
                # حفظ القيمة الافتراضية الصحيحة
                self.set_setting("printer", "paper_width", paper_width)
            
            print(f"حجم الورق المقروء: {paper_width}مم")
            return paper_width
        
        except Exception as e:
            print(f"خطأ في قراءة حجم الورق: {str(e)}")
            return 80
    
    def get_real_company_settings(self) -> dict:
        """
        الحصول على إعدادات الشركة الحقيقية مع التحقق من وجود بيانات مخصصة
        
        Returns:
            dict: قاموس يحتوي على بيانات الشركة مع معلومات إضافية عن صحة البيانات
        """
        try:
            # القيم الافتراضية التي يجب تجنبها
            default_values = {
                'name': "اسم الشركة",
                'phone': "رقم الهاتف", 
                'address': "عنوان الشركة",
                'notes': "شكراً لتعاملكم معنا"
            }
            
            # قراءة الإعدادات من QSettings
            company_info = {
                'name': self.get_setting("company", "name", ""),
                'phone': self.get_setting("company", "phone", ""),
                'address': self.get_setting("company", "address", ""),
                'notes': self.get_setting("company", "notes", "شكراً لتعاملكم معنا")
            }
            
            # التحقق من وجود بيانات حقيقية
            has_real_data = True
            empty_fields = []
            default_fields = []
            
            for key, value in company_info.items():
                # التحقق من الحقول الفارغة
                if not value or value.strip() == "":
                    empty_fields.append(key)
                    has_real_data = False
                # التحقق من القيم الافتراضية
                elif key in default_values and value == default_values[key]:
                    default_fields.append(key)
                    has_real_data = False
            
            # إضافة معلومات إضافية عن حالة البيانات
            company_info['_metadata'] = {
                'has_real_data': has_real_data,
                'empty_fields': empty_fields,
                'default_fields': default_fields,
                'is_validated': True
            }
            
            # طباعة تشخيصية
            if has_real_data:
                print("✅ تم العثور على بيانات شركة حقيقية ومخصصة")
            else:
                if empty_fields:
                    print(f"⚠️ تحذير: حقول فارغة في بيانات الشركة: {', '.join(empty_fields)}")
                if default_fields:
                    print(f"⚠️ تحذير: حقول تحتوي على قيم افتراضية: {', '.join(default_fields)}")
                print("📝 يرجى تحديث معلومات الشركة في تاب الإعدادات")
            
            return company_info
            
        except Exception as e:
            print(f"خطأ في قراءة إعدادات الشركة: {str(e)}")
            # إرجاع بيانات آمنة في حالة الخطأ
            return {
                'name': "",
                'phone': "",
                'address': "",
                'notes': "شكراً لتعاملكم معنا",
                '_metadata': {
                    'has_real_data': False,
                    'empty_fields': ['name', 'phone', 'address'],
                    'default_fields': [],
                    'is_validated': False,
                    'error': str(e)
                }
            }
    
    def validate_company_info(self, company_info: dict) -> bool:
        """
        التحقق من صحة بيانات الشركة
        
        Args:
            company_info: قاموس بيانات الشركة
            
        Returns:
            bool: True إذا كانت البيانات صحيحة ومكتملة
        """
        try:
            if not isinstance(company_info, dict):
                return False
            
            # التحقق من وجود الحقول الأساسية
            required_fields = ['name']
            for field in required_fields:
                if field not in company_info:
                    return False
                if not company_info[field] or company_info[field].strip() == "":
                    return False
            
            # التحقق من عدم وجود قيم افتراضية
            default_values = {
                'name': "اسم الشركة",
                'phone': "رقم الهاتف",
                'address': "عنوان الشركة"
            }
            
            for field, default_value in default_values.items():
                if field in company_info and company_info[field] == default_value:
                    return False
            
            return True
            
        except Exception as e:
            print(f"خطأ في التحقق من صحة بيانات الشركة: {str(e)}")
            return False
    
    def has_real_company_data(self) -> bool:
        """
        التحقق من وجود بيانات شركة حقيقية (غير افتراضية)
        
        Returns:
            bool: True إذا كانت هناك بيانات شركة حقيقية
        """
        try:
            company_info = self.get_real_company_settings()
            return company_info.get('_metadata', {}).get('has_real_data', False)
            
        except Exception as e:
            print(f"خطأ في التحقق من وجود بيانات الشركة: {str(e)}")
            return False
            
        except Exception as e:
            print(f"خطأ في قراءة إعدادات حجم الورق: {str(e)}")
            return 80  # القيمة الافتراضية الآمنة
    
    def set_paper_width_safe(self, paper_width: int) -> bool:
        """
        تعيين عرض الورق بطريقة آمنة مع التحقق من صحة القيمة
        
        Args:
            paper_width: عرض الورق بالمليمتر
            
        Returns:
            bool: True إذا تم الحفظ بنجاح
        """
        try:
            # التحقق من صحة القيمة
            valid_sizes = [58, 80, 210]
            if paper_width not in valid_sizes:
                print(f"قيمة paper_width غير مدعومة: {paper_width}")
                return False
            
            # حفظ الإعداد
            success = self.set_setting("printer", "paper_width", paper_width)
            
            if success:
                print(f"تم حفظ حجم الورق: {paper_width}مم")
            else:
                print(f"فشل في حفظ حجم الورق: {paper_width}مم")
            
            return success
            
        except Exception as e:
            print(f"خطأ في حفظ إعدادات حجم الورق: {str(e)}")
            return False
    
    def get_paper_dimensions(self, paper_width: int = None) -> tuple:
        """
        الحصول على أبعاد الورق (العرض والارتفاع) بالمليمتر
        
        Args:
            paper_width: عرض الورق (اختياري، سيتم قراءته من الإعدادات إذا لم يُمرر)
            
        Returns:
            tuple: (العرض، الارتفاع) بالمليمتر
        """
        try:
            if paper_width is None:
                paper_width = self.get_paper_width_safe()
            
            # تحديد الأبعاد حسب نوع الورق
            if paper_width == 58:
                return (58, 200)  # ورق حراري 58مم
            elif paper_width == 80:
                return (80, 250)  # ورق حراري 80مم
            elif paper_width == 210:
                return (210, 297)  # ورق A4
            else:
                print(f"حجم ورق غير معروف: {paper_width}، استخدام 80مم كافتراضي")
                return (80, 250)
                
        except Exception as e:
            print(f"خطأ في تحديد أبعاد الورق: {str(e)}")
            return (80, 250)  # القيمة الافتراضية الآمنة