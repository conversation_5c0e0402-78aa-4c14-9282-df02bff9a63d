#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os
import datetime
import subprocess
import io
import tempfile
import atexit
from typing import Optional, Any
from PyQt5.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QPushButton, QLabel, QStackedWidget,
                             QFrame, QSizePolicy, QSpacerItem, QStatusBar, QDesktopWidget,
                             QMessageBox, QSplashScreen, QMenu, QShortcut, QSystemTrayIcon, QCheckBox)
from PyQt5.QtCore import Qt, QSize, QTimer, QtMsgType, qInstallMessageHandler, QEvent
from PyQt5.QtGui import QIcon, QFont, QPixmap, QFontDatabase, QPainter, QColor, QKeySequence, QCloseEvent

# إضافة استيرادات إضافية لـ Qt constants

# إعداد نظام التسجيل لتوجيه الرسائل إلى ملف بدلاً من وحدة التحكم
DEBUG_MODE = False  # تعيين القيمة True للتطوير و False للإنتاج
LOG_FILE = "app.log"

# حفظ مرجع لوحدة الطباعة الأصلية قبل إعادة التوجيه
original_stdout = sys.stdout
original_stderr = sys.stderr

# تحديد مسارات الموارد بطريقة تعمل داخل الملف التنفيذي (frozen) وخلال التطوير
def get_base_dir():
    """إرجاع مجلد تشغيل التطبيق (مجلد الـ exe عند التصدير)."""
    try:
        if getattr(sys, "frozen", False):
            # عند التشغيل من exe
            return os.path.dirname(sys.executable)
        # أثناء التطوير
        return os.path.dirname(os.path.abspath(__file__))
    except Exception:
        return os.getcwd()

def resource_path(relative_path: str) -> str:
    """بناء مسار ملف مورد يدعم PyInstaller (sys._MEIPASS) ومجلد exe الحالي."""
    base_dir = get_base_dir()
    candidates = [
        os.path.join(base_dir, relative_path),
    ]
    # ملفات تم فكها مؤقتًا داخل _MEIPASS (في حالة onefile)
    meipass = getattr(sys, "_MEIPASS", None)
    if meipass:
        candidates.append(os.path.join(meipass, relative_path))

    for path in candidates:
        if os.path.exists(path):
            return path
    # fallback: أول مرشح حتى إن لم يكن موجودًا بعد (قد يتم إنشاؤه لاحقًا مثل logs)
    return candidates[0]

# تثبيت مجلد العمل إلى مجلد التطبيق لضمان العثور على الموارد النسبية (db, styles, الخطوط، ...)
try:
    os.chdir(get_base_dir())
except Exception:
    pass


# دالة للطباعة في وحدة التحكم حتى في وضع الإنتاج
def console_print(*args, **kwargs):
    """وظيفة للطباعة في وحدة التحكم حتى عندما يتم إعادة توجيه الإخراج العادي إلى ملف"""
    if DEBUG_MODE:
        print(*args, **kwargs)
    else:
        # طباعة في ملف السجل
        print(*args, **kwargs)
        # طباعة في وحدة التحكم أيضا
        print(*args, file=original_stdout, **kwargs)

# تهيئة ملف السجل
log_file = None  # Initialize log_file variable
if not DEBUG_MODE:
    # إنشاء مجلد logs إذا لم يكن موجودًا
    logs_dir = resource_path("logs")
    try:
        os.makedirs(logs_dir, exist_ok=True)
    except Exception:
        # في حال فشل إنشاء مجلد logs داخل مسار التطبيق (مثل Program Files)، استخدم %TEMP%
        logs_dir = os.path.join(tempfile.gettempdir(), "SmartManager_logs")
        os.makedirs(logs_dir, exist_ok=True)
    log_path = os.path.join(logs_dir, LOG_FILE)

    # فتح ملف السجل للكتابة
    log_file = open(log_path, 'a', encoding='utf-8')

    # إعادة توجيه الإخراج إلى ملف السجل - طريقة بسيطة ومباشرة
    sys.stdout = log_file
    sys.stderr = log_file

    # تفعيل الكتابة الفورية في ملف السجل
    sys.stdout.flush()
    sys.stderr.flush()

    # تفعيل الكتابة الفورية في ملف السجل
    sys.stdout.flush()

# إضافة معالج الرسائل المخصص لمنع ظهور رسائل التحذير في وحدة التحكم
def suppress_qt_warnings(msg_type, context, message):
    # تجاهل رسائل "Unknown property direction"
    if "Unknown property direction" in message:
        return

    # طباعة الرسائل المهمة فقط (الخطأ الشديد والمهلك)
    if msg_type in (QtMsgType.QtCriticalMsg, QtMsgType.QtFatalMsg):
        if not DEBUG_MODE:
            console_print(f"Qt Error: {message}")
        else:
            pass

# استيراد ملف التنسيقات
from styles import AppStyles

# استيراد مكونات قاعدة البيانات
from models.database import db
from controllers.database_controller import DatabaseController

# استيراد مكونات المستخدمين والمصادقة
from controllers.user_controller import UserController
from views.login import LoginWindow

# استيراد مدير التراخيص
from utils.license_manager import LicenseManager

# استيراد جميع الصفحات مسبقاً لضمان توفرها في الملف التنفيذي
# Pre-import all view modules to ensure they're available in the executable
from views.sales import SalesView
from views.invoices import InvoicesView
from views.reports import ReportsView
from views.customers import CustomersView
from views.suppliers import SuppliersView
from views.products import ProductsView
from views.expenses import ExpensesView
from views.settings import SettingsView

class SingleInstanceManager:
    """مدير لضمان تشغيل نسخة واحدة فقط من البرنامج"""

    def __init__(self, app_name="SmartManager"):
        self.app_name = app_name
        self.lock_file_path = os.path.join(tempfile.gettempdir(), f"{app_name}.lock")
        self.signal_file_path = os.path.join(tempfile.gettempdir(), f"{app_name}_signal.txt")
        self.lock_file = None

    def is_already_running(self):
        """التحقق من وجود نسخة أخرى من البرنامج"""
        try:
            # محاولة إنشاء ملف القفل
            if os.path.exists(self.lock_file_path):
                # التحقق من أن العملية ما زالت تعمل
                try:
                    with open(self.lock_file_path, 'r') as f:
                        pid = int(f.read().strip())

                    # التحقق من وجود العملية
                    if os.name == 'nt':  # Windows
                        try:
                            # استخدام tasklist للتحقق من وجود العملية
                            # إعداد معاملات subprocess لإخفاء نافذة cmd على Windows
                            if os.name == 'nt':  # Windows only
                                result = subprocess.run(
                                    ['tasklist', '/FI', f'PID eq {pid}'],
                                    capture_output=True,
                                    text=True,
                                    shell=True,
                                    creationflags=subprocess.CREATE_NO_WINDOW
                                )
                            else:
                                result = subprocess.run(
                                    ['tasklist', '/FI', f'PID eq {pid}'],
                                    capture_output=True,
                                    text=True,
                                    shell=True
                                )
                            if str(pid) in result.stdout:
                                return True
                            else:
                                # العملية غير موجودة، يمكن حذف ملف القفل
                                os.remove(self.lock_file_path)
                                return False
                        except Exception:
                            # في حالة فشل tasklist، نفترض أن العملية موجودة للأمان
                            return True
                    else:  # Linux/Unix
                        try:
                            os.kill(pid, 0)  # لا يقتل العملية، فقط يتحقق من وجودها
                            return True
                        except OSError:
                            # العملية غير موجودة، يمكن حذف ملف القفل
                            os.remove(self.lock_file_path)
                            return False
                except (ValueError, FileNotFoundError):
                    # ملف القفل تالف، يمكن حذفه
                    try:
                        os.remove(self.lock_file_path)
                    except:
                        pass
                    return False

            return False

        except Exception as e:
            print(f"خطأ في التحقق من النسخة الأخرى: {str(e)}")
            return False

    def send_show_signal(self):
        """إرسال إشارة لإظهار النافذة الموجودة"""
        try:
            with open(self.signal_file_path, 'w') as f:
                f.write("show_window")
            return True
        except Exception as e:
            print(f"خطأ في إرسال إشارة الإظهار: {str(e)}")
            return False

    def check_show_signal(self):
        """التحقق من وجود إشارة إظهار النافذة"""
        try:
            if os.path.exists(self.signal_file_path):
                os.remove(self.signal_file_path)
                return True
            return False
        except Exception as e:
            print(f"خطأ في التحقق من إشارة الإظهار: {str(e)}")
            return False

    def create_lock(self):
        """إنشاء ملف القفل"""
        try:
            with open(self.lock_file_path, 'w') as f:
                f.write(str(os.getpid()))

            # تسجيل دالة لحذف ملف القفل عند إغلاق البرنامج
            atexit.register(self.remove_lock)
            return True

        except Exception as e:
            print(f"خطأ في إنشاء ملف القفل: {str(e)}")
            return False

    def remove_lock(self):
        """حذف ملف القفل وملف الإشارة"""
        try:
            if os.path.exists(self.lock_file_path):
                os.remove(self.lock_file_path)
            if os.path.exists(self.signal_file_path):
                os.remove(self.signal_file_path)
        except Exception as e:
            print(f"خطأ في حذف ملف القفل: {str(e)}")

class MainWindow(QMainWindow):
    def __init__(self, user_data=None):
        super().__init__()

        # تخزين بيانات المستخدم الحالي
        self.current_user = user_data or {}

        # إنشاء مدير التراخيص
        self.license_manager = LicenseManager()

        # قائمة الصلاحيات المطلوبة لكل صفحة
        self.required_permissions = {
            0: "عرض المبيعات",      # المبيعات
            1: "عرض الفواتير",      # الفواتير
            2: "عرض تقارير المبيعات",  # التقارير
            3: "عرض العملاء",       # العملاء
            4: "عرض الموردين",      # الموردين
            5: "عرض المنتجات",      # المنتجات
            6: "عرض المصروفات",     # المصروفات
            7: "عرض الإعدادات"      # الإعدادات
        }

        # إعداد النافذة الرئيسية
        self.setWindowTitle("SMART MANAGER")
        self.setMinimumSize(810, 600)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)  # تعيين اتجاه التخطيط من اليمين إلى اليسار

        # ضبط حجم النافذة بناءً على حجم الشاشة (75% عرض و70% ارتفاع)
        self.adjust_window_size()

        # إعداد التخطيط الرئيسي
        main_widget = QWidget()
        main_layout = QHBoxLayout(main_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)

        # إنشاء الشريط الجانبي للتنقل
        self.sidebar_widget = QWidget()
        self.sidebar_widget.setObjectName("sidebar")
        self.sidebar_widget.setFixedWidth(240)  # زيادة عرض الشريط الجانبي قليلاً
        self.sidebar_widget.setLayoutDirection(Qt.LayoutDirection.LeftToRight)  # الشريط الجانبي من اليسار إلى اليمين

        sidebar_layout = QVBoxLayout(self.sidebar_widget)
        sidebar_layout.setContentsMargins(0, 0, 0, 0)
        sidebar_layout.setSpacing(0)

        # عنوان التطبيق في الشريط الجانبي
        title_widget = QWidget()
        title_widget.setObjectName("title_widget")
        title_widget.setMinimumHeight(75)  # زيادة ارتفاع رأس الشريط الجانبي
        self.title_widget = title_widget

        title_layout = QVBoxLayout(title_widget)
        title_label = QLabel("SMART MANAGER")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(AppStyles.get_system_font(14, bold=True))
        title_label.setObjectName("title_label")
        title_layout.addWidget(title_label)

        sidebar_layout.addWidget(title_widget)

        # إضافة فاصل بعد العنوان
        separator = QFrame()
        separator.setFrameShape(QFrame.HLine)
        separator.setFrameShadow(QFrame.Sunken)
        separator.setObjectName("sidebar_separator")
        sidebar_layout.addWidget(separator)
        sidebar_layout.addSpacing(10)  # إضافة مسافة بعد الفاصل

        # إنشاء أزرار التنقل مع التحميل الكسول
        self.nav_buttons = []
        # تخزين معلومات الصفحات للتحميل الكسول (تحسين الأداء)
        nav_items = [
            {"name": "المبيعات", "module": "views.sales", "class": "SalesView", "icon": "🛒"},
            {"name": "الفواتير", "module": "views.invoices", "class": "InvoicesView", "icon": "📄"},
            {"name": "التقارير", "module": "views.reports", "class": "ReportsView", "icon": "📊"},
            {"name": "العملاء", "module": "views.customers", "class": "CustomersView", "icon": "👥"},
            {"name": "الموردين", "module": "views.suppliers", "class": "SuppliersView", "icon": "🏭"},
            {"name": "المنتجات", "module": "views.products", "class": "ProductsView", "icon": "📦"},
            {"name": "المصروفات", "module": "views.expenses", "class": "ExpensesView", "icon": "💰"}
        ]

        # تخزين معلومات الصفحات للتحميل الكسول
        self.page_info = nav_items.copy()
        self.loaded_pages = {}  # قاموس لتخزين الصفحات المحملة

        # إنشاء StackedWidget للصفحات
        self.content_widget = QStackedWidget()

        # إنشاء إطار لتجميع أزرار التنقل
        nav_frame = QFrame()
        nav_frame.setObjectName("nav_frame")
        self.nav_frame = nav_frame
        nav_layout = QVBoxLayout(nav_frame)
        self.nav_layout = nav_layout
        nav_layout.setContentsMargins(15, 8, 15, 8)
        nav_layout.setSpacing(8)

        # قاموس tooltips لأزرار التنقل
        nav_tooltips = {
            "المبيعات": "إدارة عمليات البيع وإنشاء الفواتير",
            "الفواتير": "عرض وإدارة جميع الفواتير",
            "التقارير": "عرض التقارير والإحصائيات",
            "العملاء": "إدارة بيانات العملاء والديون",
            "الموردين": "إدارة الموردين وفواتير الشراء",
            "المنتجات": "إدارة المنتجات والخدمات والمخزون",
            "المصروفات": "تسجيل وإدارة المصروفات"
        }

        for i, item in enumerate(nav_items):
            # إنشاء زر التنقل
            nav_button = QPushButton(f"{item['icon']}  {item['name']}")
            nav_button.setObjectName("nav_button")
            nav_button.setMinimumHeight(50)
            nav_button.setFont(AppStyles.get_system_font(11))
            nav_button.setCheckable(True)
            nav_button.clicked.connect(lambda checked, idx=i: self.change_page(idx))

            # إضافة tooltip للزر
            nav_button.setToolTip(nav_tooltips.get(item['name'], f"الانتقال إلى صفحة {item['name']}"))

            self.nav_buttons.append(nav_button)
            nav_layout.addWidget(nav_button)

            # تحميل الصفحة فوراً مع تأجيل العمليات الثقيلة
            try:
                # استخدام مرجع مباشر بدلاً من الاستيراد الديناميكي
                page = None
                if item['class'] == 'SalesView':
                    page = SalesView()
                elif item['class'] == 'InvoicesView':
                    page = InvoicesView(              elif item['class'] == 'ReportsView':
                    page = ReportsView()
                elif item['class'] == 'CustomersView':
                    page = CustomersView()
                elif item['class'] == 'SuppliersView':
                    page = SuppliersView()
                elif item['class'] == 'ProductsView':
                    page = ProductsView()
                elif item['class'] == 'ExpensesView':
                    page = ExpensesView()
                else:
                    raise Exception(f"Unknown class: {item['class']}")

                if page:
                    page.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
                    
                    # وضع علامة للصفحة لمعرفة أنها لم يتم تحديثها بعد
                    page._initial_refresh_needed = True
                    
                    self.content_widget.addWidget(page)
                    self.loaded_pages[i] = page
                
            except Exception as e:
                # إنشاء صفحة خطأ
                error_widget = QWidget()
                error_layout = QVBoxLayout(error_widget)
                error_label = QLabel(f"خطأ في تحميل صفحة {item['name']}")
                error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                error_label.setStyleSheet("color: red; padding: 20px;")
                error_layout.addWidget(error_label)
                self.content_widget.addWidget(error_widget)

        # إضافة زر الباركود
        barcode_button = QPushButton("📱  الباركود")
        barcode_button.setObjectName("nav_button")
        barcode_button.setMinimumHeight(50)
        barcode_button.setFont(AppStyles.get_system_font(11))
        barcode_button.clicked.connect(self.open_barcode_window)
        barcode_button.setToolTip("إنشاء وطباعة الباركود للمنتجات")
        nav_layout.addWidget(barcode_button)

        # إضافة زر الآلة الحاسبة
        calc_button = QPushButton("🧮  الآلة الحاسبة")
        calc_button.setObjectName("nav_button")
        calc_button.setMinimumHeight(50)
        calc_button.setFont(AppStyles.get_system_font(11))
        calc_button.clicked.connect(self.open_calculator)
        calc_button.setToolTip("فتح الآلة الحاسبة للعمليات الحسابية")
        nav_layout.addWidget(calc_button)

        # إضافة زر الإعدادات
        settings_button = QPushButton("⚙️  الإعدادات")
        settings_button.setObjectName("nav_button")
        settings_button.setMinimumHeight(50)
        settings_button.setFont(AppStyles.get_system_font(11))
        settings_button.setCheckable(True)
        settings_button.clicked.connect(lambda checked: self.change_page(7))
        settings_button.setToolTip("إعدادات التطبيق والنظام")
        self.nav_buttons.append(settings_button)
        nav_layout.addWidget(settings_button)

        # تحميل صفحة الإعدادات فوراً مع تأجيل العمليات الثقيلة
        try:
            # استيراد وحدة الإعدادات
            settings_page = SettingsView()
            settings_page.setLayoutDirection(Qt.LayoutDirection.RightToLeft)
            
            # وضع علامة للصفحة لمعرفة أنها لم يتم تحديثها بعد
            settings_page._initial_refresh_needed = True
            
            # إضافة الصفحة إلى المكدس
            self.content_widget.addWidget(settings_page)
            
            # حفظ الصفحة في الذاكرة
            self.loaded_pages[7] = settings_page
            
        except Exception as e:
            # إنشاء صفحة خطأ
            error_widget = QWidget()
            error_layout = QVBoxLayout(error_widget)
            error_label = QLabel("خطأ في تحميل صفحة الإعدادات")
            error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            error_label.setStyleSheet("color: red; padding: 20px;")
            error_layout.addWidget(error_label)
            self.content_widget.addWidget(error_widget)

        # إضافة معلومات صفحة الإعدادات للتحميل الكسول
        self.page_info.append({"name": "الإعدادات", "module": "views.settings", "class": "SettingsView", "icon": "⚙️"})

        # إضافة إطار الأزرار إلى الشريط الجانبي مباشرة بدون تمرير
        sidebar_layout.addWidget(nav_frame)

        # إضافة مساحة فارغة في نهاية الشريط الجانبي
        sidebar_layout.addItem(QSpacerItem(20, 40, QSizePolicy.Minimum, QSizePolicy.Expanding))

        # إضافة معلومات في أسفل الشريط الجانبي
        footer_label = QLabel("SMART MANAGER © 2025")
        footer_label.setObjectName("footer_label")
        footer_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.footer_label = footer_label
        sidebar_layout.addWidget(footer_label)
        sidebar_layout.addSpacing(10)

        # إضافة الشريط الجانبي والمحتوى إلى التخطيط الرئيسي
        main_layout.addWidget(self.sidebar_widget)

        # إضافة فاصل بين الشريط الجانبي والمحتوى
        # En lugar de usar un simple QFrame, crear un QWidget personalizado para el separador
        content_separator = QWidget()
        content_separator.setObjectName("content_separator")
        # Establecer un ancho fijo para el separador (4px es suficiente para el efecto de sombra)
        content_separator.setFixedWidth(4)

        # Aplicar un estilo personalizado al separador que incluya un gradiente más marcado
        content_separator.setStyleSheet("""
            #content_separator {
                background-color: qlineargradient(
                    x1:0, y1:0, x2:1, y2:0,
                    stop:0 rgba(180, 180, 180, 0.9),
                    stop:0.4 rgba(210, 210, 210, 0.4),
                    stop:1 rgba(240, 240, 240, 0.1)
                );
                border: none;
                margin: 5px 0px;
            }
        """)

        main_layout.addWidget(content_separator)

        main_layout.addWidget(self.content_widget)

        # تحديد الصفحة الافتراضية (صفحة المبيعات)
        self.content_widget.setCurrentIndex(0)
        # تحديث حالة أزرار التنقل
        for i, button in enumerate(self.nav_buttons):
            button.setChecked(i == 0)
            
        # إعداد حالة التحميل الأولي
        self._initial_loading = True

        # إضافة الـ Widget الرئيسي إلى النافذة
        self.setCentralWidget(main_widget)

        # إنشاء شريط الحالة السفلي
        self.statusBar = QStatusBar()
        self.statusBar.setObjectName("status_bar")
        self.setStatusBar(self.statusBar)

        # إنشاء عناصر شريط الحالة
        self.status_username = QLabel("المستخدم: غير معروف")
        self.status_username.setObjectName("status_username")
        self.status_username.setAlignment(Qt.AlignmentFlag.AlignCenter)
        self.status_username.setMinimumWidth(200)

        # تفعيل قائمة السياق على اسم المستخدم
        self.status_username.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.status_username.customContextMenuRequested.connect(self.show_user_context_menu)

        # عنصر مطاط لدفع اسم المستخدم إلى أقصى اليمين
        spacer_label = QLabel()
        spacer_label.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Preferred)

        self.status_datetime = QLabel()
        self.status_datetime.setObjectName("status_item")
        self.status_datetime.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # حالة الاتصال بقاعدة البيانات (مؤجلة)
        self.status_db = QLabel()
        self.status_db.setObjectName("status_item")
        self.status_db.setAlignment(Qt.AlignmentFlag.AlignCenter)

        # فاصل
        separator = QLabel("  |  ")
        separator.setObjectName("status_separator")

        # إضافة عناصر إلى شريط الحالة (من اليسار إلى اليمين في العرض من اليمين إلى اليسار)
        self.statusBar.addWidget(self.status_username)  # سيظهر في أقصى اليمين
        self.statusBar.addWidget(separator)
        self.statusBar.addWidget(self.status_db)  # حالة الاتصال بقاعدة البيانات
        self.statusBar.addWidget(spacer_label)  # سيدفع اسم المستخدم لأقصى اليمين ويملأ المساحة الوسطى
        self.statusBar.addPermanentWidget(self.status_datetime)  # سيظهر في أقصى اليسار

        # إنشاء مؤقت لتحديث الوقت والتاريخ (مؤجل)
        self.datetime_timer = None
        
        # إنشاء مؤقت للتحقق من التفعيل دورياً
        self.license_check_timer = None

        # تطبيق الأنماط
        self.apply_styles()

        # إعداد اختصار F12 للانتقال إلى تاب المبيعات وتنشيط شريط البحث
        self.f12_shortcut = QShortcut(QKeySequence("F12"), self)
        self.f12_shortcut.activated.connect(self.go_to_sales_and_focus_search)

        # تحديد اسم المستخدم في شريط الحالة
        self.update_user_info()

        # التحقق من التفعيل وإعداد التبويبات
        self.check_license_and_setup_tabs()

        # تحديث حجم أزرار التابات للمرة الأولى
        self.update_nav_buttons_size()

        # تم إنشاء النافذة الرئيسية بنجاح - تم تحميل جميع الصفحات فورًا
        
        # تأجيل العمليات غير الحيوية لتحسين أداء بدء التشغيل
        QTimer.singleShot(500, self.setup_deferred_operations)

    def delayed_maximize(self):
        """تكبير النافذة بعد تحميل المحتوى الأساسي"""
        try:
            # التحقق من أن النافذة لا تزال مرئية ولم يتم تصغيرها
            if self.isVisible() and not self.isMinimized():
                self.showMaximized()
                # تحديث حالة التحميل
                self._initial_loading = False
                # تحديث الصفحة الحالية بعد التكبير
                current_index = self.content_widget.currentIndex()
                QTimer.singleShot(100, lambda: self.immediate_page_refresh(current_index))
        except Exception:
            # تجاهل أي أخطاء في عملية التكبير المؤجلة
            self._initial_loading = False

    def setup_deferred_operations(self):
        """إعداد العمليات المؤجلة غير الحيوية"""
        try:
            # إعداد System Tray
            self.setup_system_tray()
            
            # إعداد مؤقت للتحقق من إشارات الإظهار
            self.setup_signal_checker()
            
            # بدء مؤقت تحديث الوقت والتاريخ
            self.datetime_timer = QTimer(self)
            self.datetime_timer.timeout.connect(self.update_datetime)
            self.datetime_timer.start(1000)  # تحديث كل ثانية
            self.update_datetime()  # تحديث فوري
            
            # تحديث حالة قاعدة البيانات
            self.update_db_status()
            
            # بدء مؤقت التحقق من التفعيل دورياً
            self.setup_license_check_timer()
            
        except Exception as e:
            # تجاهل أخطاء العمليات المؤجلة لتجنب تعطيل التطبيق
            pass

    def changeEvent(self, a0: Optional[QEvent]) -> None:
        """معالجة تغيير حالة النافذة"""
        super().changeEvent(a0)

        # التحقق من تغيير حالة النافذة
        if a0 and a0.type() == QEvent.Type.WindowStateChange:
            # إذا تم تصغير النافذة من الحالة المكبرة إلى الحالة العادية
            if not self.isMaximized() and not self.isMinimized() and hasattr(self, 'normal_window_width'):
                # تطبيق الحجم المناسب المحفوظ مسبقاً
                QTimer.singleShot(50, self.apply_normal_size)

    def apply_normal_size(self):
        """تطبيق الحجم العادي المناسب للنافذة"""
        try:
            if hasattr(self, 'normal_window_width') and not self.isMaximized() and not self.isMinimized():
                self.setGeometry(
                    self.normal_x_position,
                    self.normal_y_position,
                    self.normal_window_width,
                    self.normal_window_height
                )
        except Exception as e:
            # في حالة حدوث خطأ، استخدم حجم افتراضي
            desktop = QDesktopWidget().availableGeometry()
            screen_width = desktop.width()
            screen_height = desktop.height()

            window_width = int(screen_width * 0.85)
            window_height = int(screen_height * 0.80)
            x_position = (screen_width - window_width) // 2
            y_position = (screen_height - window_height) // 2

            self.setGeometry(x_position, y_position, window_width, window_height)

    def adjust_window_size(self):
        """ضبط النافذة لتفتح بحجم كبير مع تحسين سرعة البدء"""
        # حفظ الحجم المناسب للنافذة عند التصغير
        self.calculate_and_store_normal_size()

        # فتح النافذة بحجم عادي كبير أولاً لتسريع البدء
        # ثم تكبيرها بعد تحميل المحتوى لتحسين الأداء
        desktop = QDesktopWidget().availableGeometry()
        screen_width = desktop.width()
        screen_height = desktop.height()
        
        # استخدام حجم كبير (90% من الشاشة) بدلاً من التكبير الفوري
        window_width = int(screen_width * 0.90)
        window_height = int(screen_height * 0.85)
        x_position = (screen_width - window_width) // 2
        y_position = (screen_height - window_height) // 2
        
        self.setGeometry(x_position, y_position, window_width, window_height)
        
        # تأجيل التكبير الكامل لتحسين سرعة البدء
        QTimer.singleShot(100, self.delayed_maximize)

    def calculate_and_store_normal_size(self):
        """حساب وحفظ الحجم المناسب للنافذة عند التصغير"""
        # الحصول على أبعاد الشاشة
        desktop = QDesktopWidget().availableGeometry()
        screen_width = desktop.width()
        screen_height = desktop.height()

        # حساب حجم النافذة المناسب (85% عرض و80% ارتفاع)
        self.normal_window_width = int(screen_width * 0.80)
        self.normal_window_height = int(screen_height * 0.80)

        # تحديد موقع النافذة في المنتصف
        self.normal_x_position = (screen_width - self.normal_window_width) // 2
        self.normal_y_position = (screen_height - self.normal_window_height) // 2

    def update_nav_buttons_size(self):
        """تحديث حجم أزرار التابات بناءً على حجم النافذة"""
        try:
            # الحصول على ارتفاع النافذة الحالي
            current_height = self.height()

            # تحديد الحد الأدنى والأقصى لارتفاع الأزرار
            min_button_height = 50  # الحجم الافتراضي
            max_button_height = 75  # الحد الأقصى

            # تحديد الحد الأدنى والأقصى لحجم الخط
            min_font_size = 11  # حجم الخط الافتراضي
            max_font_size = 14  # الحد الأقصى لحجم الخط

            # تحديد نطاق ارتفاع النافذة للتحكم في حجم الأزرار
            min_window_height = 600  # الحد الأدنى لارتفاع النافذة
            max_window_height = 1200  # الحد الأقصى لارتفاع النافذة

            # حساب ارتفاع الزر وحجم الخط بناءً على ارتفاع النافذة
            if current_height <= min_window_height:
                button_height = min_button_height
                font_size = min_font_size
            elif current_height >= max_window_height:
                button_height = max_button_height
                font_size = max_font_size
            else:
                # حساب نسبي للارتفاع وحجم الخط
                height_ratio = (current_height - min_window_height) / (max_window_height - min_window_height)
                button_height = min_button_height + (max_button_height - min_button_height) * height_ratio
                font_size = min_font_size + (max_font_size - min_font_size) * height_ratio

            # حساب متاح الشريط الجانبي وضبط ارتفاع الأزرار للحفاظ على الهوامش
            try:
                sidebar_available_height = self.sidebar_widget.height()
                title_widget = getattr(self, 'title_widget', None)
                header_h = title_widget.height() if title_widget else 0
                footer_label = getattr(self, 'footer_label', None)
                footer_h = footer_label.height() if footer_label else 0
                spacer_h = 40  # ارتفاع الـ QSpacerItem المحدد
                top_spacing = 10  # بعد الفاصل
                nav_margins = 16  # أعلى/أسفل من nav_layout

                fixed_top = header_h + top_spacing
                fixed_bottom = spacer_h + footer_h + 10  # + المسافة بعد الفوتر
                available_for_buttons = max(0, sidebar_available_height - fixed_top - fixed_bottom - nav_margins)

                total_buttons = len(self.nav_buttons) + 3  # + الباركود + الحاسبة + الإعدادات التي ليست في self.nav_buttons كلها؟ (الإعدادات مضافة)
                # لكننا بالفعل أضفنا settings للـ nav_buttons، لذا +2 فقط
                total_buttons = len(self.nav_buttons) + 2

                total_spacing = max(0, (total_buttons - 1) * self.nav_layout.spacing())
                per_button_max = 0
                if total_buttons > 0:
                    per_button_max = max(44, min(int(available_for_buttons - total_spacing) // total_buttons, int(button_height)))

                # ضبط ارتفاع نهائي آمن
                if per_button_max > 0:
                    button_height = min(button_height, per_button_max)
                    # تقليص الخط إذا لزم
                    if button_height <= 48:
                        font_size = max(min_font_size, int(font_size) - 1)
            except Exception:
                pass

            # تطبيق الارتفاع وحجم الخط الجديد على جميع أزرار التابات
            for button in self.nav_buttons:
                button.setMinimumHeight(int(button_height))
                # السماح بتغيير ارتفاع الأزرار لتجنب إزالة الهوامش عند تصغير النافذة
                button.setMaximumHeight(16777215)  # إعادة أقصى ارتفاع للوضع الافتراضي
                button.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Minimum)
                button.setFont(AppStyles.get_system_font(int(font_size)))

            # تطبيق الارتفاع وحجم الخط على أزرار الباركود والآلة الحاسبة والإعدادات
            # البحث عن هذه الأزرار في الشريط الجانبي
            for widget in self.sidebar_widget.findChildren(QPushButton):
                if widget.objectName() == "nav_button" and widget not in self.nav_buttons:
                    widget.setMinimumHeight(int(button_height))
                    widget.setMaximumHeight(16777215)
                    widget.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Minimum)
                    widget.setFont(AppStyles.get_system_font(int(font_size)))

            # تحديث عرض الشريط الجانبي بناءً على عرض النافذة
            current_width = self.width()
            min_sidebar_width = 240  # العرض الافتراضي
            max_sidebar_width = 300  # الحد الأقصى
            min_window_width = 800   # الحد الأدنى لعرض النافذة
            max_window_width = 1600  # الحد الأقصى لعرض النافذة

            # حساب عرض الشريط الجانبي بناءً على عرض النافذة
            if current_width <= min_window_width:
                sidebar_width = min_sidebar_width
            elif current_width >= max_window_width:
                sidebar_width = max_sidebar_width
            else:
                # حساب نسبي للعرض
                width_ratio = (current_width - min_window_width) / (max_window_width - min_window_width)
                sidebar_width = min_sidebar_width + (max_sidebar_width - min_sidebar_width) * width_ratio

            # تطبيق العرض الجديد على الشريط الجانبي
            self.sidebar_widget.setFixedWidth(int(sidebar_width))

        except Exception as e:
            # تجاهل الأخطاء لتجنب تعطيل النافذة
            pass

    def resizeEvent(self, a0):
        """معالجة أحداث تغيير حجم النافذة الرئيسية"""
        super().resizeEvent(a0)

        # تحديث حجم أزرار التابات بناءً على حجم النافذة
        self.update_nav_buttons_size()

        # إعادة حساب الحجم المناسب للنافذة عند تغيير حجم الشاشة
        if not self.isMaximized():
            self.calculate_and_store_normal_size()

        # تحديث أزرار المنتجات المفضلة في صفحة المبيعات عند تغيير حجم النافذة
        try:
            # البحث عن صفحة المبيعات في التطبيق
            sales_page = None
            for i in range(self.content_widget.count()):
                widget = self.content_widget.widget(i)
                if hasattr(widget, 'resize_buttons_grid'):  # التحقق من وجود دالة تغيير حجم الأزرار
                    sales_page = widget
                    break

            # إذا تم العثور على صفحة المبيعات، قم بتحديث أزرار المنتجات المفضلة بكفاءة
            if sales_page and hasattr(sales_page, 'resize_timer'):
                # استخدام المؤقت لتجميع التغييرات وتوفير الموارد
                sales_page.resize_timer.start(300)  # تأخير أطول لتوفير الموارد

        except Exception as e:
            # تجاهل الأخطاء لتجنب تعطيل النافذة الرئيسية
            pass

    def change_page(self, index):
        """تغيير الصفحة الحالية وتحديث حالة أزرار التنقل"""
        # التحقق من التفعيل أولاً
        if not self.license_manager.is_activated() and index != 7:  # إذا لم يكن مُفعل ولا يحاول الوصول لتاب الإعدادات
            QMessageBox.warning(
                self,
                "البرنامج غير مُفعل",
                "يجب تفعيل البرنامج أولاً من تاب الإعدادات للوصول إلى هذه الميزة."
            )
            # الانتقال إلى تاب الإعدادات
            self.content_widget.setCurrentIndex(7)
            for i, button in enumerate(self.nav_buttons):
                button.setChecked(i == 7)
            return

        # التحقق من صلاحيات المستخدم قبل الانتقال إلى الصفحة المطلوبة
        from controllers.user_controller import UserController

        # التحقق من صلاحية المستخدم للوصول إلى الصفحة المطلوبة
        if index in self.required_permissions and self.current_user and 'id' in self.current_user:
            user_id = self.current_user['id']
            permission = self.required_permissions[index]

            # المستخدم admin له جميع الصلاحيات
            if self.current_user.get('username') != 'admin':
                # التحقق من صلاحية المستخدم
                if not UserController.check_permission(user_id, permission, show_message=True, parent_widget=self):
                    # إذا لم يكن لدى المستخدم الصلاحية، لا يتم الانتقال إلى الصفحة
                    # تحديث حالة أزرار التنقل للعودة إلى الصفحة الحالية
                    current_index = self.content_widget.currentIndex()
                    for i, button in enumerate(self.nav_buttons):
                        button.setChecked(i == current_index)
                    return

        # الانتقال إلى الصفحة المطلوبة (الصفحات محملة مسبقاً)
        self.content_widget.setCurrentIndex(index)

        # تحديث حالة أزرار التنقل
        for i, button in enumerate(self.nav_buttons):
            button.setChecked(i == index)

        # تأجيل تحديث الصفحة لتحسين الاستجابة والسماح للواجهة بالعرض أولاً
        QTimer.singleShot(50, lambda: self.immediate_page_refresh(index))

    def immediate_page_refresh(self, index):
        """تحديث فوري وسريع للصفحة"""
        try:
            # التحقق من أن الصفحة لا تزال هي الصفحة الحالية
            if self.content_widget.currentIndex() != index:
                return  # المستخدم انتقل إلى صفحة أخرى
                
            current_page = self.content_widget.currentWidget()
            
            # التحقق من وجود وظيفة تحديث في الصفحة وتنفيذها
            if current_page and hasattr(current_page, 'refresh_page') and callable(current_page.refresh_page):
                # في أول مرة تحميل فيها الصفحة، قم بتحميل البيانات
                if hasattr(current_page, '_initial_refresh_needed') and current_page._initial_refresh_needed:
                    current_page._initial_refresh_needed = False
                    # تأجيل أطول لضمان عرض الصفحة أولاً ثم تحميل البيانات
                    if hasattr(current_page, 'refresh_page'):
                        QTimer.singleShot(300, lambda: current_page.refresh_page() if current_page else None)
                else:
                    # تحديث عادي
                    if hasattr(current_page, 'refresh_page'):
                        current_page.refresh_page()
            
            # تحديث حالة الاتصال بقاعدة البيانات في شريط الحالة
            self.update_db_status()
            
        except Exception as e:
            # تجاهل أخطاء تحديث الصفحة لتجنب تعطيل التطبيق
            pass

    def go_to_sales_and_focus_search(self):
        """الانتقال إلى تاب المبيعات وتنشيط شريط البحث عند الضغط على F12"""
        try:
            # التحقق من التفعيل أولاً
            if not self.license_manager.is_activated():
                QMessageBox.warning(
                    self,
                    "البرنامج غير مُفعل",
                    "يجب تفعيل البرنامج أولاً من تاب الإعدادات للوصول إلى هذه الميزة."
                )
                return

            # التحقق من صلاحيات المستخدم للوصول إلى تاب المبيعات
            from controllers.user_controller import UserController

            if self.current_user and 'id' in self.current_user:
                user_id = self.current_user['id']
                permission = "عرض المبيعات"

                # المستخدم admin له جميع الصلاحيات
                if self.current_user.get('username') != 'admin':
                    # التحقق من صلاحية المستخدم
                    if not UserController.check_permission(user_id, permission, show_message=True, parent_widget=self):
                        return

            # التحقق من التاب الحالي
            current_index = self.content_widget.currentIndex()

            # إذا لم نكن في تاب المبيعات، انتقل إليه
            if current_index != 0:
                self.change_page(0)

            # تنشيط شريط البحث في تاب المبيعات
            sales_page = self.content_widget.widget(0)
            if sales_page and hasattr(sales_page, 'focus_search_input'):
                # إذا كنا بالفعل في تاب المبيعات، تنشيط فوري
                if current_index == 0:
                    sales_page.focus_search_input()
                else:
                    # إذا انتقلنا للتو إلى التاب، انتظار قصير
                    QTimer.singleShot(100, sales_page.focus_search_input)

        except Exception as e:
            pass

    def check_license_and_setup_tabs(self):
        """التحقق من التفعيل وإعداد التبويبات مع معالجة الأخطاء"""
        try:
            # محاولة التحقق من التفعيل مع إعادة التحميل
            is_activated = self.license_manager.is_activated(force_reload=True)
            
            print(f"حالة التفعيل: {'\u0645\u0641\u0639\u0644' if is_activated else '\u063a\u064a\u0631 \u0645\u0641\u0639\u0644'}")
            
            if is_activated:
                self.enable_all_tabs()
            else:
                self.disable_all_tabs_except_settings()
                
        except Exception as e:
            print(f"خطأ في التحقق من التفعيل: {str(e)}")
            # في حالة حدوث خطأ، اعتبر البرنامج غير مفعل للأمان
            self.disable_all_tabs_except_settings()
            
    def setup_license_check_timer(self):
        """إعداد مؤقت التحقق من التفعيل دورياً"""
        try:
            self.license_check_timer = QTimer(self)
            self.license_check_timer.timeout.connect(self.periodic_license_check)
            # التحقق من التفعيل كل 30 ثانية
            self.license_check_timer.start(30000)
            print("تم بدء مؤقت التحقق من التفعيل دورياً")
        except Exception as e:
            print(f"خطأ في إعداد مؤقت التحقق من التفعيل: {str(e)}")
            
    def periodic_license_check(self):
        """التحقق الدوري من حالة التفعيل"""
        try:
            # التحقق من التفعيل بشكل هادئ دون إعادة تحميل فورية
            current_activation_status = self.license_manager.is_activated()
            
            # إذا فشل التحقق، حاول مرة أخرى مع إعادة تحميل
            if not current_activation_status:
                current_activation_status = self.license_manager.is_activated(force_reload=True)
            
            # التحقق من تغيير حالة التفعيل
            if not hasattr(self, '_last_activation_status'):
                self._last_activation_status = current_activation_status
                
            if self._last_activation_status != current_activation_status:
                print(f"تغيير حالة التفعيل: {'\u0645\u0641\u0639\u0644' if current_activation_status else '\u063a\u064a\u0631 \u0645\u0641\u0639\u0644'}")
                
                if current_activation_status:
                    self.enable_all_tabs()
                else:
                    self.disable_all_tabs_except_settings()
                    
                self._last_activation_status = current_activation_status
                
        except Exception as e:
            print(f"خطأ في التحقق الدوري من التفعيل: {str(e)}")

    def enable_all_tabs(self):
        """تفعيل جميع التبويبات"""
        try:
            for i, button in enumerate(self.nav_buttons):
                button.setEnabled(True)
                button.setStyleSheet("")  # إزالة أي تنسيق خاص
            print("تم تفعيل جميع التبويبات")
        except Exception as e:
            print(f"خطأ في تفعيل التبويبات: {str(e)}")

    def disable_all_tabs_except_settings(self):
        """تعطيل جميع التبويبات عدا تاب الإعدادات"""
        try:
            for i, button in enumerate(self.nav_buttons):
                if i == 7:  # تاب الإعدادات (آخر تاب)
                    button.setEnabled(True)
                    button.setStyleSheet("")
                else:
                    button.setEnabled(False)
                    button.setStyleSheet("""
                        QPushButton {
                            background-color: #f8f9fa;
                            color: #6c757d;
                            border: 1px solid #dee2e6;
                        }
                        QPushButton:hover {
                            background-color: #f8f9fa;
                            color: #6c757d;
                        }
                    """)
    
            # الانتقال إلى تاب الإعدادات إذا كان المستخدم في تاب آخر
            current_index = self.content_widget.currentIndex()
            if current_index != 7:  # إذا لم يكن في تاب الإعدادات
                self.change_page(7)
                
            print("تم تعطيل جميع التبويبات عدا تاب الإعدادات")
        except Exception as e:
            print(f"خطأ في تعطيل التبويبات: {str(e)}")
            
    def refresh_license_status(self):
        """إعادة فحص حالة الترخيص يدوياً (يمكن استدعاؤها من صفحة الإعدادات)"""
        print("إعادة فحص حالة الترخيص يدوياً...")
        self.check_license_and_setup_tabs()

    def update_datetime(self):
        """تحديث عرض الوقت والتاريخ في شريط الحالة"""
        now = datetime.datetime.now()

        # تهيئة التاريخ بالتنسيق العربي
        date_str = now.strftime("%Y/%m/%d")

        # تهيئة الوقت بتنسيق AM/PM
        hour = now.hour
        minute = now.minute
        second = now.second
        period = "مساءً" if hour >= 12 else "صباحًا"

        # تحويل الساعة إلى نظام 12 ساعة
        if hour > 12:
            hour -= 12
        elif hour == 0:
            hour = 12

        # تنسيق الوقت بالشكل المناسب
        time_str = f"{hour:02d}:{minute:02d}:{second:02d} {period}"

        # عرض التاريخ والوقت في شريط الحالة
        self.status_datetime.setText(f"التاريخ: {date_str}  |  الوقت: {time_str}")

    def update_db_status(self):
        """تحديث حالة الاتصال بقاعدة البيانات في شريط الحالة"""
        success, _ = DatabaseController.test_connection()

        # Verificar si el atributo status_db existe antes de usarlo
        if hasattr(self, 'status_db'):
            if success:
                self.status_db.setText("حالة قاعدة البيانات: متصلة")
                self.status_db.setStyleSheet("color: green;")
            else:
                self.status_db.setText("حالة قاعدة البيانات: غير متصلة")
                self.status_db.setStyleSheet("color: red;")

    def open_calculator(self):
        """فتح الآلة الحاسبة الخاصة بنظام ويندوز"""
        try:
            # إعداد معاملات subprocess لإخفاء نافذة cmd على Windows
            kwargs = {}
            if os.name == 'nt':  # Windows only
                kwargs['creationflags'] = subprocess.CREATE_NO_WINDOW

            subprocess.Popen("calc.exe", **kwargs)
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"لا يمكن فتح الآلة الحاسبة: {str(e)}")

    def open_barcode_window(self):
        """فتح نافذة الباركود"""
        try:
            from views.barcode_window import BarcodeWindow
            barcode_window = BarcodeWindow(self)
            barcode_window.exec_()
        except Exception as e:
            QMessageBox.warning(self, "خطأ", f"لا يمكن فتح نافذة الباركود: {str(e)}")

    def show_invoice_details(self, invoice_id):
        """عرض تفاصيل الفاتورة - الانتقال إلى تاب الفواتير وعرض التفاصيل"""
        try:
            # الانتقال إلى تاب الفواتير (الفهرس 1)
            self.change_page(1)

            # الحصول على تاب الفواتير
            invoices_page = self.content_widget.widget(1)

            # التحقق من وجود تاب الفواتير ووظيفة عرض التفاصيل
            if invoices_page and hasattr(invoices_page, 'view_invoice_details'):
                # استدعاء وظيفة عرض تفاصيل الفاتورة
                invoices_page.view_invoice_details(invoice_id)
            else:
                QMessageBox.warning(
                    self,
                    "خطأ",
                    "لا يمكن الوصول إلى تاب الفواتير لعرض التفاصيل"
                )
        except Exception as e:
            print(f"[ERROR] خطأ في عرض تفاصيل الفاتورة: {str(e)}")
            QMessageBox.critical(
                self,
                "خطأ",
                f"حدث خطأ أثناء عرض تفاصيل الفاتورة: {str(e)}"
            )

    def show_user_context_menu(self, position):
        """عرض قائمة السياق لاسم المستخدم"""
        # التحقق من وجود مستخدم مسجل دخول
        if not self.current_user or 'username' not in self.current_user:
            return

        # إنشاء قائمة السياق
        context_menu = QMenu(self)
        context_menu.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # إضافة معلومات المستخدم كعنوان
        user_fullname = self.current_user.get('full_name', self.current_user['username'])
        user_info_action = context_menu.addAction(f"👤  {user_fullname}")
        if user_info_action:
            user_info_action.setEnabled(False)  # جعله غير قابل للنقر (عنوان فقط)

        context_menu.addSeparator()

        # إضافة زر تسجيل الخروج
        logout_action = context_menu.addAction("🚪  تسجيل خروج")
        if logout_action:
            logout_action.setToolTip("تسجيل الخروج من النظام")
            logout_action.triggered.connect(self.logout)

        # تطبيق الأنماط على قائمة السياق
        context_menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 6px;
                padding: 5px;
                font-size: 12px;
                min-width: 180px;
            }
            QMenu::item {
                padding: 8px 20px;
                border-radius: 4px;
                margin: 2px;
            }
            QMenu::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
            QMenu::item:disabled {
                color: #666;
                background-color: #f5f5f5;
                font-weight: bold;
            }
            QMenu::separator {
                height: 1px;
                background-color: #eee;
                margin: 5px 10px;
            }
        """)

        # عرض قائمة السياق
        context_menu.exec_(self.status_username.mapToGlobal(position))

    def logout(self):
        """تسجيل خروج المستخدم"""
        # عرض رسالة تأكيد
        reply = QMessageBox.question(
            self,
            "تأكيد تسجيل الخروج",
            "هل أنت متأكد من تسجيل الخروج من النظام؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # إغلاق النافذة الحالية
            self.close()

            # إنشاء نافذة تسجيل دخول جديدة
            from views.login import LoginWindow
            login_window = LoginWindow()

            # متغير لتخزين بيانات المستخدم الجديد
            user_data = [None]

            # ربط إشارة نجاح تسجيل الدخول
            def on_login_successful(user):
                user_data[0] = user

            login_window.loginSuccessful.connect(on_login_successful)

            # عرض نافذة تسجيل الدخول
            login_result = login_window.exec_()

            # التحقق من نتيجة تسجيل الدخول
            if login_result == LoginWindow.Accepted and user_data[0]:
                # إنشاء نافذة رئيسية جديدة مع المستخدم الجديد
                new_window = MainWindow(user_data[0])
                new_window.show()
            else:
                # إذا تم إلغاء تسجيل الدخول، إغلاق التطبيق
                QApplication.quit()

    def apply_styles(self):
        """تطبيق الأنماط على مكونات النافذة"""
        # استخدام التنسيقات من ملف الستايلات
        # تطبيق الأنماط على النافذة الرئيسية وجميع العناصر الفرعية
        self.setStyleSheet(AppStyles.get_main_style())

        # تطبيق الأنماط على جميع الأزرار في التطبيق
        for widget in self.findChildren(QPushButton):
            # الحفاظ على تنسيقات أزرار التنقل الخاصة
            if widget.objectName() != "nav_button":
                # تطبيق الحد الأدنى للعرض والارتفاع على جميع الأزرار
                widget.setMinimumWidth(80)
                widget.setMinimumHeight(24)

        # إضافة tooltips تلقائية للأزرار التي لا تحتوي على tooltips
        self.add_automatic_tooltips()

    def add_automatic_tooltips(self):
        """إضافة tooltips تلقائية للأزرار التي لا تحتوي على tooltips"""
        # قاموس tooltips افتراضية بناءً على نص الزر
        default_tooltips = {
            # أزرار عامة
            "إغلاق": "إغلاق النافذة الحالية",
            "حفظ": "حفظ البيانات المدخلة",
            "إلغاء": "إلغاء العملية والعودة",
            "تحديث": "تحديث البيانات المعروضة",
            "بحث": "البحث في البيانات",
            "تصدير": "تصدير البيانات إلى ملف",
            "طباعة": "طباعة البيانات",
            "حذف": "حذف العنصر المحدد",
            "تعديل": "تعديل العنصر المحدد",
            "إضافة": "إضافة عنصر جديد",
            "اختيار": "اختيار العنصر المحدد",
            "موافق": "تأكيد العملية",
            "نعم": "الموافقة على الإجراء",
            "لا": "رفض الإجراء",
            "تطبيق": "تطبيق التغييرات",
            "استعادة": "استعادة الإعدادات الافتراضية",

            # أزرار خاصة بالمبيعات
            "عملية بيع جديدة": "بدء عملية بيع جديدة",
            "إضافة للفاتورة": "إضافة المنتج إلى الفاتورة",
            "إزالة من الفاتورة": "إزالة المنتج من الفاتورة",

            # أزرار خاصة بالمنتجات
            "إضافة للمفضلة": "إضافة المنتج إلى المفضلة",
            "إزالة من المفضلة": "إزالة المنتج من المفضلة",
            "تحديث المخزون": "تحديث كمية المخزون",

            # أزرار خاصة بالتقارير
            "عرض التقرير": "عرض التقرير المحدد",
            "تصدير PDF": "تصدير التقرير كملف PDF",
            "تصدير Excel": "تصدير التقرير كملف Excel",

            # أزرار خاصة بالإعدادات
            "حفظ الإعدادات": "حفظ جميع الإعدادات",
            "استعادة الافتراضي": "استعادة الإعدادات الافتراضية",
            "تصدير الإعدادات": "تصدير الإعدادات إلى ملف",
            "استيراد الإعدادات": "استيراد الإعدادات من ملف"
        }

        # البحث عن جميع الأزرار في التطبيق
        for widget in self.findChildren(QPushButton):
            # تخطي الأزرار التي تحتوي بالفعل على tooltips
            if widget.toolTip():
                continue

            # تخطي أزرار المنتجات المفضلة لأنها تحتوي على tooltips خاصة
            if widget.objectName() == "favorite_product_btn":
                continue

            button_text = widget.text().strip()

            # إزالة الرموز التعبيرية من النص للمطابقة
            clean_text = ""
            for char in button_text:
                if ord(char) < 127:  # الاحتفاظ بالأحرف الأساسية فقط
                    clean_text += char
            clean_text = clean_text.strip()

            # البحث عن tooltip مناسب
            tooltip = None
            for key, value in default_tooltips.items():
                if key in clean_text or clean_text in key:
                    tooltip = value
                    break

            # إذا لم نجد tooltip محدد، نستخدم tooltip عام
            if not tooltip and clean_text:
                tooltip = f"تنفيذ إجراء: {clean_text}"

            # تطبيق tooltip إذا وجد
            if tooltip:
                widget.setToolTip(tooltip)

    def update_user_info(self):
        """تحديث معلومات المستخدم في شريط الحالة وتحديث حالة أزرار التنقل"""
        if self.current_user and 'username' in self.current_user:
            user_fullname = self.current_user.get('full_name', self.current_user['username'])
            self.status_username.setText(f"المستخدم: {user_fullname}")

            # تحديث حالة تفعيل أزرار التنقل بناءً على صلاحيات المستخدم
            self.update_nav_buttons_state()
        else:
            self.status_username.setText("المستخدم: غير معروف")

    def update_nav_buttons_state(self):
        """تحديث حالة تفعيل أزرار التنقل بناءً على صلاحيات المستخدم"""
        # التحقق من وجود مستخدم حالي
        if not self.current_user or 'id' not in self.current_user:
            return

        # المستخدم admin له جميع الصلاحيات
        if self.current_user.get('username') == 'admin':
            # تفعيل جميع الأزرار
            for button in self.nav_buttons:
                button.setEnabled(True)
            return

        # استيراد وحدة التحكم بالمستخدمين
        from controllers.user_controller import UserController

        user_id = self.current_user['id']

        # تحديث حالة كل زر بناءً على صلاحيات المستخدم
        for i, button in enumerate(self.nav_buttons):
            if i in self.required_permissions:
                permission = self.required_permissions[i]
                has_permission = UserController.check_permission(user_id, permission)

                # تفعيل أو تعطيل الزر بناءً على الصلاحية
                button.setEnabled(has_permission)

                # تغيير لون الزر المعطل ليكون رمادي
                if not has_permission:
                    button.setStyleSheet("color: #999999; background-color: #f0f0f0;")
                else:
                    button.setStyleSheet("")  # إعادة تعيين النمط الافتراضي

    def refresh_all_pages(self):
        """تحديث جميع الصفحات في التطبيق"""
        try:
            # تحديث جميع الصفحات في التطبيق
            for i in range(self.content_widget.count()):
                widget = self.content_widget.widget(i)

                # تحديث الصفحة إذا كانت تحتوي على دالة refresh_page
                if widget and hasattr(widget, 'refresh_page') and callable(widget.refresh_page):
                    widget.refresh_page()

        except Exception as e:
            pass

    def setup_system_tray(self):
        """إعداد أيقونة النظام (System Tray)"""
        # التحقق من دعم النظام للـ System Tray
        if not QSystemTrayIcon.isSystemTrayAvailable():
            QMessageBox.critical(
                self,
                "خطأ في النظام",
                "النظام لا يدعم أيقونة شريط المهام. لن تتمكن من تشغيل البرنامج في الخلفية."
            )
            return

        # إنشاء أيقونة للـ System Tray
        self.tray_icon = QSystemTrayIcon(self)

        # إنشاء أيقونة افتراضية إذا لم تكن موجودة
        tray_pixmap = QPixmap(32, 32)
        tray_pixmap.fill(QColor(0, 120, 215))  # لون أزرق

        # رسم حرف S على الأيقونة
        painter = QPainter(tray_pixmap)
        painter.setPen(QColor(255, 255, 255))
        painter.setFont(QFont("Arial", 20, QFont.Bold))
        painter.drawText(tray_pixmap.rect(), Qt.AlignmentFlag.AlignCenter, "S")
        painter.end()

        self.tray_icon.setIcon(QIcon(tray_pixmap))
        self.tray_icon.setToolTip("Smart Manager - يعمل في الخلفية")

        # إنشاء قائمة السياق للـ System Tray
        tray_menu = QMenu()
        tray_menu.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # إضافة خيار إظهار النافذة
        show_action = tray_menu.addAction("🔍  إظهار النافذة")
        if show_action:
            show_action.triggered.connect(self.show_window)

        # إضافة خيار إخفاء النافذة
        hide_action = tray_menu.addAction("🙈  إخفاء النافذة")
        if hide_action:
            hide_action.triggered.connect(self.hide_window)

        tray_menu.addSeparator()

        # إضافة خيار إغلاق البرنامج نهائياً
        quit_action = tray_menu.addAction("❌  إغلاق البرنامج")
        if quit_action:
            quit_action.triggered.connect(self.quit_application)

        # تطبيق الأنماط على قائمة السياق
        tray_menu.setStyleSheet("""
            QMenu {
                background-color: white;
                border: 1px solid #ddd;
                border-radius: 6px;
                padding: 5px;
                font-size: 12px;
                min-width: 180px;
            }
            QMenu::item {
                padding: 8px 20px;
                border-radius: 4px;
                margin: 2px;
            }
            QMenu::item:selected {
                background-color: #e3f2fd;
                color: #1976d2;
            }
        """)

        self.tray_icon.setContextMenu(tray_menu)

        # ربط النقر المزدوج بإظهار النافذة
        self.tray_icon.activated.connect(self.tray_icon_activated)

        # إظهار أيقونة الـ System Tray
        self.tray_icon.show()

    def setup_signal_checker(self):
        """إعداد مؤقت للتحقق من إشارات الإظهار من نسخ أخرى"""
        # إنشاء مدير النسخة الواحدة للتحقق من الإشارات
        self.instance_manager = SingleInstanceManager("SmartManager")

        # إنشاء مؤقت للتحقق من إشارات الإظهار كل ثانية
        self.signal_timer = QTimer(self)
        self.signal_timer.timeout.connect(self.check_show_signals)
        self.signal_timer.start(1000)  # التحقق كل ثانية

    def check_show_signals(self):
        """التحقق من إشارات الإظهار من نسخ أخرى"""
        try:
            if hasattr(self, 'instance_manager') and self.instance_manager.check_show_signal():
                # تم استلام إشارة إظهار، إظهار النافذة
                self.show_window()
        except Exception as e:
            # تجاهل الأخطاء لتجنب تعطيل التطبيق
            pass

    def tray_icon_activated(self, reason):
        """معالجة النقر على أيقونة الـ System Tray"""
        if reason == QSystemTrayIcon.ActivationReason.DoubleClick:
            self.show_window()

    def show_window(self):
        """إظهار النافذة الرئيسية مع التركيز القوي"""
        # إظهار النافذة إذا كانت مخفية
        if not self.isVisible():
            self.show()

        # إذا كانت النافذة مصغرة، استعادتها
        if self.isMinimized():
            self.showNormal()
            # تطبيق الحجم المناسب بعد الاستعادة
            if hasattr(self, 'normal_window_width'):
                QTimer.singleShot(100, self.apply_normal_size)

        # رفع النافذة إلى المقدمة
        self.raise_()

        # تفعيل النافذة والتركيز عليها
        self.activateWindow()

        # التأكد من أن النافذة في المقدمة (Windows specific)
        if os.name == 'nt':
            try:
                import ctypes
                from ctypes import wintypes

                # الحصول على handle النافذة
                hwnd = int(self.winId())

                # إحضار النافذة إلى المقدمة بقوة
                ctypes.windll.user32.SetForegroundWindow(hwnd)
                ctypes.windll.user32.BringWindowToTop(hwnd)
                ctypes.windll.user32.ShowWindow(hwnd, 9)  # SW_RESTORE
                ctypes.windll.user32.SetFocus(hwnd)

                # محاولة إضافية للتأكد من التركيز
                ctypes.windll.user32.SetActiveWindow(hwnd)

            except Exception as e:
                pass

        # تأخير قصير ثم محاولة أخرى للتركيز
        QTimer.singleShot(100, self._ensure_focus)

        # إعادة تشغيل مؤقت تحديث الوقت عند إظهار النافذة
        if hasattr(self, 'datetime_timer') and self.datetime_timer and not self.datetime_timer.isActive():
            self.datetime_timer.start(1000)

    def _ensure_focus(self):
        """التأكد من التركيز على النافذة بعد تأخير قصير"""
        try:
            self.raise_()
            self.activateWindow()
            self.setFocus()
        except Exception:
            pass

    def hide_window(self):
        """إخفاء النافذة الرئيسية"""
        self.hide()

        # تحسين الأداء عند العمل في الخلفية
        # إيقاف مؤقت تحديث الوقت مؤقتاً لتوفير الموارد
        if hasattr(self, 'datetime_timer') and self.datetime_timer:
            self.datetime_timer.stop()

    def quit_application(self):
        """إغلاق البرنامج نهائياً"""
        # عرض رسالة تأكيد
        reply = QMessageBox.question(
            self,
            "تأكيد الإغلاق",
            "هل أنت متأكد من إغلاق البرنامج نهائياً؟",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # إخفاء أيقونة الـ System Tray
            if hasattr(self, 'tray_icon'):
                self.tray_icon.hide()

            # إغلاق التطبيق
            QApplication.quit()

    def closeEvent(self, a0: Optional[QCloseEvent]) -> None:
        """معالجة إغلاق النافذة الرئيسية"""
        # إنشاء رسالة سؤال مخصصة مع خيارات متعددة
        msg_box = QMessageBox(self)
        msg_box.setWindowTitle("إغلاق البرنامج")
        msg_box.setIcon(QMessageBox.Question)

        # تعريف متغيرات الأزرار مبكراً
        close_button = None
        minimize_button = None
        cancel_button = None

        # التحقق من وجود الـ System Tray لتحديد الخيارات المتاحة
        if hasattr(self, 'tray_icon') and self.tray_icon.isVisible():
            msg_box.setText("الخروج من النظام")
            msg_box.setInformativeText("يمكنك اختيار اغلاق البرنامج او العمل في الخلفية")

        # إضافة الأزرار المخصصة
            close_button = msg_box.addButton("إغلاق البرنامجً", QMessageBox.DestructiveRole)
            minimize_button = msg_box.addButton("تصغير للخلفية", QMessageBox.AcceptRole)
            cancel_button = msg_box.addButton("إلغاء", QMessageBox.RejectRole)

            # تعيين الزر الافتراضي
            msg_box.setDefaultButton(minimize_button)
        else:
            msg_box.setText("هل أنت متأكد من إغلاق البرنامج؟")
            msg_box.setInformativeText("ملاحظة: النظام لا يدعم تشغيل البرنامج في الخلفية.")

            # إضافة الأزرار للحالة بدون System Tray
            close_button = msg_box.addButton("إغلاق البرنامج", QMessageBox.AcceptRole)
            cancel_button = msg_box.addButton("إلغاء", QMessageBox.RejectRole)

            # تعيين الزر الافتراضي
            msg_box.setDefaultButton(cancel_button)

        # تطبيق التنسيق المناسب للرسالة
        msg_box.setStyleSheet("""
            QMessageBox {
                background-color: white;
                font-size: 12px;
            }
            QMessageBox QLabel {
                color: #333;
                font-size: 12px;
            }
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 4px;
                padding: 8px 16px;
                font-size: 11px;
                min-width: 100px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
        """)

        # عرض الرسالة والحصول على الاختيار
        msg_box.exec_()
        clicked_button = msg_box.clickedButton()

        # معالجة الاختيار
        if hasattr(self, 'tray_icon') and self.tray_icon.isVisible():
            if clicked_button == close_button:
                # إغلاق البرنامج نهائياً
                if hasattr(self, 'tray_icon'):
                    self.tray_icon.hide()
                if a0:
                    a0.accept()
                QApplication.quit()
            elif clicked_button == minimize_button:
                # تصغير للخلفية
                self.hide()

                # تحسين الأداء عند العمل في الخلفية
                if hasattr(self, 'datetime_timer') and self.datetime_timer:
                    self.datetime_timer.stop()

                # إظهار إشعار للمستخدم
                self.tray_icon.showMessage(
                    "Smart Manager",
                    "تم تصغير البرنامج إلى شريط المهام. يمكنك الوصول إليه من خلال النقر على الأيقونة.",
                    QSystemTrayIcon.MessageIcon.Information,
                    3000  # مدة الإشعار بالميلي ثانية
                )

                # منع إغلاق النافذة
                if a0:
                    a0.ignore()
            else:
                # إلغاء - البقاء في النافذة الحالية
                if a0:
                    a0.ignore()
        else:
            # للحالة بدون System Tray
            if clicked_button == close_button:
                if a0:
                    a0.accept()
            else:
                if a0:
                    a0.ignore()

    # في MainWindow class أضف دالة لمزامنة إعدادات المبيعات
    def refresh_sales_view(self):
        # تحديث صفحة المبيعات إذا كانت مفتوحة
        for i in range(self.content_widget.count()):
            widget = self.content_widget.widget(i)
            if widget and widget.__class__.__name__ == 'SalesView':
                if hasattr(widget, 'refresh_page'):
                    widget.refresh_page()
                break


# Python 3 handles Unicode by default, so no need to manually set encoding
# The following is only kept for documentation purposes:
# import importlib
# importlib.reload(sys)  # Python 3 way to reload modules if needed

if __name__ == "__main__":
    # التحقق من عدم تشغيل نسخة أخرى من البرنامج
    instance_manager = SingleInstanceManager("SmartManager")

    if instance_manager.is_already_running():
        # إرسال إشارة لإظهار النافذة الموجودة
        if not instance_manager.send_show_signal():
            # في حالة فشل إرسال الإشارة، عرض رسالة تحذير
            temp_app = QApplication(sys.argv)
            QMessageBox.warning(
                None,
                "البرنامج يعمل بالفعل",
                "البرنامج يعمل بالفعل. تأكد من شريط المهام أو أعد تشغيل الجهاز."
            )
        sys.exit(0)

    # إنشاء ملف القفل
    if not instance_manager.create_lock():
        temp_app = QApplication(sys.argv)
        QMessageBox.critical(
            None,
            "خطأ في بدء التشغيل",
            "لا يمكن إنشاء ملف القفل.\n\nتأكد من صلاحيات الكتابة في مجلد النظام المؤقت."
        )
        sys.exit(1)

    # تعريف معالج الاستثناءات غير المعالجة
    def exception_hook(exctype, value, traceback):
        sys.__excepthook__(exctype, value, traceback)
        if not DEBUG_MODE:
            # طباعة رسالة الخطأ في وحدة التحكم أيضًا
            console_print(f"CRITICAL ERROR: {str(value)}")
            # عرض رسالة خطأ للمستخدم
            error_msg = f"حدث خطأ غير متوقع: {str(value)}\n\nتم حفظ تفاصيل الخطأ في ملف السجل."
            QMessageBox.critical(None, "خطأ غير متوقع", error_msg)

    # تعيين معالج الاستثناءات
    sys.excepthook = exception_hook

    app = QApplication(sys.argv)

    # تثبيت معالج الرسائل المخصص لمنع ظهور رسائل التحذير
    qInstallMessageHandler(suppress_qt_warnings)

    app.setLayoutDirection(Qt.LayoutDirection.RightToLeft)  # جعل التطبيق للغة العربية من اليمين لليسار

    # تحميل ملف الخط Readex Pro مباشرة من مجلد البرنامج
    try:
        font_path = resource_path("ReadexPro.ttf")
    except Exception:
        font_path = os.path.join(get_base_dir(), "ReadexPro.ttf")
    if os.path.exists(font_path):
        # Intentar cargar la fuente varias veces para evitar errores
        font_id = -1
        for i in range(3):  # Intentar 3 veces
            font_id = QFontDatabase.addApplicationFont(font_path)
            if font_id != -1:
                break

        if font_id != -1:
            font_families = QFontDatabase.applicationFontFamilies(font_id)
            if font_families:
                custom_font_family = font_families[0]
                # تخزين اسم العائلة في متغير عام لاستخدامه في styles.py
                AppStyles.LOADED_CUSTOM_FONT = custom_font_family

    # تهيئة وتطبيق الخط المناسب للنظام
    system_font = AppStyles.initialize_fonts()
    font = app.font()
    font.setFamily(system_font)
    font.setPointSize(10)
    app.setFont(font)

    # تهيئة قاعدة البيانات
    success, message = DatabaseController.initialize_database()
    if not success:
        QMessageBox.critical(None, "خطأ في تهيئة قاعدة البيانات", message)
        sys.exit(1)

    # إنشاء النافذة الرئيسية

    # عرض نافذة تسجيل الدخول قبل عرض النافذة الرئيسية
    login_window = LoginWindow()

    # متغير لتخزين بيانات المستخدم بعد تسجيل الدخول
    user_data = [None]  # استخدام قائمة لتخزين المرجع

    # ربط إشارة نجاح تسجيل الدخول بدالة لتخزين بيانات المستخدم
    def on_login_successful(user):
        user_data[0] = user

    # ربط الإشارة بالدالة
    login_window.loginSuccessful.connect(on_login_successful)

    # عرض نافذة تسجيل الدخول وانتظار النتيجة
    login_result = login_window.exec_()

    # التحقق من نتيجة تسجيل الدخول
    if login_result != LoginWindow.Accepted or not user_data[0]:
        sys.exit(0)

    # إنشاء النافذة الرئيسية مع بيانات المستخدم
    window = MainWindow(user_data[0])

    # إظهار النافذة بسرعة أولاً
    window.show()
    
    # تأجيل تطبيق التركيز والرفع لتحسين سرعة الظهور
    def apply_window_focus():
        try:
            window.raise_()
            window.activateWindow()
            
            # تطبيق التركيز القوي على Windows
            if os.name == 'nt':
                try:
                    import ctypes
                    hwnd = int(window.winId())
                    ctypes.windll.user32.SetForegroundWindow(hwnd)
                    ctypes.windll.user32.BringWindowToTop(hwnd)
                    ctypes.windll.user32.SetFocus(hwnd)
                except Exception:
                    pass
        except Exception:
            pass
    
    # تأجيل تطبيق التركيز لتحسين سرعة عرض النافذة
    QTimer.singleShot(100, apply_window_focus)

    # تنفيذ التطبيق وإرجاع رمز الخروج
    exit_code = app.exec_()

    # تنظيف ملف القفل عند إغلاق البرنامج
    instance_manager.remove_lock()

    # استعادة قنوات الإخراج الأصلية وإغلاق ملف السجل
    if not DEBUG_MODE and log_file is not None:
        # استعادة قنوات الإخراج الأصلية
        sys.stdout = original_stdout
        sys.stderr = original_stderr

        # إغلاق ملف السجل
        log_file.close()

    sys.exit(exit_code)