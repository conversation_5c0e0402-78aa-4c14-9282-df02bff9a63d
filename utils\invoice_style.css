/* أنماط الفاتورة المركزية */
body {
    font-family: 'Readex Pro', 'Tahoma', sans-serif;
    margin: 0 auto !important;
    padding: 0;
    direction: rtl;
    max-width: 100%;
    text-align: center !important;
}

.header {
    text-align: center !important;
    margin: 0 auto !important;
    padding: 5px;
    width: 100%;
}

.company-name {
    text-align: center !important;
    margin: 0 auto !important;
    font-weight: bold;
    font-size: 16px;
}

.company-info {
    text-align: center !important;
    margin: 0 auto !important;
    font-size: 14px;
}

.invoice-info {
    margin: 5px auto !important;
    padding: 5px;
    text-align: center !important;
}

.invoice-info div {
    margin: 5px auto !important;
    padding: 2px;
    text-align: center !important;
}

center {
    display: block;
    text-align: center !important;
    margin: 0 auto !important;
    width: 100%;
}

table {
    width: 100%;
    border-collapse: collapse;
    margin: 5px auto !important;
    text-align: center !important;
}

th, td {
    border: 1px solid #000;
    padding: 3px;
    text-align: center !important;
}

.product-name-cell {
    text-align: center !important;
    word-break: break-word;
    white-space: normal;
    vertical-align: top;
}

.total-section {
    margin-top: 5px;
    text-align: center !important;
}

.total-box {
    border: 1px solid #000;
    padding: 5px;
    margin: 5px auto !important;
    width: 85%;
    font-weight: bold;
    text-align: center !important;
}

.notes {
    margin-top: 5px;
    text-align: center !important;
}