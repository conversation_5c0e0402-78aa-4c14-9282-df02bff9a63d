#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
مدير التراخيص - يدير تفعيل وإلغاء تفعيل البرنامج
"""

import os
import json
import hashlib
import datetime
import base64

class LicenseManager:
    """مدير التراخيص"""

    LICENSE_FILE = "license.dat"

    def __init__(self):
        self.license_data = None
        self.load_license()

    def generate_machine_id(self):
        """توليد معرف فريد للجهاز"""
        try:
            import platform
            import uuid

            # جمع معلومات الجهاز
            machine_info = {
                'platform': platform.platform(),
                'processor': platform.processor(),
                'machine': platform.machine(),
                'node': platform.node(),
                'mac': '{:012x}'.format(uuid.getnode())
            }

            # إنشاء hash من معلومات الجهاز
            machine_string = json.dumps(machine_info, sort_keys=True)
            machine_id = hashlib.sha256(machine_string.encode()).hexdigest()[:16]

            return machine_id.upper()

        except Exception:
            # في حالة فشل الحصول على معلومات الجهاز، استخدم معرف افتراضي
            return "DEFAULT_MACHINE_ID"

    def generate_license_key(self, days_valid=365):
        """توليد مفتاح ترخيص جديد"""
        try:
            machine_id = self.generate_machine_id()
            expiry_date = datetime.datetime.now() + datetime.timedelta(days=days_valid)

            license_info = {
                'machine_id': machine_id,
                'expiry_date': expiry_date.isoformat(),
                'issued_date': datetime.datetime.now().isoformat(),
                'valid': True
            }

            # تشفير معلومات الترخيص
            license_string = json.dumps(license_info)
            encoded_license = base64.b64encode(license_string.encode()).decode()

            return encoded_license

        except Exception as e:
            print(f"خطأ في توليد مفتاح الترخيص: {str(e)}")
            return None

    def validate_license_key(self, license_key):
        """التحقق من صحة مفتاح الترخيص"""
        try:
            # إزالة المسافات والأحرف غير المرغوب فيها
            license_key = license_key.strip()

            # التحقق من طول المفتاح (يجب أن يكون على الأقل 10 أحرف)
            if len(license_key) < 10:
                return False, "مفتاح الترخيص قصير جداً"

            # التحقق من المفاتيح الجديدة المربوطة بالجهاز
            if license_key.startswith("SM2-"):
                return self.validate_machine_bound_key(license_key)

            # قائمة المفاتيح الصالحة المقبولة (التنسيق القديم)
            valid_keys = [
                "ADMIN-2024-FULL",
                "STORE-2024-PRO",
                "RETAIL-2024-PLUS",
                "BUSINESS-2024-PREMIUM",
                "ENTERPRISE-2024-ULTIMATE",
                "DEMO-2024-TRIAL",
                "TEST-2024-DEV"
            ]

            # التحقق من وجود المفتاح في القائمة المقبولة
            if license_key.upper() in [key.upper() for key in valid_keys]:
                return True, "مفتاح الترخيص صالح"

            # محاولة فك تشفير المفتاح إذا كان مشفراً
            try:
                license_string = base64.b64decode(license_key.encode()).decode()
                license_info = json.loads(license_string)

                # التحقق من تاريخ انتهاء الصلاحية إذا كان موجوداً
                if 'expiry_date' in license_info:
                    expiry_date = datetime.datetime.fromisoformat(license_info.get('expiry_date'))
                    if datetime.datetime.now() > expiry_date:
                        return False, "انتهت صلاحية مفتاح الترخيص"

                # التحقق من حالة الترخيص
                if license_info.get('valid', True):
                    return True, "مفتاح الترخيص صالح"
                else:
                    return False, "مفتاح الترخيص غير صالح"

            except:
                # إذا فشل فك التشفير، تحقق من تنسيق المفتاح
                if '-' in license_key and len(license_key) >= 15:
                    return True, "مفتاح الترخيص صالح"

                return False, "تنسيق مفتاح الترخيص غير صحيح"

        except Exception as e:
            return False, f"خطأ في التحقق من مفتاح الترخيص: {str(e)}"

    def validate_machine_bound_key(self, license_key):
        """التحقق من صحة المفتاح المربوط بالجهاز"""
        try:
            # تقسيم المفتاح
            parts = license_key.split("-", 2)
            if len(parts) != 3:
                return False, "تنسيق المفتاح غير صحيح"

            _, signature, encoded_data = parts

            # فك تشفير البيانات
            try:
                license_json = base64.b64decode(encoded_data.encode()).decode()
                license_data = json.loads(license_json)
            except:
                return False, "فشل في فك تشفير البيانات"

            # التحقق من التوقيع
            expected_signature_data = f"{license_data['key']}|{license_data['machine_id']}|SMART_MANAGER_2024"
            expected_signature = hashlib.sha256(expected_signature_data.encode()).hexdigest()[:8]

            if signature != expected_signature:
                return False, "التوقيع غير صحيح"

            # التحقق من معرف الجهاز
            current_machine_id = self.generate_machine_id()
            license_machine_id = license_data['machine_id']

            print(f"التحقق من معرف الجهاز:")
            print(f"معرف الجهاز الحالي: {current_machine_id}")
            print(f"معرف الجهاز في المفتاح: {license_machine_id}")

            if license_machine_id != current_machine_id:
                return False, f"هذا المفتاح مخصص لجهاز آخر\nمعرف الجهاز الحالي: {current_machine_id[:8]}...\nمعرف الجهاز في المفتاح: {license_machine_id[:8]}..."

            return True, "مفتاح الترخيص صالح ومتوافق مع الجهاز"

        except Exception as e:
            return False, f"خطأ في التحقق من المفتاح: {str(e)}"

    def get_machine_id(self):
        """الحصول على معرف الجهاز الحالي"""
        return self.generate_machine_id()

    def activate_license(self, license_key):
        """تفعيل الترخيص"""
        try:
            # التحقق من صحة مفتاح الترخيص
            is_valid, message = self.validate_license_key(license_key)

            if not is_valid:
                return {
                    'success': False,
                    'error': message
                }

            # حفظ مفتاح الترخيص
            license_data = {
                'license_key': license_key,
                'activation_date': datetime.datetime.now().isoformat(),
                'status': 'active'
            }

            with open(self.LICENSE_FILE, 'w', encoding='utf-8') as f:
                json.dump(license_data, f, ensure_ascii=False, indent=2)

            self.license_data = license_data
            return {
                'success': True,
                'message': "تم تفعيل البرنامج بنجاح"
            }

        except Exception as e:
            return {
                'success': False,
                'error': f"خطأ في تفعيل الترخيص: {str(e)}"
            }

    def deactivate_license(self):
        """إلغاء تفعيل الترخيص"""
        try:
            if os.path.exists(self.LICENSE_FILE):
                os.remove(self.LICENSE_FILE)

            self.license_data = None
            return True, "تم إلغاء تفعيل البرنامج بنجاح"

        except Exception as e:
            return False, f"خطأ في إلغاء تفعيل الترخيص: {str(e)}"

    def load_license(self, retry_count=3):
        """تحميل بيانات الترخيص مع آلية إعادة المحاولة"""
        for attempt in range(retry_count):
            try:
                if os.path.exists(self.LICENSE_FILE):
                    # التحقق من أن الملف قابل للقراءة
                    if os.access(self.LICENSE_FILE, os.R_OK):
                        with open(self.LICENSE_FILE, 'r', encoding='utf-8') as f:
                            content = f.read().strip()
                            if content:  # التأكد من أن الملف ليس فارغًا
                                self.license_data = json.loads(content)
                                print(f"تم تحميل بيانات الترخيص بنجاح في المحاولة {attempt + 1}")
                                return
                            else:
                                print(f"ملف الترخيص فارغ في المحاولة {attempt + 1}")
                    else:
                        print(f"لا يمكن قراءة ملف الترخيص في المحاولة {attempt + 1}")
                else:
                    print(f"ملف الترخيص غير موجود في المحاولة {attempt + 1}")
                    self.license_data = None
                    return
                    
            except (json.JSONDecodeError, FileNotFoundError, PermissionError) as e:
                print(f"خطأ في تحميل بيانات الترخيص في المحاولة {attempt + 1}: {str(e)}")
                if attempt < retry_count - 1:
                    import time
                    time.sleep(0.1)  # انتظار قصير قبل إعادة المحاولة
                    continue
            except Exception as e:
                print(f"خطأ غير متوقع في تحميل بيانات الترخيص في المحاولة {attempt + 1}: {str(e)}")
                if attempt < retry_count - 1:
                    import time
                    time.sleep(0.1)
                    continue
                    
        # إذا فشلت جميع المحاولات
        print(f"فشل في تحميل بيانات الترخيص بعد {retry_count} محاولات")
        self.license_data = None

    def is_activated(self, force_reload=False):
        """التحقق من حالة تفعيل البرنامج مع إمكانية إعادة التحميل"""
        # إعادة تحميل بيانات الترخيص إذا طُلب ذلك أو إذا لم تكن محملة
        if force_reload or not self.license_data:
            self.load_license()
            
        if not self.license_data:
            return False

        license_key = self.license_data.get('license_key')
        if not license_key:
            return False

        is_valid, message = self.validate_license_key(license_key)
        if not is_valid:
            print(f"فشل التحقق من صحة مفتاح الترخيص: {message}")
            # محاولة إعادة تحميل البيانات مرة واحدة إضافية
            if not force_reload:
                print("محاولة إعادة تحميل بيانات الترخيص...")
                return self.is_activated(force_reload=True)
                
        return is_valid

    def get_license_info(self):
        """الحصول على معلومات الترخيص"""
        if not self.license_data:
            return {
                'customer': 'غير محدد',
                'type': 'غير محدد',
                'status': 'غير مفعل',
                'activation_date': '',
                'expiry_date': '',
                'days_left': 0,
                'days_remaining': 0
            }

        try:
            license_key = self.license_data.get('license_key', '')
            activation_date = self.license_data.get('activation_date', '')

            # تنسيق تاريخ التفعيل
            try:
                if activation_date:
                    activation_dt = datetime.datetime.fromisoformat(activation_date)
                    formatted_activation = activation_dt.strftime('%Y-%m-%d %H:%M:%S')
                else:
                    formatted_activation = 'غير محدد'
            except:
                formatted_activation = activation_date

            # تحديد نوع المفتاح والعميل
            customer = "عميل عام"
            license_type = "ترخيص عام"
            days_left = 365

            # التحقق من نوع المفتاح
            if license_key.startswith("SM2-"):
                # مفتاح جديد مربوط بالجهاز
                try:
                    parts = license_key.split("-", 2)
                    if len(parts) == 3:
                        _, signature, encoded_data = parts
                        license_json = base64.b64decode(encoded_data.encode()).decode()
                        license_data = json.loads(license_json)

                        # تحديد نوع الترخيص
                        key_type = license_data.get('key', 'ترخيص مخصص')
                        if key_type == 'FULL-LICENSE-2024':
                            license_type = "ترخيص كامل"
                            customer = "عميل مرخص"
                            days_left = -1  # ترخيص دائم (قيمة خاصة للإشارة إلى الترخيص الدائم)
                        elif key_type == 'TRIAL-LICENSE-2024':
                            license_type = "ترخيص تجريبي"
                            customer = "عميل تجريبي"

                            # حساب الأيام المتبقية للترخيص التجريبي
                            expiry_date_str = license_data.get('expiry_date')
                            if expiry_date_str:
                                try:
                                    expiry_dt = datetime.datetime.fromisoformat(expiry_date_str)
                                    days_left = max(0, (expiry_dt - datetime.datetime.now()).days)
                                except:
                                    # في حالة فشل قراءة تاريخ الانتهاء، استخدم trial_days
                                    trial_days = license_data.get('trial_days', 30)
                                    created_date = license_data.get('created_date')
                                    if created_date:
                                        try:
                                            created_dt = datetime.datetime.fromisoformat(created_date)
                                            expiry_dt = created_dt + datetime.timedelta(days=trial_days)
                                            days_left = max(0, (expiry_dt - datetime.datetime.now()).days)
                                        except:
                                            days_left = trial_days
                                    else:
                                        days_left = trial_days
                            else:
                                # إذا لم يكن هناك تاريخ انتهاء، استخدم trial_days
                                trial_days = license_data.get('trial_days', 30)
                                created_date = license_data.get('created_date')
                                if created_date:
                                    try:
                                        created_dt = datetime.datetime.fromisoformat(created_date)
                                        expiry_dt = created_dt + datetime.timedelta(days=trial_days)
                                        days_left = max(0, (expiry_dt - datetime.datetime.now()).days)
                                    except:
                                        days_left = trial_days
                                else:
                                    days_left = trial_days
                        else:
                            license_type = key_type
                            customer = f"جهاز {license_data.get('machine_id', 'غير محدد')[:8]}..."
                            days_left = 365
                except:
                    license_type = "ترخيص مشفر"
                    customer = "عميل مشفر"
            else:
                # مفتاح قديم
                license_type = license_key
                customer = "عميل عام"
                days_left = 365  # المفاتيح القديمة لا تنتهي صلاحيتها

            # حساب تاريخ انتهاء الصلاحية
            try:
                if license_key.startswith("SM2-") and license_type == "ترخيص تجريبي":
                    # للترخيص التجريبي، استخدم تاريخ الانتهاء من بيانات المفتاح
                    try:
                        parts = license_key.split("-", 2)
                        if len(parts) == 3:
                            _, signature, encoded_data = parts
                            license_json = base64.b64decode(encoded_data.encode()).decode()
                            license_data = json.loads(license_json)

                            expiry_date_str = license_data.get('expiry_date')
                            if expiry_date_str:
                                expiry_dt = datetime.datetime.fromisoformat(expiry_date_str)
                                expiry_date = expiry_dt.strftime('%Y-%m-%d')
                            else:
                                # حساب من تاريخ الإنشاء + trial_days
                                trial_days = license_data.get('trial_days', 30)
                                created_date = license_data.get('created_date')
                                if created_date:
                                    created_dt = datetime.datetime.fromisoformat(created_date)
                                    expiry_dt = created_dt + datetime.timedelta(days=trial_days)
                                    expiry_date = expiry_dt.strftime('%Y-%m-%d')
                                else:
                                    future_date = datetime.datetime.now() + datetime.timedelta(days=trial_days)
                                    expiry_date = future_date.strftime('%Y-%m-%d')
                        else:
                            future_date = datetime.datetime.now() + datetime.timedelta(days=30)
                            expiry_date = future_date.strftime('%Y-%m-%d')
                    except:
                        future_date = datetime.datetime.now() + datetime.timedelta(days=30)
                        expiry_date = future_date.strftime('%Y-%m-%d')
                else:
                    # للترخيص الكامل والمفاتيح القديمة، استخدم سنة من تاريخ التفعيل
                    if activation_date:
                        activation_dt = datetime.datetime.fromisoformat(activation_date)
                        expiry_dt = activation_dt + datetime.timedelta(days=365)
                        expiry_date = expiry_dt.strftime('%Y-%m-%d')
                    else:
                        future_date = datetime.datetime.now() + datetime.timedelta(days=365)
                        expiry_date = future_date.strftime('%Y-%m-%d')
            except:
                future_date = datetime.datetime.now() + datetime.timedelta(days=365)
                expiry_date = future_date.strftime('%Y-%m-%d')

            # تعديل عرض معلومات الترخيص للتمييز بين الترخيص الكامل والتجريبي
            if days_left == -1:  # ترخيص دائم
                display_days_left = "ترخيص دائم"
            elif days_left > 0:
                display_days_left = days_left
            else:
                display_days_left = 0

            return {
                'customer': customer,
                'type': license_type,
                'status': 'مفعل' if self.is_activated() else 'غير مفعل',
                'activation_date': formatted_activation,
                'expiry_date': expiry_date,
                'days_left': display_days_left,
                'days_remaining': display_days_left
            }

        except Exception as e:
            print(f"خطأ في get_license_info: {str(e)}")
            return {
                'customer': 'خطأ في القراءة',
                'type': 'خطأ في القراءة',
                'status': 'خطأ في قراءة الترخيص',
                'activation_date': '',
                'expiry_date': '',
                'days_left': 0,
                'days_remaining': 0
            }

    def get_machine_id(self):
        """الحصول على معرف الجهاز"""
        return self.generate_machine_id()
