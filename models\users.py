"""
وحدة إدارة المستخدمين - مسؤولة عن إدارة المستخدمين والصلاحيات
"""

import hashlib
import datetime
from models.database import db
import json

class UserModel:
    """نموذج المستخدم يحتوي على عمليات إدارة المستخدمين والصلاحيات"""

    @staticmethod
    def hash_password(password):
        """تشفير كلمة المرور باستخدام SHA-256"""
        return hashlib.sha256(password.encode()).hexdigest()

    @staticmethod
    def create_user(username, full_name, password, role="كاشير", is_active=True):
        """إنشاء مستخدم جديد"""
        try:
            # التحقق من عدم وجود مستخدم بنفس اسم المستخدم
            existing_user = UserModel.get_user_by_username(username)
            if existing_user:
                return False, "يوجد مستخدم آخر بنفس اسم المستخدم"

            # تشفير كلمة المرور
            hashed_password = UserModel.hash_password(password)

            # الوقت الحالي
            now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # إدراج المستخدم الجديد
            query = """
                INSERT INTO users (username, full_name, password, role, is_active, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            params = (username, full_name, hashed_password, role, 1 if is_active else 0, now, now)

            db.connect()
            db.execute(query, params)
            user_id = db.get_last_insert_id()
            db.commit()

            # تعديل: إرجاع رسالة نصية بدلاً من معرف المستخدم
            return True, f"تم إضافة المستخدم {full_name} بنجاح"
        except Exception as e:
            db.rollback()
            db.log_error(f"خطأ في إنشاء مستخدم جديد: {str(e)}")
            return False, f"خطأ في إنشاء المستخدم: {str(e)}"

    @staticmethod
    def update_user(user_id, username, full_name, password=None, role=None, is_active=None):
        """تحديث بيانات مستخدم"""
        try:
            # الوقت الحالي
            now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # إعداد الحقول المطلوب تحديثها
            fields = []
            params = []

            if username:
                # التحقق من وجود اسم مستخدم آخر بنفس الاسم
                db.connect()
                current_user = db.fetch_one("SELECT username FROM users WHERE id = ?", (user_id,))

                if current_user and current_user['username'] != username:
                    existing = db.fetch_one("SELECT id FROM users WHERE username = ? AND id != ?", (username, user_id))
                    if existing:
                        return False, "يوجد مستخدم آخر بنفس اسم المستخدم"

                fields.append("username = ?")
                params.append(username)

            if full_name:
                fields.append("full_name = ?")
                params.append(full_name)

            if password:
                fields.append("password = ?")
                params.append(UserModel.hash_password(password))

            if role:
                fields.append("role = ?")
                params.append(role)

            if is_active is not None:
                fields.append("is_active = ?")
                params.append(1 if is_active else 0)

            # إضافة حقل updated_at
            fields.append("updated_at = ?")
            params.append(now)

            # إضافة معرف المستخدم للمعلمات
            params.append(user_id)

            # بناء استعلام التحديث
            query = f"""
                UPDATE users
                SET {', '.join(fields)}
                WHERE id = ?
            """

            db.connect()
            result = db.execute(query, params)
            db.commit()

            return True, "تم تحديث بيانات المستخدم بنجاح"
        except Exception as e:
            db.rollback()
            db.log_error(f"خطأ في تحديث بيانات المستخدم: {str(e)}")
            return False, f"خطأ في تحديث المستخدم: {str(e)}"

    @staticmethod
    def delete_user(user_id):
        """حذف مستخدم"""
        try:
            # التحقق من عدم محاولة حذف المستخدم admin
            user = UserModel.get_user_by_id(user_id)
            if user and user['username'] == 'admin':
                return False, "لا يمكن حذف المستخدم الرئيسي"

            query = "DELETE FROM users WHERE id = ?"

            db.connect()
            db.execute(query, (user_id,))
            db.commit()

            return True, "تم حذف المستخدم بنجاح"
        except Exception as e:
            db.rollback()
            db.log_error(f"خطأ في حذف المستخدم: {str(e)}")
            return False, f"خطأ في حذف المستخدم: {str(e)}"

    @staticmethod
    def get_all_users():
        """الحصول على جميع المستخدمين"""
        try:
            query = """
                SELECT id, username, full_name, role, is_active, last_login, created_at, updated_at
                FROM users
                ORDER BY id
            """

            db.connect()
            users = db.fetch_all(query)

            # تحويل حقل is_active إلى قيمة منطقية
            for user in users:
                user['is_active'] = bool(user['is_active'])
                user['status'] = "نشط" if user['is_active'] else "غير نشط"

            return users
        except Exception as e:
            db.log_error(f"خطأ في الحصول على قائمة المستخدمين: {str(e)}")
            return []

    @staticmethod
    def get_user_by_id(user_id):
        """الحصول على مستخدم بواسطة المعرف"""
        try:
            query = """
                SELECT id, username, full_name, role, is_active, last_login, created_at, updated_at
                FROM users
                WHERE id = ?
            """

            db.connect()
            user = db.fetch_one(query, (user_id,))

            if user:
                user['is_active'] = bool(user['is_active'])
                user['status'] = "نشط" if user['is_active'] else "غير نشط"

            return user
        except Exception as e:
            db.log_error(f"خطأ في الحصول على بيانات المستخدم: {str(e)}")
            return None

    @staticmethod
    def get_user_by_username(username):
        """الحصول على مستخدم بواسطة اسم المستخدم"""
        try:
            query = """
                SELECT id, username, full_name, password, role, is_active, last_login, created_at, updated_at
                FROM users
                WHERE username = ?
            """

            db.connect()
            user = db.fetch_one(query, (username,))

            if user:
                user['is_active'] = bool(user['is_active'])
                user['status'] = "نشط" if user['is_active'] else "غير نشط"

            return user
        except Exception as e:
            db.log_error(f"خطأ في الحصول على بيانات المستخدم: {str(e)}")
            return None

    @staticmethod
    def authenticate(username, password):
        """مصادقة المستخدم"""
        try:
            # الحصول على بيانات المستخدم
            user = UserModel.get_user_by_username(username)

            # التحقق من وجود المستخدم وكلمة المرور
            if not user:
                return False, None, "اسم المستخدم غير موجود"

            # التحقق من كلمة المرور
            hashed_password = UserModel.hash_password(password)
            if user['password'] != hashed_password:
                return False, None, "كلمة المرور غير صحيحة"

            # التحقق من أن المستخدم نشط
            if not user['is_active']:
                return False, None, "هذا الحساب غير نشط"

            # تحديث تاريخ آخر تسجيل دخول
            now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            update_query = """
                UPDATE users
                SET last_login = ?, updated_at = ?
                WHERE id = ?
            """

            db.execute(update_query, (now, now, user['id']))
            db.commit()

            return True, user, "تم تسجيل الدخول بنجاح"
        except Exception as e:
            db.log_error(f"خطأ في تسجيل الدخول: {str(e)}")
            return False, None, f"خطأ في تسجيل الدخول: {str(e)}"

    @staticmethod
    def change_password(user_id, old_password, new_password):
        """تغيير كلمة المرور"""
        try:
            # الحصول على بيانات المستخدم
            query = "SELECT password FROM users WHERE id = ?"
            db.connect()
            user = db.fetch_one(query, (user_id,))

            if not user:
                return False, "المستخدم غير موجود"

            # التحقق من كلمة المرور القديمة
            if user['password'] != UserModel.hash_password(old_password):
                return False, "كلمة المرور القديمة غير صحيحة"

            # تحديث كلمة المرور
            now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            update_query = """
                UPDATE users
                SET password = ?, updated_at = ?
                WHERE id = ?
            """

            db.execute(update_query, (UserModel.hash_password(new_password), now, user_id))
            db.commit()

            return True, "تم تغيير كلمة المرور بنجاح"
        except Exception as e:
            db.rollback()
            db.log_error(f"خطأ في تغيير كلمة المرور: {str(e)}")
            return False, f"خطأ في تغيير كلمة المرور: {str(e)}"

    @staticmethod
    def get_role_permissions():
        """الحصول على صلاحيات الأدوار"""
        try:
            query = "SELECT role, permissions FROM role_permissions"

            db.connect()
            results = db.fetch_all(query)

            permissions_data = {}
            for record in results:
                try:
                    role = record['role']
                    # تحويل نص JSON إلى كائن Python
                    perm_data = json.loads(record['permissions'])
                    permissions_data[role] = perm_data
                except json.JSONDecodeError:
                    db.log_error(f"خطأ في تحويل بيانات الصلاحيات للدور: {record['role']}")

            # إضافة الصلاحيات الافتراضية للمدير إذا لم تكن موجودة
            if "مدير" not in permissions_data:
                # استخدام نفس الصلاحيات المعرفة في initialize_admin_user
                permissions_data["مدير"] = {
                    # المبيعات
                    "عرض المبيعات": True,
                    "إضافة عملية بيع": True,
                    "تعديل عملية بيع": True,
                    "حذف عملية بيع": True,
                    "تعديل سعر المنتج في الفاتورة": True,
                    "تعديل كمية المنتج في الفاتورة": True,
                    "طباعة فاتورة مبيعات": True,

                    # المنتجات
                    "عرض المنتجات": True,
                    "إضافة منتج": True,
                    "تعديل منتج": True,
                    "حذف منتج": True,
                    "إدارة فئات المنتجات": True,
                    "تعديل أسعار المنتجات": True,

                    # المخزون
                    "عرض المخزون": True,
                    "إضافة للمخزون": True,
                    "تعديل المخزون": True,

                    # الفواتير
                    "عرض الفواتير": True,
                    "إلغاء فاتورة": True,
                    "حذف فاتورة": True,
                    "طباعة فاتورة": True,
                    "تعديل فاتورة": True,

                    # التقارير
                    "عرض تقارير المبيعات": True,
                    "عرض تقارير المخزون": True,
                    "عرض تقارير الأرباح": True,
                    "عرض تقارير العملاء": True,
                    "عرض تقرير المنتجات الأكثر مبيعاً": True,

                    # العملاء
                    "عرض العملاء": True,
                    "إضافة عميل": True,
                    "تعديل عميل": True,
                    "حذف عميل": True,
                    "إدارة ديون العملاء": True,
                    "عرض تفاصيل العميل": True,

                    # الموردين
                    "عرض الموردين": True,
                    "إضافة مورد": True,
                    "تعديل مورد": True,
                    "حذف مورد": True,
                    "إدارة مدفوعات الموردين": True,
                    "عرض تفاصيل المورد": True,

                    # المصروفات
                    "عرض المصروفات": True,
                    "إضافة مصروف": True,
                    "تعديل مصروف": True,
                    "حذف مصروف": True,

                    # المستخدمين
                    "عرض المستخدمين": True,
                    "إضافة مستخدم": True,
                    "تعديل مستخدم": True,
                    "حذف مستخدم": True,
                    "إدارة صلاحيات المستخدمين": True,

                    # الإعدادات
                    "عرض الإعدادات": True,
                    "تعديل إعدادات النظام": True,
                    "إدارة النسخ الاحتياطي": True,
                    "استعادة النسخ الاحتياطي": True
                }

            # إضافة الأدوار الأخرى إذا لم تكن موجودة
            if "كاشير" not in permissions_data:
                permissions_data["كاشير"] = {
                    # المبيعات
                    "عرض المبيعات": True,
                    "إضافة عملية بيع": True,
                    "تعديل عملية بيع": False,
                    "حذف عملية بيع": False,
                    "تعديل سعر المنتج في الفاتورة": True,
                    "تعديل كمية المنتج في الفاتورة": True,
                    "طباعة فاتورة مبيعات": True,

                    # المنتجات
                    "عرض المنتجات": True,
                    "إضافة منتج": False,
                    "تعديل منتج": False,
                    "حذف منتج": False,
                    "إدارة فئات المنتجات": False,
                    "تعديل أسعار المنتجات": False,

                    # المخزون
                    "عرض المخزون": True,
                    "إضافة للمخزون": False,
                    "تعديل المخزون": False,

                    # الفواتير
                    "عرض الفواتير": True,
                    "إلغاء فاتورة": False,
                    "حذف فاتورة": False,
                    "طباعة فاتورة": True,
                    "تعديل فاتورة": False,

                    # التقارير
                    "عرض تقارير المبيعات": False,
                    "عرض تقارير المخزون": False,
                    "عرض تقارير الأرباح": False,
                    "عرض تقارير العملاء": False,

                    # العملاء
                    "عرض العملاء": True,
                    "إضافة عميل": True,
                    "تعديل عميل": True,
                    "حذف عميل": False,
                    "إدارة ديون العملاء": False,
                    "عرض تفاصيل العميل": True,

                    # الموردين
                    "عرض الموردين": False,
                    "إضافة مورد": False,
                    "تعديل مورد": False,
                    "حذف مورد": False,
                    "إدارة مدفوعات الموردين": False,
                    "عرض تفاصيل المورد": False,

                    # المصروفات
                    "عرض المصروفات": False,
                    "إضافة مصروف": False,
                    "تعديل مصروف": False,
                    "حذف مصروف": False,

                    # المستخدمين
                    "عرض المستخدمين": False,
                    "إضافة مستخدم": False,
                    "تعديل مستخدم": False,
                    "حذف مستخدم": False,
                    "إدارة صلاحيات المستخدمين": False,

                    # الإعدادات
                    "عرض الإعدادات": False,
                    "تعديل إعدادات النظام": False,
                    "إدارة النسخ الاحتياطي": False,
                    "استعادة النسخ الاحتياطي": False
                }

            if "مدير مخزون" not in permissions_data:
                permissions_data["مدير مخزون"] = {
                    # المبيعات
                    "عرض المبيعات": False,
                    "إضافة عملية بيع": False,
                    "تعديل عملية بيع": False,
                    "حذف عملية بيع": False,
                    "تعديل سعر المنتج في الفاتورة": False,
                    "تعديل كمية المنتج في الفاتورة": False,
                    "طباعة فاتورة مبيعات": False,

                    # المنتجات
                    "عرض المنتجات": True,
                    "إضافة منتج": True,
                    "تعديل منتج": True,
                    "حذف منتج": True,
                    "إدارة فئات المنتجات": True,
                    "تعديل أسعار المنتجات": True,

                    # المخزون
                    "عرض المخزون": True,
                    "إضافة للمخزون": True,
                    "تعديل المخزون": True,

                    # الفواتير
                    "عرض الفواتير": False,
                    "إلغاء فاتورة": False,
                    "حذف فاتورة": False,
                    "طباعة فاتورة": False,
                    "تعديل فاتورة": False,

                    # التقارير
                    "عرض تقارير المبيعات": False,
                    "عرض تقارير المخزون": True,
                    "عرض تقارير الأرباح": False,
                    "عرض تقارير العملاء": False,

                    # العملاء
                    "عرض العملاء": False,
                    "إضافة عميل": False,
                    "تعديل عميل": False,
                    "حذف عميل": False,
                    "إدارة ديون العملاء": False,
                    "عرض تفاصيل العميل": False,

                    # الموردين
                    "عرض الموردين": True,
                    "إضافة مورد": True,
                    "تعديل مورد": True,
                    "حذف مورد": True,
                    "إدارة مدفوعات الموردين": True,
                    "عرض تفاصيل المورد": True,

                    # المصروفات
                    "عرض المصروفات": False,
                    "إضافة مصروف": False,
                    "تعديل مصروف": False,
                    "حذف مصروف": False,

                    # المستخدمين
                    "عرض المستخدمين": False,
                    "إضافة مستخدم": False,
                    "تعديل مستخدم": False,
                    "حذف مستخدم": False,
                    "إدارة صلاحيات المستخدمين": False,

                    # الإعدادات
                    "عرض الإعدادات": False,
                    "تعديل إعدادات النظام": False,
                    "إدارة النسخ الاحتياطي": False,
                    "استعادة النسخ الاحتياطي": False
                }

            return permissions_data
        except Exception as e:
            db.log_error(f"خطأ في الحصول على صلاحيات الأدوار: {str(e)}")
            return {
                "مدير": {
                    "إدارة المبيعات": True,
                    "إدارة المنتجات": True,
                    "إدارة الفواتير": True,
                    "إدارة التقارير": True,
                    "إدارة العملاء": True,
                    "إدارة الموردين": True,
                    "إدارة الإعدادات": True
                },
                "كاشير": {
                    "إدارة المبيعات": True,
                    "إدارة المنتجات": False,
                    "إدارة الفواتير": True,
                    "إدارة التقارير": False,
                    "إدارة العملاء": True,
                    "إدارة الموردين": False,
                    "إدارة الإعدادات": False
                },
                "مدير مخزون": {
                    "إدارة المبيعات": False,
                    "إدارة المنتجات": True,
                    "إدارة الفواتير": False,
                    "إدارة التقارير": False,
                    "إدارة العملاء": False,
                    "إدارة الموردين": True,
                    "إدارة الإعدادات": False
                }
            }

    @staticmethod
    def save_role_permissions(permissions):
        """حفظ صلاحيات الأدوار"""
        try:
            # الحصول على التاريخ الحالي
            now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # حذف الصلاحيات الحالية
            db.connect()
            db.execute("DELETE FROM role_permissions")

            # إدراج الصلاحيات الجديدة
            for role, perms in permissions.items():
                # تحويل بيانات الصلاحيات إلى نص JSON
                perms_json = json.dumps(perms)

                # التحقق من وجود السجل
                existing = db.fetch_one("SELECT id FROM role_permissions WHERE role = ?", (role,))

                if existing:
                    # تحديث السجل الموجود
                    query = """
                        UPDATE role_permissions
                        SET permissions = ?, updated_at = ?
                        WHERE role = ?
                    """
                    db.execute(query, (perms_json, now, role))
                else:
                    # إدراج سجل جديد
                    query = """
                        INSERT INTO role_permissions (role, permissions, created_at, updated_at)
                        VALUES (?, ?, ?, ?)
                    """
                    db.execute(query, (role, perms_json, now, now))

            # حفظ التغييرات
            db.commit()

            return True, "تم حفظ صلاحيات الأدوار بنجاح"
        except Exception as e:
            db.rollback()
            db.log_error(f"خطأ في حفظ صلاحيات الأدوار: {str(e)}")
            return False, f"خطأ في حفظ الصلاحيات: {str(e)}"

    @staticmethod
    def check_permission(user_id, permission_name):
        """التحقق من صلاحية المستخدم"""
        try:
            # الحصول على معلومات المستخدم
            user = UserModel.get_user_by_id(user_id)
            if not user:
                return False

            # المستخدم admin لديه كل الصلاحيات دائمًا
            if user['username'] == 'admin':
                return True

            # المدير لديه كل الصلاحيات
            if user['role'] == "مدير":
                return True

            # الحصول على صلاحيات المستخدم
            user_permissions = UserModel.get_user_permissions(user_id)

            # التحقق من وجود الصلاحية للمستخدم
            if permission_name in user_permissions:
                return user_permissions[permission_name]

            return False
        except Exception as e:
            db.log_error(f"خطأ في التحقق من الصلاحية: {str(e)}")
            return False

    @staticmethod
    def check_permissions(user_id, permission_names):
        """التحقق من مجموعة من الصلاحيات للمستخدم (يجب أن تتوفر جميع الصلاحيات)"""
        try:
            # الحصول على معلومات المستخدم
            user = UserModel.get_user_by_id(user_id)
            if not user:
                return False

            # المستخدم admin لديه كل الصلاحيات دائمًا
            if user['username'] == 'admin':
                return True

            # المدير لديه كل الصلاحيات
            if user['role'] == "مدير":
                return True

            # الحصول على صلاحيات المستخدم
            user_permissions = UserModel.get_user_permissions(user_id)

            # التحقق من جميع الصلاحيات المطلوبة
            for permission_name in permission_names:
                if permission_name not in user_permissions or not user_permissions[permission_name]:
                    return False

            return True
        except Exception as e:
            db.log_error(f"خطأ في التحقق من الصلاحيات: {str(e)}")
            return False

    @staticmethod
    def check_any_permission(user_id, permission_names):
        """التحقق من مجموعة من الصلاحيات للمستخدم (يكفي توفر صلاحية واحدة)"""
        try:
            # الحصول على معلومات المستخدم
            user = UserModel.get_user_by_id(user_id)
            if not user:
                return False

            # المستخدم admin لديه كل الصلاحيات دائمًا
            if user['username'] == 'admin':
                return True

            # المدير لديه كل الصلاحيات
            if user['role'] == "مدير":
                return True

            # الحصول على صلاحيات المستخدم
            user_permissions = UserModel.get_user_permissions(user_id)

            # التحقق من أي من الصلاحيات المطلوبة
            for permission_name in permission_names:
                if permission_name in user_permissions and user_permissions[permission_name]:
                    return True

            return False
        except Exception as e:
            db.log_error(f"خطأ في التحقق من الصلاحيات: {str(e)}")
            return False

    @staticmethod
    def get_user_permissions(user_id):
        """الحصول على صلاحيات مستخدم محدد"""
        try:
            # الحصول على معلومات المستخدم
            user = UserModel.get_user_by_id(user_id)
            if not user:
                return {}

            # المدير لديه كل الصلاحيات
            if user['role'] == "مدير":
                # إنشاء قائمة بجميع الصلاحيات المتاحة
                all_permissions = {}

                # الصلاحيات المتاحة - مجموعة بحسب الوظائف
                available_permissions = {
                    "المبيعات": [
                        "عرض المبيعات",
                        "إضافة عملية بيع",
                        "تعديل عملية بيع",
                        "حذف عملية بيع",
                        "طباعة فاتورة مبيعات"
                    ],
                    "المنتجات": [
                        "عرض المنتجات",
                        "إضافة منتج",
                        "تعديل منتج",
                        "حذف منتج",
                        "إدارة فئات المنتجات",
                        "تعديل أسعار المنتجات"
                    ],
                    "المخزون": [
                        "عرض المخزون",
                        "إضافة للمخزون",
                        "تعديل المخزون"
                    ],
                    "الفواتير": [
                        "عرض الفواتير",
                        "إلغاء فاتورة",
                        "حذف فاتورة",
                        "طباعة فاتورة",
                        "تعديل فاتورة"
                    ],
                    "التقارير": [
                        "عرض تقارير المبيعات",
                        "عرض تقارير المخزون",
                        "عرض تقارير الأرباح",
                        "عرض تقارير العملاء"
                    ],
                    "العملاء": [
                        "عرض العملاء",
                        "إضافة عميل",
                        "تعديل عميل",
                        "حذف عميل",
                        "إدارة ديون العملاء"
                    ],
                    "الموردين": [
                        "عرض الموردين",
                        "إضافة مورد",
                        "تعديل مورد",
                        "حذف مورد",
                        "إدارة مدفوعات الموردين"
                    ],
                    "المصروفات": [
                        "عرض المصروفات",
                        "إضافة مصروف",
                        "تعديل مصروف",
                        "حذف مصروف"
                    ],
                    "المستخدمين": [
                        "عرض المستخدمين",
                        "إضافة مستخدم",
                        "تعديل مستخدم",
                        "حذف مستخدم",
                        "إدارة صلاحيات المستخدمين"
                    ],
                    "الإعدادات": [
                        "عرض الإعدادات",
                        "تعديل إعدادات النظام",
                        "إدارة النسخ الاحتياطي",
                        "استعادة النسخ الاحتياطي"
                    ]
                }

                # إضافة جميع الصلاحيات بقيمة True
                for category, permissions in available_permissions.items():
                    for permission in permissions:
                        all_permissions[permission] = True

                return all_permissions

            # الحصول على صلاحيات المستخدم من قاعدة البيانات
            query = "SELECT permissions FROM user_permissions WHERE user_id = ?"
            result = db.fetch_all(query, (user_id,))

            if result and len(result) > 0 and result[0]['permissions']:
                return json.loads(result[0]['permissions'])

            # إذا لم تكن هناك صلاحيات محددة للمستخدم، نستخدم صلاحيات الدور الافتراضية
            role_permissions = UserModel.get_role_permissions()
            if user['role'] in role_permissions:
                return role_permissions[user['role']]

            # إذا لم تكن هناك صلاحيات للدور أيضًا، نرجع قاموس فارغ
            return {}
        except Exception as e:
            db.log_error(f"خطأ في الحصول على صلاحيات المستخدم: {str(e)}")
            return {}

    @staticmethod
    def save_user_permissions(user_id, permissions_data):
        """حفظ صلاحيات مستخدم محدد"""
        try:
            # التحقق من وجود المستخدم
            user = UserModel.get_user_by_id(user_id)
            if not user:
                return False, "المستخدم غير موجود"

            # تحويل بيانات الصلاحيات إلى JSON
            permissions_json = json.dumps(permissions_data, ensure_ascii=False)

            # التحقق من وجود صلاحيات للمستخدم
            query = "SELECT * FROM user_permissions WHERE user_id = ?"
            result = db.fetch_all(query, (user_id,))

            # تأكد من الاتصال بقاعدة البيانات
            db.connect()

            if result and len(result) > 0:
                # تحديث الصلاحيات الموجودة
                update_query = "UPDATE user_permissions SET permissions = ? WHERE user_id = ?"
                db.execute(update_query, (permissions_json, user_id))
            else:
                # إضافة صلاحيات جديدة
                insert_query = "INSERT INTO user_permissions (user_id, permissions) VALUES (?, ?)"
                db.execute(insert_query, (user_id, permissions_json))

            # حفظ التغييرات
            db.commit()

            return True, "تم حفظ صلاحيات المستخدم بنجاح"
        except Exception as e:
            db.log_error(f"خطأ في حفظ صلاحيات المستخدم: {str(e)}")
            return False, f"حدث خطأ أثناء حفظ الصلاحيات: {str(e)}"

    @staticmethod
    def reset_password(user_id, new_password):
        """إعادة تعيين كلمة المرور للمستخدم"""
        try:
            # التحقق من وجود المستخدم
            user = UserModel.get_user_by_id(user_id)
            if not user:
                return False, "المستخدم غير موجود"

            # تشفير كلمة المرور الجديدة
            hashed_password = UserModel.hash_password(new_password)

            # تحديث كلمة المرور
            now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            query = """
                UPDATE users
                SET password = ?, updated_at = ?
                WHERE id = ?
            """

            db.connect()
            db.execute(query, (hashed_password, now, user_id))
            db.commit()

            return True, "تم إعادة تعيين كلمة المرور بنجاح"
        except Exception as e:
            db.rollback()
            db.log_error(f"خطأ في إعادة تعيين كلمة المرور: {str(e)}")
            return False, f"خطأ في إعادة تعيين كلمة المرور: {str(e)}"

    @staticmethod
    def initialize_admin_user():
        """تهيئة المستخدم الرئيسي (admin)"""
        try:
            # التحقق من وجود المستخدم الرئيسي
            db.connect()
            admin = UserModel.get_user_by_username("admin")

            if not admin:
                # إنشاء المستخدم الرئيسي
                hashed_password = UserModel.hash_password("admin")
                now = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                query = """
                    INSERT INTO users (username, full_name, password, role, is_active, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """
                params = ("admin", "مدير النظام", hashed_password, "مدير", 1, now, now)

                db.execute(query, params)
                db.commit()

                print("تم إنشاء المستخدم الرئيسي بنجاح")

                # إنشاء الصلاحيات الافتراضية
                default_permissions = {
                    "مدير": {
                        # المبيعات
                        "عرض المبيعات": True,
                        "إضافة عملية بيع": True,
                        "تعديل عملية بيع": True,
                        "حذف عملية بيع": True,
                        "تعديل سعر المنتج في الفاتورة": True,
                        "تعديل كمية المنتج في الفاتورة": True,
                        "طباعة فاتورة مبيعات": True,

                        # المنتجات
                        "عرض المنتجات": True,
                        "إضافة منتج": True,
                        "تعديل منتج": True,
                        "حذف منتج": True,
                        "إدارة فئات المنتجات": True,
                        "تعديل أسعار المنتجات": True,

                        # المخزون
                        "عرض المخزون": True,
                        "إضافة للمخزون": True,
                        "تعديل المخزون": True,

                        # الفواتير
                        "عرض الفواتير": True,
                        "إلغاء فاتورة": True,
                        "طباعة فاتورة": True,
                        "تعديل فاتورة": True,

                        # التقارير
                        "عرض تقارير المبيعات": True,
                        "عرض تقارير المخزون": True,
                        "عرض تقارير الأرباح": True,
                        "عرض تقارير العملاء": True,
                        "عرض تقرير المنتجات الأكثر مبيعاً": True,

                        # العملاء
                        "عرض العملاء": True,
                        "إضافة عميل": True,
                        "تعديل عميل": True,
                        "حذف عميل": True,
                        "إدارة ديون العملاء": True,
                        "عرض تفاصيل العميل": True,

                        # الموردين
                        "عرض الموردين": True,
                        "إضافة مورد": True,
                        "تعديل مورد": True,
                        "حذف مورد": True,
                        "إدارة مدفوعات الموردين": True,
                        "عرض تفاصيل المورد": True,

                        # المصروفات
                        "عرض المصروفات": True,
                        "إضافة مصروف": True,
                        "تعديل مصروف": True,
                        "حذف مصروف": True,

                        # المستخدمين
                        "عرض المستخدمين": True,
                        "إضافة مستخدم": True,
                        "تعديل مستخدم": True,
                        "حذف مستخدم": True,
                        "إدارة صلاحيات المستخدمين": True,

                        # الإعدادات
                        "عرض الإعدادات": True,
                        "تعديل إعدادات النظام": True,
                        "إدارة النسخ الاحتياطي": True,
                        "استعادة النسخ الاحتياطي": True
                    },
                    "كاشير": {
                        # المبيعات
                        "عرض المبيعات": True,
                        "إضافة عملية بيع": True,
                        "تعديل عملية بيع": False,
                        "حذف عملية بيع": False,
                        "تعديل سعر المنتج في الفاتورة": True,
                        "تعديل كمية المنتج في الفاتورة": True,
                        "طباعة فاتورة مبيعات": True,

                        # المنتجات
                        "عرض المنتجات": True,
                        "إضافة منتج": False,
                        "تعديل منتج": False,
                        "حذف منتج": False,
                        "إدارة فئات المنتجات": False,
                        "تعديل أسعار المنتجات": False,

                        # المخزون
                        "عرض المخزون": True,
                        "إضافة للمخزون": False,
                        "تعديل المخزون": False,

                        # الفواتير
                        "عرض الفواتير": True,
                        "إلغاء فاتورة": False,
                        "طباعة فاتورة": True,
                        "تعديل فاتورة": False,

                        # التقارير
                        "عرض تقارير المبيعات": False,
                        "عرض تقارير المخزون": False,
                        "عرض تقارير الأرباح": False,
                        "عرض تقارير العملاء": False,

                        # العملاء
                        "عرض العملاء": True,
                        "إضافة عميل": True,
                        "تعديل عميل": True,
                        "حذف عميل": False,
                        "إدارة ديون العملاء": False,
                        "عرض تفاصيل العميل": True,

                        # الموردين
                        "عرض الموردين": False,
                        "إضافة مورد": False,
                        "تعديل مورد": False,
                        "حذف مورد": False,
                        "إدارة مدفوعات الموردين": False,
                        "عرض تفاصيل المورد": False,

                        # المصروفات
                        "عرض المصروفات": False,
                        "إضافة مصروف": False,
                        "تعديل مصروف": False,
                        "حذف مصروف": False,

                        # المستخدمين
                        "عرض المستخدمين": False,
                        "إضافة مستخدم": False,
                        "تعديل مستخدم": False,
                        "حذف مستخدم": False,
                        "إدارة صلاحيات المستخدمين": False,

                        # الإعدادات
                        "عرض الإعدادات": False,
                        "تعديل إعدادات النظام": False,
                        "إدارة النسخ الاحتياطي": False,
                        "استعادة النسخ الاحتياطي": False
                    },
                    "مدير مخزون": {
                        # المبيعات
                        "عرض المبيعات": False,
                        "إضافة عملية بيع": False,
                        "تعديل عملية بيع": False,
                        "حذف عملية بيع": False,
                        "تعديل سعر المنتج في الفاتورة": False,
                        "تعديل كمية المنتج في الفاتورة": False,
                        "طباعة فاتورة مبيعات": False,

                        # المنتجات
                        "عرض المنتجات": True,
                        "إضافة منتج": True,
                        "تعديل منتج": True,
                        "حذف منتج": True,
                        "إدارة فئات المنتجات": True,
                        "تعديل أسعار المنتجات": True,

                        # المخزون
                        "عرض المخزون": True,
                        "إضافة للمخزون": True,
                        "تعديل المخزون": True,

                        # الفواتير
                        "عرض الفواتير": False,
                        "إلغاء فاتورة": False,
                        "طباعة فاتورة": False,
                        "تعديل فاتورة": False,

                        # التقارير
                        "عرض تقارير المبيعات": False,
                        "عرض تقارير المخزون": True,
                        "عرض تقارير الأرباح": False,
                        "عرض تقارير العملاء": False,

                        # العملاء
                        "عرض العملاء": False,
                        "إضافة عميل": False,
                        "تعديل عميل": False,
                        "حذف عميل": False,
                        "إدارة ديون العملاء": False,
                        "عرض تفاصيل العميل": False,

                        # الموردين
                        "عرض الموردين": True,
                        "إضافة مورد": True,
                        "تعديل مورد": True,
                        "حذف مورد": True,
                        "إدارة مدفوعات الموردين": True,
                        "عرض تفاصيل المورد": True,

                        # المصروفات
                        "عرض المصروفات": False,
                        "إضافة مصروف": False,
                        "تعديل مصروف": False,
                        "حذف مصروف": False,

                        # المستخدمين
                        "عرض المستخدمين": False,
                        "إضافة مستخدم": False,
                        "تعديل مستخدم": False,
                        "حذف مستخدم": False,
                        "إدارة صلاحيات المستخدمين": False,

                        # الإعدادات
                        "عرض الإعدادات": False,
                        "تعديل إعدادات النظام": False,
                        "إدارة النسخ الاحتياطي": False,
                        "استعادة النسخ الاحتياطي": False
                    }
                }

                UserModel.save_role_permissions(default_permissions)

                return True, "تم تهيئة نظام المستخدمين بنجاح"
            else:
                print("المستخدم الرئيسي موجود بالفعل")
                return True, "نظام المستخدمين مهيأ بالفعل"
        except Exception as e:
            db.rollback()
            db.log_error(f"خطأ في تهيئة نظام المستخدمين: {str(e)}")
            return False, f"خطأ في تهيئة نظام المستخدمين: {str(e)}"