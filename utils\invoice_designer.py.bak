#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
مصمم الفواتير - يوفر معاينة وتصميم الفواتير
"""

from PyQt5.QtWidgets import (QDialog, QVBoxLayout, QHBoxLayout, QLabel,
                             QPushButton, QTextEdit, QFrame, QScrollArea,
                             QWidget, QGridLayout, QSpacerItem, QSizePolicy)
from PyQt5.QtCore import Qt, QSize, QSizeF, QSettings
from PyQt5.QtGui import QFont, QPixmap, QPainter, QTextDocument
from PyQt5.QtPrintSupport import QPrintPreviewDialog, QPrintDialog, QPrinter
import datetime
import re


class TextWrapper:
    """فئة لتقسيم النصوص الطويلة بذكاء"""
    
    def __init__(self, max_width_chars, max_lines=3):
        """
        تهيئة TextWrapper
        
        Args:
            max_width_chars (int): عدد الأحرف الأقصى في السطر الواحد
            max_lines (int): عدد الأسطر الأقصى المسموح
        """
        self.max_width_chars = max_width_chars
        self.max_lines = max_lines
    
    def wrap_text(self, text):
        """
        تقسيم النص إلى أسطر متعددة
        
        Args:
            text (str): النص المراد تقسيمه
            
        Returns:
            list: قائمة بالأسطر المقسمة
        """
        if not text or not isinstance(text, str):
            return [""]
        
        text = text.strip()
        if len(text) <= self.max_width_chars:
            return [text]
        
        lines = []
        remaining_text = text
        
        for _ in range(self.max_lines):
            if not remaining_text:
                break
                
            if len(remaining_text) <= self.max_width_chars:
                lines.append(remaining_text)
                break
            
            # محاولة التقسيم الذكي
            line, remaining_text = self.smart_break(remaining_text, self.max_width_chars)
            lines.append(line)
        
        # إضافة نقاط إذا تم اقتطاع النص
        if remaining_text and lines:
            lines = self.add_ellipsis_if_needed(lines)
        
        return lines
    
    def smart_break(self, text, width):
        """
        تقسيم ذكي للنص مع تجنب قطع الكلمات
        
        Args:
            text (str): النص المراد تقسيمه
            width (int): عرض السطر المطلوب
            
        Returns:
            tuple: (السطر المقتطع, النص المتبقي)
        """
        if len(text) <= width:
            return text, ""
        
        # البحث عن أفضل نقطة قطع (مسافة أو علامة ترقيم)
        break_chars = [' ', '-', '،', '؛', '.', ',']
        best_break = -1
        
        # البحث عن أفضل نقطة قطع في النطاق المسموح
        for i in range(width - 1, max(0, width - 10), -1):
            if i < len(text) and text[i] in break_chars:
                best_break = i
                break
        
        if best_break > 0:
            # قطع عند النقطة المناسبة
            line = text[:best_break].strip()
            remaining = text[best_break:].strip()
        else:
            # قطع اضطراري إذا لم توجد نقطة مناسبة
            line = text[:width - 1].strip()
            remaining = text[width - 1:].strip()
        
        return line, remaining
    
    def add_ellipsis_if_needed(self, lines):
        """
        إضافة نقاط في نهاية النص المقتطع
        
        Args:
            lines (list): قائمة الأسطر
            
        Returns:
            list: قائمة الأسطر مع النقاط إذا لزم الأمر
        """
        if not lines:
            return lines
        
        # إضافة نقاط في نهاية السطر الأخير إذا كان هناك مساحة
        last_line = lines[-1]
        if len(last_line) <= self.max_width_chars - 3:
            lines[-1] = last_line + "..."
        elif len(last_line) > 3:
            lines[-1] = last_line[:-3] + "..."
        
        return lines


# إعدادات تقسيم النص حسب حجم الورق
PAPER_SIZE_SETTINGS = {
    "58mm": {
        "max_chars_per_line": 12,
        "max_lines": 2,
        "font_size": "6px",
        "line_height": "1.2",  # محسن من 1.1
        "cell_padding": "0.3mm",  # محسن من 0.2mm
        "page_size": "58mm auto",
        "body_padding": "1.5mm",  # محسن من 1mm
        "margin": "1mm",
        "header_spacing": "1mm",  # جديد
        "info_spacing": "0.8mm",  # جديد
        "total_spacing": "1.2mm"  # جديد
    },
    "80mm": {
        "max_chars_per_line": 18,
        "max_lines": 3,
        "font_size": "7px",
        "line_height": "1.3",  # محسن من 1.2
        "cell_padding": "0.4mm",  # محسن من 0.3mm
        "page_size": "80mm auto",
        "body_padding": "1.5mm",  # محسن من 1mm
        "margin": "1mm",
        "header_spacing": "1.5mm",  # جديد
        "info_spacing": "1mm",  # جديد
        "total_spacing": "1.5mm"  # جديد
    },
    "A4": {
        "max_chars_per_line": 50,
        "max_lines": 5,
        "font_size": "14px",
        "line_height": "1.6",  # محسن من 1.5
        "cell_padding": "5mm",  # محسن من 4mm
        "page_size": "A4",
        "body_padding": "20mm",  # محسن من 15mm
        "margin": "15mm",
        "header_spacing": "5mm",  # جديد
        "info_spacing": "3mm",  # جديد
        "total_spacing": "4mm"  # جديد
    }
}


def validate_paper_size(paper_size):
    """
    التحقق من صحة حجم الورق المحدد
    
    Args:
        paper_size (str): حجم الورق
        
    Returns:
        str: حجم الورق الصحيح أو الافتراضي
    """
    valid_sizes = ["58mm", "80mm", "A4"]
    
    if paper_size in valid_sizes:
        return paper_size
    
    print(f"تحذير: حجم الورق {paper_size} غير مدعوم، استخدام 80mm كافتراضي")
    return "80mm"


def validate_spacing_settings(paper_size):
    """
    التحقق من صحة إعدادات المسافات
    
    Args:
        paper_size (str): حجم الورق
        
    Returns:
        dict: إعدادات المسافات المحققة أو الافتراضية
    """
    if paper_size not in PAPER_SIZE_SETTINGS:
        print(f"تحذير: حجم الورق {paper_size} غير مدعوم، استخدام 80mm كافتراضي")
        return PAPER_SIZE_SETTINGS["80mm"]
    
    settings = PAPER_SIZE_SETTINGS[paper_size].copy()
    
    # التحقق من وجود الإعدادات الجديدة
    required_keys = ["header_spacing", "info_spacing", "total_spacing"]
    for key in required_keys:
        if key not in settings:
            print(f"تحذير: إعداد {key} غير موجود، استخدام قيمة افتراضية")
            settings[key] = "1mm"
    
    return settings


def safe_format_css_value(value, default="1mm"):
    """
    تنسيق آمن لقيم CSS
    
    Args:
        value: القيمة المراد تنسيقها
        default (str): القيمة الافتراضية في حالة الخطأ
        
    Returns:
        str: القيمة المنسقة أو الافتراضية
    """
    try:
        if not value or not isinstance(value, str):
            return default
        return value.strip()
    except Exception:
        return default


def get_page_settings_for_paper_size(paper_size):
    """
    الحصول على إعدادات الصفحة المناسبة لحجم الورق
    
    Args:
        paper_size (str): حجم الورق
        
    Returns:
        dict: إعدادات الصفحة
    """
    # التحقق من صحة حجم الورق أولاً
    validated_paper_size = validate_paper_size(paper_size)
    settings = PAPER_SIZE_SETTINGS.get(validated_paper_size, PAPER_SIZE_SETTINGS["80mm"])
    
    return {
        'page_size': settings['page_size'],
        'margin': settings['margin'],
        'body_padding': settings['body_padding'],
        'font_size': settings['font_size'],
        'line_height': settings['line_height'],
        'cell_padding': settings['cell_padding']
    }


class ProductNameFormatter:
    """فئة لتنسيق أسماء المنتجات حسب حجم الورق"""
    
    def __init__(self, paper_size):
        """
        تهيئة ProductNameFormatter
        
        Args:
            paper_size (str): حجم الورق (58mm, 80mm, A4)
        """
        self.paper_size = paper_size
        self.settings = self._get_safe_paper_settings()
        self.text_wrapper = self._create_text_wrapper()
    
    def format_product_name(self, product_name):
        """
        تنسيق اسم المنتج للعرض في الفاتورة
        
        Args:
            product_name (str): اسم المنتج الأصلي
            
        Returns:
            str: اسم المنتج منسق للعرض في HTML
        """
        try:
            # تنظيف وتأمين اسم المنتج
            safe_name = self._sanitize_product_name(product_name)
            
            # تقسيم النص إلى أسطر
            lines = self.text_wrapper.wrap_text(safe_name)
            
            # تحويل الأسطر إلى HTML
            if len(lines) <= 1:
                return lines[0] if lines else ""
            
            # إنشاء HTML متعدد الأسطر
            html_lines = []
            for line in lines:
                if line:  # تجنب الأسطر الفارغة
                    html_lines.append(f'<div class="product-line">{line}</div>')
            
            return ''.join(html_lines)
        except Exception as e:
            print(f"خطأ في تنسيق اسم المنتج: {str(e)}")
            return sanitize_product_name(product_name) if product_name else "منتج غير محدد"
    
    def _create_text_wrapper(self):
        """
        إنشاء TextWrapper مناسب لحجم الورق
        
        Returns:
            TextWrapper: كائن TextWrapper مكون حسب حجم الورق
        """
        return TextWrapper(
            max_width_chars=self.settings["max_chars_per_line"],
            max_lines=self.settings["max_lines"]
        )
    
    def _get_safe_paper_settings(self):
        """
        الحصول على إعدادات آمنة لحجم الورق
        
        Returns:
            dict: إعدادات حجم الورق أو الإعدادات الافتراضية
        """
        if self.paper_size not in PAPER_SIZE_SETTINGS:
            print(f"تحذير: حجم الورق {self.paper_size} غير مدعوم، استخدام 80mm كافتراضي")
            return PAPER_SIZE_SETTINGS["80mm"]
        return PAPER_SIZE_SETTINGS[self.paper_size]
    
    def _sanitize_product_name(self, product_name):
        """
        تنظيف اسم المنتج من الأحرف الخطيرة
        
        Args:
            product_name (str): اسم المنتج الأصلي
            
        Returns:
            str: اسم المنتج المنظف والآمن
        """
        if not product_name or not isinstance(product_name, str):
            return "منتج غير محدد"
        
        # إزالة المسافات الزائدة
        clean_name = product_name.strip()
        
        # تحويل الأحرف الخاصة إلى HTML entities (بترتيب مهم لتجنب التحويل المزدوج)
        # يجب تحويل & أولاً لتجنب تحويل الـ entities الأخرى
        clean_name = clean_name.replace('&', '&amp;')
        clean_name = clean_name.replace('<', '&lt;')
        clean_name = clean_name.replace('>', '&gt;')
        clean_name = clean_name.replace('"', '&quot;')
        clean_name = clean_name.replace("'", '&#x27;')
        
        return clean_name if clean_name else "منتج غير محدد"


def safe_format_product_name(product_name, paper_size="80mm"):
    """
    تنسيق آمن لاسم المنتج مع معالجة الأخطاء
    
    Args:
        product_name (str): اسم المنتج
        paper_size (str): حجم الورق
        
    Returns:
        str: اسم المنتج منسق أو قيمة افتراضية آمنة
    """
    try:
        if not product_name or not isinstance(product_name, str):
            return "منتج غير محدد"
        
        formatter = ProductNameFormatter(paper_size)
        return formatter.format_product_name(product_name)
    except Exception as e:
        print(f"خطأ في تنسيق اسم المنتج: {str(e)}")
        return sanitize_product_name(product_name) if product_name else "منتج غير محدد"


def sanitize_product_name(product_name):
    """
    تنظيف اسم المنتج من الأحرف الخطيرة (دالة مستقلة)
    
    Args:
        product_name (str): اسم المنتج الأصلي
        
    Returns:
        str: اسم المنتج المنظف والآمن
    """
    try:
        if not product_name or not isinstance(product_name, str):
            return "منتج غير محدد"
        
        # إزالة المسافات الزائدة
        clean_name = str(product_name).strip()
        
        # تحويل الأحرف الخاصة إلى HTML entities (بترتيب مهم لتجنب التحويل المزدوج)
        # يجب تحويل & أولاً لتجنب تحويل الـ entities الأخرى
        clean_name = clean_name.replace('&', '&amp;')
        clean_name = clean_name.replace('<', '&lt;')
        clean_name = clean_name.replace('>', '&gt;')
        clean_name = clean_name.replace('"', '&quot;')
        clean_name = clean_name.replace("'", '&#x27;')
        clean_name = clean_name.replace('\n', ' ')
        clean_name = clean_name.replace('\r', ' ')
        clean_name = clean_name.replace('\t', ' ')
        
        # إزالة المسافات المتعددة
        clean_name = re.sub(r'\s+', ' ', clean_name)
        
        return clean_name if clean_name else "منتج غير محدد"
    except Exception as e:
        print(f"خطأ في تنظيف اسم المنتج: {str(e)}")
        return "منتج غير محدد"


def get_safe_paper_settings(paper_size):
    """
    الحصول على إعدادات آمنة لحجم الورق مع معالجة الأخطاء
    
    Args:
        paper_size (str): حجم الورق المطلوب
        
    Returns:
        dict: إعدادات حجم الورق أو الإعدادات الافتراضية
    """
    try:
        if not paper_size or not isinstance(paper_size, str):
            print("تحذير: حجم الورق غير صحيح، استخدام 80mm كافتراضي")
            return PAPER_SIZE_SETTINGS["80mm"]
        
        paper_size = paper_size.strip()
        if paper_size not in PAPER_SIZE_SETTINGS:
            print(f"تحذير: حجم الورق {paper_size} غير مدعوم، استخدام 80mm كافتراضي")
            return PAPER_SIZE_SETTINGS["80mm"]
        
        return PAPER_SIZE_SETTINGS[paper_size]
    except Exception as e:
        print(f"خطأ في الحصول على إعدادات الورق: {str(e)}")
        return PAPER_SIZE_SETTINGS["80mm"]


class InvoiceDesigner:
    """مصمم الفواتير"""

    def __init__(self, parent=None):
        self.parent = parent
        self.paper_size = self._get_paper_size_from_settings()  # قراءة حجم الورق من الإعدادات
        self.company_name = "اسم الشركة"
        self.company_phone = "رقم الهاتف"
        self.company_address = "عنوان الشركة"
        self.invoice_notes = "ملاحظات الفاتورة"
    
    def _get_paper_size_from_settings(self):
        """قراءة حجم الورق من الإعدادات وتحويله إلى تنسيق مناسب"""
        try:
            from utils.settings_manager import SettingsManager
            settings_manager = SettingsManager()
            paper_width = settings_manager.get_paper_width_safe()
            
            # تحويل العرض بالمليمتر إلى تنسيق نصي
            if paper_width == 58:
                return "58mm"
            elif paper_width == 80:
                return "80mm"
            elif paper_width == 210:
                return "A4"
            else:
                print(f"حجم ورق غير معروف: {paper_width}، استخدام 80mm كافتراضي")
                return "80mm"
                
        except Exception as e:
            print(f"خطأ في قراءة حجم الورق من الإعدادات: {str(e)}")
            return "80mm"  # القيمة الافتراضية الآمنة

    def set_company_info(self, name, phone, address):
        """تعيين معلومات الشركة"""
        self.company_name = name or "اسم الشركة"
        self.company_phone = phone or "رقم الهاتف"
        self.company_address = address or "عنوان الشركة"

    def set_paper_size(self, size):
        """تعيين حجم الورق"""
        self.paper_size = size
    
    def refresh_paper_size_from_settings(self):
        """تحديث حجم الورق من الإعدادات"""
        self.paper_size = self._get_paper_size_from_settings()
        print(f"تم تحديث حجم الورق إلى: {self.paper_size}")

    def set_invoice_notes(self, notes):
        """تعيين ملاحظات الفاتورة"""
        self.invoice_notes = notes or "ملاحظات الفاتورة"

    def generate_invoice_html(self, invoice_data=None, items_data=None):
        """توليد HTML للفاتورة - يدعم البيانات الحقيقية والتجريبية"""
        
        try:
            return self._generate_invoice_html_safe(invoice_data, items_data)
        except Exception as e:
            print(f"خطأ في إنشاء HTML للفاتورة: {str(e)}")
            return self._generate_fallback_html()
    
    def _generate_invoice_html_safe(self, invoice_data=None, items_data=None):
        """الدالة الداخلية الآمنة لتوليد HTML للفاتورة"""
        
        try:
            # إنشاء منسق أسماء المنتجات مع معالجة الأخطاء
            product_formatter = ProductNameFormatter(self.paper_size)
            paper_settings = product_formatter.settings
            
            # الحصول على إعدادات المسافات المحسنة
            spacing_settings = validate_spacing_settings(self.paper_size)
        except Exception as e:
            print(f"خطأ في إنشاء منسق المنتجات: {str(e)}")
            # استخدام إعدادات افتراضية آمنة
            paper_settings = get_safe_paper_settings(self.paper_size)
            spacing_settings = validate_spacing_settings(self.paper_size)

        # تحديد عرض الفاتورة بناءً على حجم الورق - محسن للطباعة الحرارية
        if self.paper_size == "58mm":
            width = "58mm"
            font_size = "8px"
            header_font = "10px"
            table_font = "7px"
        elif self.paper_size == "A4":
            width = "A4"
            font_size = "14px"
            header_font = "20px"
            table_font = "12px"
        else:  # 80mm
            width = "80mm"
            font_size = "9px"
            header_font = "11px"
            table_font = "8px"

        # استخدام البيانات المرسلة أو البيانات التجريبية
        if invoice_data and items_data:
            # بيانات حقيقية
            invoice_number = invoice_data.get('reference_number', 'INV-001')
            invoice_date = invoice_data.get('date', datetime.datetime.now().strftime("%Y/%m/%d %H:%M"))
            customer_name = invoice_data.get('customer_name', 'عميل نقدي')
            total_amount = invoice_data.get('total', 0)

            # إنشاء صفوف الجدول من البيانات الحقيقية
            table_rows = ""
            for item in items_data:
                try:
                    # تنسيق اسم المنتج للعرض متعدد الأسطر مع معالجة الأخطاء
                    formatted_product_name = safe_format_product_name(
                        item.get('product_name', ''), self.paper_size
                    )
                    
                    # التأكد من صحة البيانات الرقمية
                    total_price = float(item.get('total_price', 0))
                    unit_price = float(item.get('unit_price', 0))
                    quantity = item.get('quantity', '')
                    
                    table_rows += f"""
                        <tr>
                            <td>{total_price:.2f}</td>
                            <td>{unit_price:.2f}</td>
                            <td>{quantity}</td>
                            <td class="product-name-cell">{formatted_product_name}</td>
                        </tr>
                    """
                except Exception as e:
                    print(f"خطأ في معالجة عنصر الفاتورة: {str(e)}")
                    # إضافة صف بقيم افتراضية في حالة الخطأ
                    table_rows += f"""
                        <tr>
                            <td>0.00</td>
                            <td>0.00</td>
                            <td>1</td>
                            <td class="product-name-cell">منتج غير محدد</td>
                        </tr>
                    """
        else:
            # بيانات تجريبية
            invoice_number = "INV-001"
            invoice_date = datetime.datetime.now().strftime("%Y/%m/%d %H:%M")
            customer_name = "عميل تجريبي"
            total_amount = 125.00

            table_rows = """
                <tr>
                    <td>50.00</td>
                    <td>25.00</td>
                    <td>2</td>
                    <td>منتج تجريبي 1</td>
                </tr>
                <tr>
                    <td>30.00</td>
                    <td>30.00</td>
                    <td>1</td>
                    <td>منتج تجريبي 2</td>
                </tr>
                <tr>
                    <td>45.00</td>
                    <td>15.00</td>
                    <td>3</td>
                    <td>منتج تجريبي 3</td>
                </tr>
            """

        # التحقق من صحة حجم الورق والحصول على إعدادات الصفحة المناسبة
        validated_paper_size = validate_paper_size(self.paper_size)
        page_settings = get_page_settings_for_paper_size(validated_paper_size)
        
        # تحديد إعدادات CSS حسب حجم الورق
        if self.paper_size == "A4":
            body_width = "100%"
        else:
            body_width = width
        
        # استخراج المتغيرات للاستخدام في HTML
        page_margin = page_settings['margin']
        page_size = page_settings['page_size']
        body_padding = page_settings['body_padding']
        line_height = page_settings['line_height']
        
        # استخراج متغيرات المسافات المحسنة
        header_spacing = safe_format_css_value(spacing_settings.get('header_spacing', '2mm'))
        info_spacing = safe_format_css_value(spacing_settings.get('info_spacing', '1mm'))
        total_spacing = safe_format_css_value(spacing_settings.get('total_spacing', '1.5mm'))

        html = f"""
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <style>
                @page {{
                    margin: {page_margin};
                    size: {page_size};
                }}
                body {{
                    font-family: 'Tahoma', 'Arial', sans-serif;
                    margin: 0;
                    padding: {body_padding};
                    width: {body_width};
                    font-size: {font_size};
                    direction: rtl;
                    text-align: center;
                    line-height: {line_height};
                }}
                .header {
                    text-align: center;
                    margin: 0 auto;
                    padding-bottom: 3mm;
                    border-bottom: 1px solid #000;
                }}
                .company-name {{
                    font-size: {header_font};
                    font-weight: bold;
                    margin: 0 auto 3mm auto;
                    line-height: 1.5;
                    padding: 2mm 0;
                    width: 100%;
                    text-align: center;
                }}
                .company-info {{
                    font-size: {font_size};
                    margin: 0 auto 2mm auto;
                    line-height: 1.5;
                    padding: 0;
                    width: 100%;
                    text-align: center;
                }}
                .invoice-info {{
                    margin: {info_spacing} auto;
                    text-align: center;
                    font-size: {font_size};
                }}
                .invoice-info div {{
                    margin: 0 auto {info_spacing} auto;
                    line-height: 1.3;
                    text-align: center;
                }}
                table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin: 1mm 0;
                    font-size: {table_font};
                }}
                th, td {{
                    border: 1px solid #000;
                    padding: {paper_settings['cell_padding']};
                    text-align: center;
                    line-height: {paper_settings['line_height']};
                    vertical-align: middle;
                }}
                th {{
                    background-color: #f0f0f0;
                    font-weight: bold;
                    font-size: {font_size};
                }}
                .product-name-cell {{
                    text-align: right;
                    padding: {paper_settings['cell_padding']};
                    word-wrap: break-word;
                    word-break: break-word;
                    white-space: normal;
                    vertical-align: top;
                    line-height: {paper_settings['line_height']};
                }}
                .product-line {{
                    margin: 0;
                    padding: 0;
                    line-height: {paper_settings['line_height']};
                    font-size: {paper_settings['font_size']};
                }}
                .total-section {{
                    margin-top: {total_spacing};
                    text-align: center;
                }}
                .total-box {{
                    border: 1px solid #000;
                    padding: {total_spacing};
                    margin: {total_spacing} auto;
                    width: 85%;
                    font-weight: bold;
                    font-size: {header_font};
                    line-height: 1.3;
                    min-height: {total_spacing};
                }}
                .notes {{
                    margin-top: {total_spacing};
                    text-align: center;
                    font-size: {font_size};
                    border-top: 1px solid #000;
                    padding-top: {total_spacing};
                    line-height: 1.3;
                }}
            </style>
        </head>
        <body>
            <center>
                <div class="company-name">{self.company_name}</div>
                <div class="company-info">{self.company_phone}</div>
                <div class="company-info">{self.company_address}</div>
            </center>

            <center>
                <div><strong>رقم الفاتورة:</strong> {invoice_number}</div>
                <div><strong>التاريخ:</strong> {invoice_date}</div>
                <div><strong>العميل:</strong> {customer_name}</div>
            </center>

            <table>
                <thead>
                    <tr>
                        <th>الإجمالي</th>
                        <th>السعر</th>
                        <th>الكمية</th>
                        <th>المنتج</th>
                    </tr>
                </thead>
                <tbody>
                    {table_rows}
                </tbody>
            </table>

            <div class="total-section">
                <div class="total-box">
                    الإجمالي {total_amount:.2f} ج.م
                </div>
            </div>

            <div class="notes">
                {self.invoice_notes}
            </div>
        </body>
        </html>
        """

        return html
    
    def _generate_fallback_html(self):
        """إنشاء HTML احتياطي في حالة الخطأ"""
        return """
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <style>
                body { font-family: Arial; text-align: center; padding: 20px; }
            </style>
        </head>
        <body>
            <h2>خطأ في إنشاء الفاتورة</h2>
            <p>حدث خطأ أثناء إنشاء الفاتورة. يرجى المحاولة مرة أخرى.</p>
        </body>
        </html>
        """

    def generate_compact_invoice_html(self, invoice_data=None, items_data=None):
        """توليد HTML مضغوط للفاتورة - مخصص للطباعة الحرارية"""
        
        try:
            # إنشاء منسق أسماء المنتجات للنسخة المضغوطة مع معالجة الأخطاء
            product_formatter = ProductNameFormatter(self.paper_size)
            paper_settings = product_formatter.settings
            
            # الحصول على إعدادات المسافات المحسنة للنسخة المضغوطة
            spacing_settings = validate_spacing_settings(self.paper_size)
        except Exception as e:
            print(f"خطأ في إنشاء منسق المنتجات (مضغوط): {str(e)}")
            # استخدام إعدادات افتراضية آمنة
            paper_settings = get_safe_paper_settings(self.paper_size)
            spacing_settings = validate_spacing_settings(self.paper_size)

        # تحديد عرض الفاتورة بناءً على حجم الورق - مضغوط أكثر
        if self.paper_size == "58mm":
            width = "58mm"
            font_size = "6px"
            header_font = "8px"
            table_font = "5px"
        elif self.paper_size == "A4":
            width = "A4"
            font_size = "12px"
            header_font = "18px"
            table_font = "10px"
        else:  # 80mm
            width = "80mm"
            font_size = "7px"
            header_font = "9px"
            table_font = "6px"

        # استخدام البيانات المرسلة أو البيانات التجريبية
        if invoice_data and items_data:
            # بيانات حقيقية
            invoice_number = invoice_data.get('reference_number', 'INV-001')
            invoice_date = invoice_data.get('date', datetime.datetime.now().strftime("%Y/%m/%d %H:%M"))
            customer_name = invoice_data.get('customer_name', 'عميل نقدي')
            total_amount = invoice_data.get('total', 0)

            # إنشاء صفوف الجدول من البيانات الحقيقية
            table_rows = ""
            for item in items_data:
                try:
                    # تنسيق اسم المنتج للعرض متعدد الأسطر في النسخة المضغوطة مع معالجة الأخطاء
                    formatted_product_name = safe_format_product_name(
                        item.get('product_name', ''), self.paper_size
                    )
                    
                    # التأكد من صحة البيانات الرقمية
                    total_price = float(item.get('total_price', 0))
                    unit_price = float(item.get('unit_price', 0))
                    quantity = item.get('quantity', '')
                    
                    table_rows += f"""
                        <tr>
                            <td>{total_price:.1f}</td>
                            <td>{unit_price:.1f}</td>
                            <td>{quantity}</td>
                            <td class="product-name-cell">{formatted_product_name}</td>
                        </tr>
                    """
                except Exception as e:
                    print(f"خطأ في معالجة عنصر الفاتورة (مضغوط): {str(e)}")
                    # إضافة صف بقيم افتراضية في حالة الخطأ
                    table_rows += f"""
                        <tr>
                            <td>0.0</td>
                            <td>0.0</td>
                            <td>1</td>
                            <td class="product-name-cell">منتج غير محدد</td>
                        </tr>
                    """
        else:
            # بيانات تجريبية مضغوطة
            invoice_number = "INV-001"
            invoice_date = datetime.datetime.now().strftime("%Y/%m/%d %H:%M")
            customer_name = "عميل تجريبي"
            total_amount = 125.00

            table_rows = """
                <tr><td>50.0</td><td>25.0</td><td>2</td><td>منتج 1</td></tr>
                <tr><td>30.0</td><td>30.0</td><td>1</td><td>منتج 2</td></tr>
                <tr><td>45.0</td><td>15.0</td><td>3</td><td>منتج 3</td></tr>
            """

        # التحقق من صحة حجم الورق والحصول على إعدادات الصفحة المناسبة للنسخة المضغوطة
        validated_paper_size = validate_paper_size(self.paper_size)
        page_settings = get_page_settings_for_paper_size(validated_paper_size)
        
        # تحديد إعدادات CSS حسب حجم الورق للنسخة المضغوطة
        if self.paper_size == "A4":
            page_margin = "8mm"  # هوامش أصغر للنسخة المضغوطة
            body_padding = "8mm"
            page_size = "A4"
            body_width = "100%"
            line_height = "1.2"
        else:
            page_margin = page_settings['margin']
            body_padding = page_settings['body_padding']
            page_size = page_settings['page_size']
            body_width = width
            line_height = page_settings['line_height']
        
        # استخراج متغيرات المسافات المحسنة للنسخة المضغوطة
        header_spacing_compact = safe_format_css_value(spacing_settings.get('header_spacing', '1mm'))
        info_spacing_compact = safe_format_css_value(spacing_settings.get('info_spacing', '0.5mm'))
        total_spacing_compact = safe_format_css_value(spacing_settings.get('total_spacing', '0.5mm'))

        # إنشاء HTML مضغوط للطباعة الحرارية
        html = f"""
        <!DOCTYPE html>
        <html dir="rtl">
        <head>
            <meta charset="UTF-8">
            <style>
                @page {{
                    margin: {page_margin};
                    size: {page_size};
                }}
                body {{
                    font-family: 'Tahoma', 'Arial', sans-serif;
                    margin: 0;
                    padding: {body_padding};
                    width: {body_width};
                    font-size: {font_size};
                    direction: rtl;
                    text-align: center;
                    line-height: {line_height};
                }}
                .header-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: 0 auto {header_spacing_compact} auto;
                    border-bottom: 1px solid #000;
                    padding-bottom: {header_spacing_compact};
                    text-align: center;
                }
                .header-table td {
                    text-align: center;
                    padding: 0.5mm;
                }
                .invoice-info-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin: {info_spacing_compact} auto;
                    text-align: center;
                }
                .invoice-info-table td {
                    text-align: center;
                    padding: 0.5mm;
                }}
                .company-name {
                    font-size: {header_font};
                    font-weight: bold;
                    margin: 0 auto;
                    line-height: 1.3;
                    padding: 1mm 0;
                    text-align: center;
                }}
                .company-info {
                    font-size: {font_size};
                    margin: 0 auto;
                    line-height: 1.3;
                    padding: 0;
                    text-align: center;
                }}
                .invoice-info {{
                    margin: {info_spacing_compact} auto;
                    text-align: center;
                    font-size: {font_size};
                }}
                .invoice-info div {{
                    margin: 0 auto {info_spacing_compact} auto;
                    line-height: 1.1;
                    text-align: center;
                }}
                table {{
                    width: 100%;
                    border-collapse: collapse;
                    margin: 0.5mm 0;
                    font-size: {table_font};
                }}
                th, td {{
                    border: 1px solid #000;
                    padding: 0.2mm;
                    text-align: center;
                    line-height: 0.9;
                    vertical-align: middle;
                }}
                th {{
                    background-color: #f0f0f0;
                    font-weight: bold;
                    font-size: {font_size};
                }}
                .product-name-cell {{
                    text-align: right;
                    padding: 0.2mm;
                    word-wrap: break-word;
                    word-break: break-word;
                    white-space: normal;
                    vertical-align: top;
                    line-height: 0.9;
                }}
                .product-line {{
                    margin: 0;
                    padding: 0;
                    line-height: 0.9;
                    font-size: {table_font};
                }}
                .total-section {{
                    margin-top: {total_spacing_compact};
                    text-align: center;
                }}
                .total-box {{
                    border: 1px solid #000;
                    padding: {total_spacing_compact};
                    margin: {total_spacing_compact} auto;
                    width: 85%;
                    font-weight: bold;
                    font-size: {header_font};
                    line-height: 1.2;
                    min-height: {total_spacing_compact};
                }}
                .notes {{
                    margin-top: {total_spacing_compact};
                    text-align: center;
                    font-size: {font_size};
                    border-top: 1px solid #000;
                    padding-top: {total_spacing_compact};
                    line-height: 1.1;
                }}
            </style>
        </head>
        <body>
            <center>
                <div class="company-name">{self.company_name}</div>
                <div class="company-info">{self.company_phone}</div>
                <div class="company-info">{self.company_address}</div>
            </center>

            <center>
                <div><strong>رقم الفاتورة:</strong> {invoice_number}</div>
                <div><strong>التاريخ:</strong> {invoice_date}</div>
                <div><strong>العميل:</strong> {customer_name}</div>
            </center>

            <table>
                <thead>
                    <tr>
                        <th>الإجمالي</th>
                        <th>السعر</th>
                        <th>الكمية</th>
                        <th>المنتج</th>
                    </tr>
                </thead>
                <tbody>
                    {table_rows}
                </tbody>
            </table>

            <div class="total-section">
                <div class="total-box">
                    الإجمالي {total_amount:.1f} ج.م
                </div>
            </div>

            <div class="notes">
                {self.invoice_notes}
            </div>
        </body>
        </html>
        """

        return html

    def generate_sample_invoice_html(self):
        """توليد HTML لفاتورة تجريبية - للتوافق مع الكود القديم"""
        return self.generate_invoice_html()

    def show_preview_dialog(self):
        """عرض نافذة معاينة الفاتورة"""
        dialog = QDialog(self.parent)
        dialog.setWindowTitle("معاينة تصميم الفاتورة")
        dialog.setModal(True)

        # استخدام حجم مناسب مثل معاينة المبيعات
        dialog.setMinimumSize(350, 500)
        dialog.setMaximumSize(450, 650)
        dialog.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # تخطيط النافذة
        layout = QVBoxLayout(dialog)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(8)

        # عنوان النافذة (مبسط)
        title_label = QLabel("معاينة تصميم الفاتورة")
        title_label.setFont(QFont("Arial", 14, QFont.Bold))
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setStyleSheet("""
            QLabel {
                color: #2c3e50;
                padding: 8px;
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 5px;
            }
        """)
        layout.addWidget(title_label)

        # معلومات حجم الورق (مبسط)
        size_info = QLabel(f"حجم الورق: {self.paper_size}")
        size_info.setFont(QFont("Arial", 10))
        size_info.setAlignment(Qt.AlignmentFlag.AlignCenter)
        size_info.setStyleSheet("""
            QLabel {
                color: #495057;
                padding: 3px;
                background-color: #e9ecef;
                border-radius: 3px;
            }
        """)
        layout.addWidget(size_info)

        # رسالة إعلامية بدلاً من المعاينة
        info_label = QLabel("تم إزالة نظام المعاينة من الإعدادات.\nيمكنك طباعة الفواتير مباشرة من تاب المبيعات.")
        info_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        info_label.setStyleSheet("""
            QLabel {
                color: #495057;
                font-size: 14px;
                padding: 30px;
                border: 2px solid #dee2e6;
                border-radius: 5px;
                background-color: #f8f9fa;
                margin: 10px;
            }
        """)
        layout.addWidget(info_label)

        # زر الإغلاق فقط
        buttons_layout = QHBoxLayout()

        close_btn = QPushButton("إغلاق")
        close_btn.setFont(QFont("Arial", 9))
        close_btn.setStyleSheet("""
            QPushButton {
                background-color: #6c757d;
                color: white;
                border: none;
                padding: 8px 15px;
                border-radius: 5px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #545b62;
            }
        """)
        close_btn.clicked.connect(dialog.accept)

        buttons_layout.addStretch()
        buttons_layout.addWidget(close_btn)
        buttons_layout.addStretch()

        layout.addLayout(buttons_layout)

        # عرض النافذة
        dialog.exec_()



    @staticmethod
    def create_invoice_widget(invoice_data, items_data, is_preview=True):
        """إنشاء widget الفاتورة باستخدام التصميم المرئي الفعلي"""
        try:
            print("بدء إنشاء widget الفاتورة...")

            # التحقق من صحة البيانات
            if not invoice_data or not items_data:
                print("بيانات الفاتورة غير صحيحة")
                return InvoiceDesigner._create_fallback_widget("بيانات الفاتورة غير صحيحة")

            # محاولة استيراد النظام الجديد
            try:
                from utils.thermal_printer_helper import ESCPOSInvoicePrinter
                print("تم استيراد ESCPOSInvoicePrinter بنجاح")
            except ImportError as import_error:
                print(f"فشل في استيراد ESCPOSInvoicePrinter: {str(import_error)}")
                return InvoiceDesigner._create_fallback_widget("فشل في تحميل نظام المعاينة")

            # قراءة إعدادات الشركة
            try:
                settings = QSettings()
                company_info = {
                    'name': settings.value("company_name", "اسم الشركة"),
                    'phone': settings.value("company_phone", "رقم الهاتف"),
                    'address': settings.value("company_address", "عنوان الشركة"),
                    'notes': settings.value("invoice_notes", "شكراً لتعاملكم معنا")
                }
                print("تم قراءة إعدادات الشركة")
            except Exception as settings_error:
                print(f"خطأ في قراءة الإعدادات: {str(settings_error)}")
                company_info = {
                    'name': "اسم الشركة",
                    'phone': "رقم الهاتف",
                    'address': "عنوان الشركة",
                    'notes': "شكراً لتعاملكم معنا"
                }

            # إنشاء طابعة ESC/POS لاستخدام نظام المعاينة
            try:
                escpos_printer = ESCPOSInvoicePrinter()
                print("تم إنشاء ESCPOSInvoicePrinter")
            except Exception as printer_error:
                print(f"خطأ في إنشاء الطابعة: {str(printer_error)}")
                return InvoiceDesigner._create_fallback_widget("فشل في إنشاء نظام الطباعة")

            # إنشاء widget المعاينة باستخدام نفس التصميم المرئي
            try:
                preview_widget = escpos_printer.create_preview_widget(
                    invoice_data, items_data, company_info
                )

                if preview_widget is None:
                    print("فشل في إنشاء widget المعاينة")
                    return InvoiceDesigner._create_fallback_widget("فشل في إنشاء المعاينة")

                print("تم إنشاء widget المعاينة بنجاح")
                return preview_widget

            except Exception as widget_error:
                print(f"خطأ في إنشاء widget المعاينة: {str(widget_error)}")
                return InvoiceDesigner._create_fallback_widget(f"خطأ في المعاينة: {str(widget_error)}")

        except Exception as e:
            print(f"خطأ عام في إنشاء widget الفاتورة: {str(e)}")
            import traceback
            traceback.print_exc()
            return InvoiceDesigner._create_fallback_widget(f"خطأ عام: {str(e)}")

    @staticmethod
    def _create_fallback_widget(error_message):
        """إنشاء widget احتياطي في حالة الخطأ"""
        try:
            from PyQt5.QtWidgets import QWidget, QLabel, QVBoxLayout, QTextEdit
            from PyQt5.QtCore import Qt

            print(f"إنشاء widget احتياطي: {error_message}")

            # إنشاء widget بسيط يعرض رسالة الخطأ
            fallback_widget = QWidget()
            layout = QVBoxLayout(fallback_widget)

            # عنوان الخطأ
            error_title = QLabel("خطأ في عرض المعاينة")
            error_title.setAlignment(Qt.AlignmentFlag.AlignCenter)
            error_title.setStyleSheet("""
                QLabel {
                    color: #e74c3c;
                    font-weight: bold;
                    font-size: 14px;
                    padding: 10px;
                }
            """)

            # تفاصيل الخطأ
            error_details = QLabel(error_message)
            error_details.setAlignment(Qt.AlignmentFlag.AlignCenter)
            error_details.setWordWrap(True)
            error_details.setStyleSheet("""
                QLabel {
                    color: #7f8c8d;
                    padding: 10px;
                    border: 1px solid #bdc3c7;
                    background-color: #ecf0f1;
                }
            """)

            layout.addWidget(error_title)
            layout.addWidget(error_details)

            fallback_widget.setFixedSize(300, 150)
            return fallback_widget

        except Exception as fallback_error:
            print(f"خطأ في إنشاء widget الاحتياطي: {str(fallback_error)}")
            # إرجاع None في حالة فشل كل شيء
            return None
