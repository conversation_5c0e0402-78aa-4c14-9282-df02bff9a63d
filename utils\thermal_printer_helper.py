# -*- coding: utf-8 -*-
"""
مساعد الطباعة الحرارية - نسخة محسنة
يحتوي على الدوال المستخدمة فقط لتحسين الأداء والوضوح
مع نظام تسجيل محسن ومعالجة أخطاء أفضل
"""

import os
import tempfile
import subprocess
import time
import logging
from datetime import datetime
from PyQt5.QtPrintSupport import QPrinter, QPrintDialog, QPrinterInfo
from PyQt5.QtCore import QSizeF, Qt, QSettings, QRect
from PyQt5.QtGui import QPainter, QFont, QPixmap, QPen, QBrush, QFontMetrics
from PyQt5.QtWidgets import QWidget, QVBoxLayout, QLabel

# إعداد نظام التسجيل
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/thermal_printer.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('ThermalPrinter')

# استيرادات إضافية للطباعة المحسنة
try:
    import win32print
    import win32ui
    WIN32_AVAILABLE = True
    logger.info("تم تحميل win32print بنجاح")
except ImportError:
    WIN32_AVAILABLE = False
    logger.warning("win32print غير متوفر - بعض طرق الطباعة قد لا تعمل")


class PrinterDetector:
    """فئة اكتشاف الطابعات المتاحة والتحقق من حالتها"""
    
    def __init__(self):
        """تهيئة كاشف الطابعات"""
        self.available_printers = []
        self._refresh_printers()
    
    def _refresh_printers(self):
        """تحديث قائمة الطابعات المتاحة"""
        try:
            self.available_printers = self.detect_printers()
        except Exception as e:
            print(f"خطأ في تحديث قائمة الطابعات: {str(e)}")
            self.available_printers = []
    
    def detect_printers(self):
        """اكتشاف الطابعات المتاحة في النظام
        
        Returns:
            List[str]: قائمة بأسماء الطابعات المتاحة
        """
        printers = []
        
        try:
            # استخدام QPrinter للحصول على الطابعات المتاحة
            from PyQt5.QtPrintSupport import QPrinterInfo
            
            # الحصول على جميع الطابعات المتاحة
            available_printers = QPrinterInfo.availablePrinters()
            
            for printer_info in available_printers:
                printer_name = printer_info.printerName()
                if printer_name:
                    printers.append(printer_name)
            
            print(f"تم اكتشاف {len(printers)} طابعة: {printers}")
            
        except Exception as e:
            print(f"خطأ في اكتشاف الطابعات باستخدام QPrinter: {str(e)}")
            
            # محاولة بديلة باستخدام win32print إذا كان متاحاً
            if WIN32_AVAILABLE:
                try:
                    printers = self._detect_printers_win32()
                except Exception as win32_error:
                    print(f"خطأ في اكتشاف الطابعات باستخدام win32print: {str(win32_error)}")
        
        return printers
    
    def _detect_printers_win32(self):
        """اكتشاف الطابعات باستخدام win32print (Windows فقط)
        
        Returns:
            List[str]: قائمة بأسماء الطابعات
        """
        printers = []
        
        if not WIN32_AVAILABLE:
            return printers
        
        try:
            # الحصول على قائمة الطابعات المحلية والشبكية
            printer_list = win32print.EnumPrinters(
                win32print.PRINTER_ENUM_LOCAL | win32print.PRINTER_ENUM_CONNECTIONS
            )
            
            for printer in printer_list:
                printer_name = printer[2]  # اسم الطابعة
                if printer_name:
                    printers.append(printer_name)
            
            print(f"تم اكتشاف {len(printers)} طابعة باستخدام win32print")
            
        except Exception as e:
            print(f"خطأ في win32print.EnumPrinters: {str(e)}")
        
        return printers
    
    def is_printer_available(self, printer_name=None):
        """التحقق من توفر طابعة محددة أو أي طابعة
        
        Args:
            printer_name (str, optional): اسم الطابعة للتحقق منها. 
                                        إذا كان None، يتم التحقق من وجود أي طابعة
        
        Returns:
            bool: True إذا كانت الطابعة متاحة، False خلاف ذلك
        """
        try:
            # تحديث قائمة الطابعات بشكل إجباري
            self.available_printers = self.detect_printers()
            
            # طباعة معلومات تشخيصية
            print(f"قائمة الطابعات المتاحة: {self.available_printers}")
            
            # إذا لم يتم تحديد طابعة، تحقق من وجود أي طابعة
            if printer_name is None:
                # استخدام الطابعة الافتراضية إذا كانت متاحة
                default_printer = self.get_default_printer()
                if default_printer:
                    print(f"استخدام الطابعة الافتراضية: {default_printer}")
                    return True
                
                # التحقق من وجود أي طابعة
                has_printers = len(self.available_printers) > 0
                print(f"هل توجد طابعات متاحة: {'نعم' if has_printers else 'لا'}")
                return has_printers
            
            # التحقق من طابعة محددة
            printer_exists = printer_name in self.available_printers
            
            # محاولة إضافية باستخدام win32print إذا لم يتم العثور على الطابعة
            if not printer_exists and WIN32_AVAILABLE:
                try:
                    # محاولة الحصول على معلومات الطابعة مباشرة
                    printer_handle = win32print.OpenPrinter(printer_name, {})
                    if printer_handle:
                        win32print.ClosePrinter(printer_handle)
                        print(f"تم العثور على الطابعة {printer_name} باستخدام win32print")
                        return True
                except Exception as win32_error:
                    print(f"لم يتم العثور على الطابعة {printer_name} باستخدام win32print: {str(win32_error)}")
            
            print(f"نتيجة التحقق من الطابعة {printer_name}: {'متاحة' if printer_exists else 'غير متاحة'}")
            return printer_exists
            
        except Exception as e:
            print(f"خطأ في التحقق من توفر الطابعة: {str(e)}")
            # محاولة أخيرة: افتراض أن الطابعة متاحة إذا كانت الطابعة الافتراضية
            if printer_name is None:
                print("افتراض أن الطابعة الافتراضية متاحة")
                return True
            return False
    
    def get_default_printer(self):
        """الحصول على الطابعة الافتراضية
        
        Returns:
            str or None: اسم الطابعة الافتراضية أو None إذا لم توجد
        """
        try:
            # استخدام QPrinterInfo للحصول على الطابعة الافتراضية
            from PyQt5.QtPrintSupport import QPrinterInfo
            
            default_printer = QPrinterInfo.defaultPrinter()
            if default_printer.isNull():
                print("لا توجد طابعة افتراضية محددة")
                return None
            
            printer_name = default_printer.printerName()
            print(f"الطابعة الافتراضية: {printer_name}")
            return printer_name
            
        except Exception as e:
            print(f"خطأ في الحصول على الطابعة الافتراضية: {str(e)}")
            
            # محاولة بديلة باستخدام win32print
            if WIN32_AVAILABLE:
                try:
                    return self._get_default_printer_win32()
                except Exception as win32_error:
                    print(f"خطأ في win32print للطابعة الافتراضية: {str(win32_error)}")
            
            return None
    
    def _get_default_printer_win32(self):
        """الحصول على الطابعة الافتراضية باستخدام win32print
        
        Returns:
            str or None: اسم الطابعة الافتراضية
        """
        if not WIN32_AVAILABLE:
            return None
        
        try:
            default_printer = win32print.GetDefaultPrinter()
            print(f"الطابعة الافتراضية (win32): {default_printer}")
            return default_printer
        except Exception as e:
            print(f"خطأ في win32print.GetDefaultPrinter: {str(e)}")
            return None
    
    def test_printer_connection(self, printer_name):
        """اختبار الاتصال بطابعة محددة
        
        Args:
            printer_name (str): اسم الطابعة لاختبارها
        
        Returns:
            bool: True إذا كان الاتصال ناجحاً، False خلاف ذلك
        """
        try:
            # التحقق من وجود الطابعة في القائمة أولاً
            if not self.is_printer_available(printer_name):
                print(f"الطابعة {printer_name} غير موجودة في قائمة الطابعات المتاحة")
                return False
            
            # محاولة إنشاء QPrinter للطابعة المحددة
            printer = QPrinter()
            printer.setPrinterName(printer_name)
            
            # التحقق من صحة الطابعة
            if printer.isValid():
                print(f"اختبار الاتصال بالطابعة {printer_name}: ناجح")
                return True
            else:
                print(f"اختبار الاتصال بالطابعة {printer_name}: فاشل - الطابعة غير صحيحة")
                return False
                
        except Exception as e:
            print(f"خطأ في اختبار الاتصال بالطابعة {printer_name}: {str(e)}")
            return False
    
    def get_printer_status(self, printer_name):
        """الحصول على حالة طابعة محددة
        
        Args:
            printer_name (str): اسم الطابعة
        
        Returns:
            dict: معلومات حالة الطابعة
        """
        status = {
            'available': False,
            'is_default': False,
            'connection_ok': False,
            'error_message': ''
        }
        
        try:
            # التحقق من التوفر
            status['available'] = self.is_printer_available(printer_name)
            
            # التحقق من كونها الطابعة الافتراضية
            default_printer = self.get_default_printer()
            status['is_default'] = (default_printer == printer_name)
            
            # اختبار الاتصال
            if status['available']:
                status['connection_ok'] = self.test_printer_connection(printer_name)
            
        except Exception as e:
            status['error_message'] = str(e)
            print(f"خطأ في الحصول على حالة الطابعة {printer_name}: {str(e)}")
        
        return status
    
    def get_available_printers_list(self):
        """الحصول على قائمة الطابعات المتاحة مع معلومات إضافية
        
        Returns:
            List[dict]: قائمة بمعلومات الطابعات المتاحة
        """
        printers_info = []
        
        try:
            self._refresh_printers()
            default_printer = self.get_default_printer()
            
            for printer_name in self.available_printers:
                printer_info = {
                    'name': printer_name,
                    'is_default': (printer_name == default_printer),
                    'connection_ok': self.test_printer_connection(printer_name)
                }
                printers_info.append(printer_info)
            
        except Exception as e:
            print(f"خطأ في الحصول على قائمة الطابعات: {str(e)}")
        
        return printers_info


class PathManager:
    """فئة إدارة مسارات الملفات وأسماء الملفات"""
    
    def __init__(self):
        """تهيئة مدير المسارات"""
        self.base_pdf_directory = "invoices_pdf"
        self.invalid_chars = ['<', '>', ':', '"', '/', '\\', '|', '?', '*']
    
    def get_base_pdf_directory(self):
        """الحصول على المجلد الأساسي لحفظ PDF
        
        Returns:
            str: مسار المجلد الأساسي
        """
        try:
            # الحصول على مجلد المستندات للمستخدم
            documents_path = self.get_user_documents_path()
            
            # إنشاء مسار المجلد الأساسي
            base_path = os.path.join(documents_path, self.base_pdf_directory)
            
            return base_path
            
        except Exception as e:
            print(f"خطأ في الحصول على المجلد الأساسي: {str(e)}")
            # استخدام مجلد المشروع الحالي كبديل
            return os.path.join(os.getcwd(), self.base_pdf_directory)
    
    def get_user_documents_path(self):
        """الحصول على مجلد المستندات للمستخدم
        
        Returns:
            str: مسار مجلد المستندات
        """
        try:
            # محاولة الحصول على مجلد المستندات في Windows
            if os.name == 'nt':  # Windows
                import winreg
                try:
                    # قراءة مسار مجلد المستندات من الريجستري
                    key = winreg.OpenKey(
                        winreg.HKEY_CURRENT_USER,
                        r"Software\Microsoft\Windows\CurrentVersion\Explorer\Shell Folders"
                    )
                    documents_path = winreg.QueryValueEx(key, "Personal")[0]
                    winreg.CloseKey(key)
                    return documents_path
                except:
                    # بديل: استخدام متغير البيئة
                    return os.path.expanduser("~/Documents")
            else:
                # للأنظمة الأخرى (Linux, Mac)
                return os.path.expanduser("~/Documents")
                
        except Exception as e:
            print(f"خطأ في الحصول على مجلد المستندات: {str(e)}")
            # استخدام المجلد الحالي كبديل
            return os.getcwd()
    
    def create_date_based_path(self, base_path, date_str):
        """إنشاء مسار منظم حسب التاريخ
        
        Args:
            base_path (str): المسار الأساسي
            date_str (str): التاريخ بصيغة YYYY-MM-DD أو مشابهة
        
        Returns:
            str: المسار المنظم حسب التاريخ
        """
        try:
            # استخراج السنة والشهر من التاريخ
            if len(date_str) >= 10:  # YYYY-MM-DD format
                year = date_str[:4]
                month = date_str[5:7]
            elif len(date_str) >= 8:  # YYYYMMDD format
                year = date_str[:4]
                month = date_str[4:6]
            else:
                # استخدام التاريخ الحالي كبديل
                from datetime import datetime
                now = datetime.now()
                year = str(now.year)
                month = f"{now.month:02d}"
            
            # إنشاء المسار المنظم
            organized_path = os.path.join(base_path, year, month)
            
            return organized_path
            
        except Exception as e:
            print(f"خطأ في إنشاء مسار منظم حسب التاريخ: {str(e)}")
            # إرجاع المسار الأساسي كبديل
            return base_path
    
    def sanitize_filename(self, filename):
        """تنظيف اسم الملف من الأحرف غير المسموحة
        
        Args:
            filename (str): اسم الملف الأصلي
        
        Returns:
            str: اسم الملف المنظف
        """
        try:
            # إزالة الأحرف غير المسموحة
            clean_filename = filename
            
            for char in self.invalid_chars:
                clean_filename = clean_filename.replace(char, '_')
            
            # إزالة المسافات الزائدة والنقاط في البداية والنهاية
            clean_filename = clean_filename.strip(' .')
            
            # التأكد من أن اسم الملف ليس فارغاً
            if not clean_filename:
                clean_filename = "invoice"
            
            # قطع اسم الملف إذا كان طويلاً جداً (Windows limit is 255 chars)
            if len(clean_filename) > 200:  # ترك مساحة للامتداد والمسار
                clean_filename = clean_filename[:200]
            
            return clean_filename
            
        except Exception as e:
            print(f"خطأ في تنظيف اسم الملف: {str(e)}")
            return "invoice"  # اسم افتراضي آمن
    
    def generate_safe_filename(self, invoice_number, date_str, customer_name=""):
        """إنشاء اسم ملف آمن ومنظم
        
        Args:
            invoice_number (str): رقم الفاتورة
            date_str (str): تاريخ الفاتورة
            customer_name (str, optional): اسم العميل
        
        Returns:
            str: اسم الملف الآمن بدون امتداد
        """
        try:
            # تنظيف المكونات
            clean_invoice_number = self.sanitize_filename(str(invoice_number))
            clean_date = self.sanitize_filename(str(date_str))
            
            # إنشاء اسم الملف الأساسي
            filename_parts = ["invoice", clean_invoice_number]
            
            # إضافة التاريخ إذا كان متاحاً
            if clean_date and clean_date != "invoice":
                # تحويل التاريخ إلى صيغة مناسبة لاسم الملف
                date_for_filename = clean_date.replace('-', '').replace('/', '').replace('\\', '')
                if date_for_filename:
                    filename_parts.append(date_for_filename)
            
            # إضافة اسم العميل إذا كان متاحاً ومناسباً
            if customer_name:
                clean_customer = self.sanitize_filename(str(customer_name))
                if clean_customer and clean_customer != "invoice" and len(clean_customer) <= 20:
                    filename_parts.append(clean_customer)
            
            # دمج الأجزاء
            filename = "_".join(filename_parts)
            
            # التأكد من أن اسم الملف ليس طويلاً جداً
            if len(filename) > 150:
                # اختصار اسم الملف مع الحفاظ على رقم الفاتورة
                filename = f"invoice_{clean_invoice_number}_{clean_date[:8]}"
            
            return filename
            
        except Exception as e:
            print(f"خطأ في إنشاء اسم ملف آمن: {str(e)}")
            # إنشاء اسم افتراضي بالوقت الحالي
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            return f"invoice_{timestamp}"
    
    def add_timestamp_to_filename(self, filename):
        """إضافة طابع زمني لاسم الملف
        
        Args:
            filename (str): اسم الملف الأصلي (بدون امتداد)
        
        Returns:
            str: اسم الملف مع الطابع الزمني
        """
        try:
            from datetime import datetime
            timestamp = datetime.now().strftime("%H%M%S")
            return f"{filename}_{timestamp}"
        except Exception as e:
            print(f"خطأ في إضافة طابع زمني: {str(e)}")
            return filename
    
    def validate_path(self, path):
        """التحقق من صحة المسار
        
        Args:
            path (str): المسار للتحقق منه
        
        Returns:
            dict: نتيجة التحقق مع معلومات إضافية
        """
        result = {
            'valid': False,
            'exists': False,
            'writable': False,
            'error_message': ''
        }
        
        try:
            # التحقق من أن المسار ليس فارغاً
            if not path:
                result['error_message'] = "المسار فارغ"
                return result
            
            # التحقق من صحة المسار
            normalized_path = os.path.normpath(path)
            
            # التحقق من وجود المسار
            result['exists'] = os.path.exists(normalized_path)
            
            # التحقق من إمكانية الكتابة
            if result['exists']:
                result['writable'] = os.access(normalized_path, os.W_OK)
            else:
                # التحقق من إمكانية إنشاء المسار
                parent_dir = os.path.dirname(normalized_path)
                if os.path.exists(parent_dir):
                    result['writable'] = os.access(parent_dir, os.W_OK)
                else:
                    result['writable'] = False
            
            # المسار صحيح إذا كان قابلاً للكتابة
            result['valid'] = result['writable']
            
            if not result['valid']:
                if not result['exists']:
                    result['error_message'] = "المسار غير موجود ولا يمكن إنشاؤه"
                elif not result['writable']:
                    result['error_message'] = "لا توجد صلاحية كتابة في المسار"
            
        except Exception as e:
            result['error_message'] = f"خطأ في التحقق من المسار: {str(e)}"
        
        return result
    
    def ensure_path_exists(self, path):
        """التأكد من وجود المسار وإنشاؤه إذا لم يكن موجوداً
        
        Args:
            path (str): المسار للتأكد من وجوده
        
        Returns:
            bool: True إذا تم إنشاء المسار بنجاح أو كان موجوداً، False خلاف ذلك
        """
        try:
            if not path:
                return False
            
            # تطبيع المسار
            normalized_path = os.path.normpath(path)
            
            # إنشاء المسار إذا لم يكن موجوداً
            if not os.path.exists(normalized_path):
                os.makedirs(normalized_path, exist_ok=True)
                print(f"تم إنشاء المسار: {normalized_path}")
            
            # التحقق من نجاح الإنشاء
            return os.path.exists(normalized_path)
            
        except Exception as e:
            print(f"خطأ في إنشاء المسار {path}: {str(e)}")
            return False


class ConfigManager:
    """فئة إدارة إعدادات PDF والتكوين"""
    
    def __init__(self):
        """تهيئة مدير الإعدادات"""
        try:
            self.settings = QSettings("MyCompany", "SmartManager")
            self.pdf_settings_group = "pdf_settings"
            self._initialize_default_settings()
        except Exception as e:
            print(f"تحذير: خطأ في تهيئة QSettings: {str(e)}")
            self.settings = None
    
    def _initialize_default_settings(self):
        """تهيئة الإعدادات الافتراضية"""
        try:
            if not self.settings:
                return
            
            # التحقق من وجود الإعدادات وإنشاء القيم الافتراضية إذا لم تكن موجودة
            defaults = {
                f"{self.pdf_settings_group}/save_directory": "",
                f"{self.pdf_settings_group}/auto_open": False,
                f"{self.pdf_settings_group}/quality": "high",
                f"{self.pdf_settings_group}/include_date_folder": True,
                f"{self.pdf_settings_group}/filename_format": "invoice_{number}_{date}",
                f"{self.pdf_settings_group}/auto_pdf_enabled": True,
                f"{self.pdf_settings_group}/compression_level": 6,
                f"{self.pdf_settings_group}/paper_size_auto": True
            }
            
            for key, default_value in defaults.items():
                if not self.settings.contains(key):
                    self.settings.setValue(key, default_value)
            
            # حفظ الإعدادات
            self.settings.sync()
            
        except Exception as e:
            print(f"خطأ في تهيئة الإعدادات الافتراضية: {str(e)}")
    
    def get_pdf_save_directory(self):
        """الحصول على مجلد حفظ PDF المحدد
        
        Returns:
            str: مسار مجلد حفظ PDF
        """
        try:
            if not self.settings:
                # استخدام PathManager للحصول على المسار الافتراضي
                path_manager = PathManager()
                return path_manager.get_base_pdf_directory()
            
            # قراءة المسار المحفوظ
            saved_path = self.settings.value(
                f"{self.pdf_settings_group}/save_directory", 
                "", 
                type=str
            )
            
            # إذا لم يكن هناك مسار محفوظ، استخدم المسار الافتراضي
            if not saved_path:
                path_manager = PathManager()
                default_path = path_manager.get_base_pdf_directory()
                self.set_pdf_save_directory(default_path)
                return default_path
            
            return saved_path
            
        except Exception as e:
            print(f"خطأ في الحصول على مجلد حفظ PDF: {str(e)}")
            # إرجاع مسار افتراضي آمن
            path_manager = PathManager()
            return path_manager.get_base_pdf_directory()
    
    def set_pdf_save_directory(self, directory):
        """تعيين مجلد حفظ PDF
        
        Args:
            directory (str): مسار المجلد الجديد
        
        Returns:
            bool: True إذا تم الحفظ بنجاح، False خلاف ذلك
        """
        try:
            if not self.settings:
                print("تحذير: إعدادات QSettings غير متاحة")
                return False
            
            # التحقق من صحة المسار
            path_manager = PathManager()
            validation = path_manager.validate_path(directory)
            
            if not validation['valid']:
                print(f"مسار غير صحيح: {validation['error_message']}")
                return False
            
            # حفظ المسار
            self.settings.setValue(f"{self.pdf_settings_group}/save_directory", directory)
            self.settings.sync()
            
            print(f"تم حفظ مجلد PDF: {directory}")
            return True
            
        except Exception as e:
            print(f"خطأ في حفظ مجلد PDF: {str(e)}")
            return False
    
    def get_pdf_quality_settings(self):
        """الحصول على إعدادات جودة PDF
        
        Returns:
            dict: إعدادات الجودة
        """
        try:
            if not self.settings:
                return self._get_default_quality_settings()
            
            quality = self.settings.value(
                f"{self.pdf_settings_group}/quality", 
                "high", 
                type=str
            )
            
            compression_level = self.settings.value(
                f"{self.pdf_settings_group}/compression_level", 
                6, 
                type=int
            )
            
            paper_size_auto = self.settings.value(
                f"{self.pdf_settings_group}/paper_size_auto", 
                True, 
                type=bool
            )
            
            return {
                'quality': quality,
                'compression_level': compression_level,
                'paper_size_auto': paper_size_auto,
                'resolution': self._get_resolution_for_quality(quality)
            }
            
        except Exception as e:
            print(f"خطأ في الحصول على إعدادات الجودة: {str(e)}")
            return self._get_default_quality_settings()
    
    def _get_default_quality_settings(self):
        """الحصول على إعدادات الجودة الافتراضية"""
        return {
            'quality': 'high',
            'compression_level': 6,
            'paper_size_auto': True,
            'resolution': 300
        }
    
    def _get_resolution_for_quality(self, quality):
        """الحصول على دقة الوضوح حسب مستوى الجودة
        
        Args:
            quality (str): مستوى الجودة
        
        Returns:
            int: دقة الوضوح بـ DPI
        """
        quality_map = {
            'low': 150,
            'medium': 200,
            'high': 300,
            'ultra': 600
        }
        
        return quality_map.get(quality.lower(), 300)
    
    def set_pdf_quality_settings(self, quality="high", compression_level=6, paper_size_auto=True):
        """تعيين إعدادات جودة PDF
        
        Args:
            quality (str): مستوى الجودة (low, medium, high, ultra)
            compression_level (int): مستوى الضغط (0-9)
            paper_size_auto (bool): تحديد حجم الورق تلقائياً
        
        Returns:
            bool: True إذا تم الحفظ بنجاح
        """
        try:
            if not self.settings:
                return False
            
            # التحقق من صحة القيم
            valid_qualities = ['low', 'medium', 'high', 'ultra']
            if quality.lower() not in valid_qualities:
                quality = 'high'
            
            if not (0 <= compression_level <= 9):
                compression_level = 6
            
            # حفظ الإعدادات
            self.settings.setValue(f"{self.pdf_settings_group}/quality", quality)
            self.settings.setValue(f"{self.pdf_settings_group}/compression_level", compression_level)
            self.settings.setValue(f"{self.pdf_settings_group}/paper_size_auto", paper_size_auto)
            self.settings.sync()
            
            return True
            
        except Exception as e:
            print(f"خطأ في حفظ إعدادات الجودة: {str(e)}")
            return False
    
    def is_auto_pdf_enabled(self):
        """التحقق من تفعيل الحفظ التلقائي لـ PDF
        
        Returns:
            bool: True إذا كان مفعلاً، False خلاف ذلك
        """
        try:
            if not self.settings:
                return True  # افتراضياً مفعل
            
            return self.settings.value(
                f"{self.pdf_settings_group}/auto_pdf_enabled", 
                True, 
                type=bool
            )
            
        except Exception as e:
            print(f"خطأ في التحقق من تفعيل PDF التلقائي: {str(e)}")
            return True
    
    def set_auto_pdf_enabled(self, enabled):
        """تعيين تفعيل الحفظ التلقائي لـ PDF
        
        Args:
            enabled (bool): True لتفعيل، False لإلغاء التفعيل
        
        Returns:
            bool: True إذا تم الحفظ بنجاح
        """
        try:
            if not self.settings:
                return False
            
            self.settings.setValue(f"{self.pdf_settings_group}/auto_pdf_enabled", enabled)
            self.settings.sync()
            
            return True
            
        except Exception as e:
            print(f"خطأ في حفظ إعداد PDF التلقائي: {str(e)}")
            return False
    
    def get_filename_format(self):
        """الحصول على تنسيق اسم الملف
        
        Returns:
            str: تنسيق اسم الملف
        """
        try:
            if not self.settings:
                return "invoice_{number}_{date}"
            
            return self.settings.value(
                f"{self.pdf_settings_group}/filename_format", 
                "invoice_{number}_{date}", 
                type=str
            )
            
        except Exception as e:
            print(f"خطأ في الحصول على تنسيق اسم الملف: {str(e)}")
            return "invoice_{number}_{date}"
    
    def set_filename_format(self, format_string):
        """تعيين تنسيق اسم الملف
        
        Args:
            format_string (str): تنسيق اسم الملف (يمكن أن يحتوي على {number}, {date}, {customer})
        
        Returns:
            bool: True إذا تم الحفظ بنجاح
        """
        try:
            if not self.settings:
                return False
            
            # التحقق من صحة التنسيق
            if not format_string or not isinstance(format_string, str):
                format_string = "invoice_{number}_{date}"
            
            self.settings.setValue(f"{self.pdf_settings_group}/filename_format", format_string)
            self.settings.sync()
            
            return True
            
        except Exception as e:
            print(f"خطأ في حفظ تنسيق اسم الملف: {str(e)}")
            return False
    
    def is_include_date_folder_enabled(self):
        """التحقق من تفعيل تنظيم المجلدات حسب التاريخ
        
        Returns:
            bool: True إذا كان مفعلاً
        """
        try:
            if not self.settings:
                return True
            
            return self.settings.value(
                f"{self.pdf_settings_group}/include_date_folder", 
                True, 
                type=bool
            )
            
        except Exception as e:
            print(f"خطأ في التحقق من تنظيم المجلدات: {str(e)}")
            return True
    
    def set_include_date_folder_enabled(self, enabled):
        """تعيين تفعيل تنظيم المجلدات حسب التاريخ
        
        Args:
            enabled (bool): True لتفعيل التنظيم
        
        Returns:
            bool: True إذا تم الحفظ بنجاح
        """
        try:
            if not self.settings:
                return False
            
            self.settings.setValue(f"{self.pdf_settings_group}/include_date_folder", enabled)
            self.settings.sync()
            
            return True
            
        except Exception as e:
            print(f"خطأ في حفظ إعداد تنظيم المجلدات: {str(e)}")
            return False
    
    def is_auto_open_enabled(self):
        """التحقق من تفعيل فتح PDF تلقائياً بعد الحفظ
        
        Returns:
            bool: True إذا كان مفعلاً
        """
        try:
            if not self.settings:
                return False
            
            return self.settings.value(
                f"{self.pdf_settings_group}/auto_open", 
                False, 
                type=bool
            )
            
        except Exception as e:
            print(f"خطأ في التحقق من الفتح التلقائي: {str(e)}")
            return False
    
    def set_auto_open_enabled(self, enabled):
        """تعيين تفعيل فتح PDF تلقائياً بعد الحفظ
        
        Args:
            enabled (bool): True لتفعيل الفتح التلقائي
        
        Returns:
            bool: True إذا تم الحفظ بنجاح
        """
        try:
            if not self.settings:
                return False
            
            self.settings.setValue(f"{self.pdf_settings_group}/auto_open", enabled)
            self.settings.sync()
            
            return True
            
        except Exception as e:
            print(f"خطأ في حفظ إعداد الفتح التلقائي: {str(e)}")
            return False
    
    def get_all_pdf_settings(self):
        """الحصول على جميع إعدادات PDF
        
        Returns:
            dict: جميع الإعدادات
        """
        try:
            return {
                'save_directory': self.get_pdf_save_directory(),
                'auto_pdf_enabled': self.is_auto_pdf_enabled(),
                'quality_settings': self.get_pdf_quality_settings(),
                'filename_format': self.get_filename_format(),
                'include_date_folder': self.is_include_date_folder_enabled(),
                'auto_open': self.is_auto_open_enabled()
            }
            
        except Exception as e:
            print(f"خطأ في الحصول على جميع الإعدادات: {str(e)}")
            return {}
    
    def reset_to_defaults(self):
        """إعادة تعيين جميع الإعدادات للقيم الافتراضية
        
        Returns:
            bool: True إذا تم الإعادة بنجاح
        """
        try:
            if not self.settings:
                return False
            
            # حذف جميع إعدادات PDF
            self.settings.beginGroup(self.pdf_settings_group)
            self.settings.clear()
            self.settings.endGroup()
            
            # إعادة تهيئة الإعدادات الافتراضية
            self._initialize_default_settings()
            
            print("تم إعادة تعيين إعدادات PDF للقيم الافتراضية")
            return True
            
        except Exception as e:
            print(f"خطأ في إعادة تعيين الإعدادات: {str(e)}")
            return False
    
    def export_settings(self, file_path):
        """تصدير الإعدادات إلى ملف
        
        Args:
            file_path (str): مسار الملف للتصدير
        
        Returns:
            bool: True إذا تم التصدير بنجاح
        """
        try:
            import json
            
            settings_data = self.get_all_pdf_settings()
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(settings_data, f, ensure_ascii=False, indent=2)
            
            print(f"تم تصدير الإعدادات إلى: {file_path}")
            return True
            
        except Exception as e:
            print(f"خطأ في تصدير الإعدادات: {str(e)}")
            return False
    
    def import_settings(self, file_path):
        """استيراد الإعدادات من ملف
        
        Args:
            file_path (str): مسار الملف للاستيراد
        
        Returns:
            bool: True إذا تم الاستيراد بنجاح
        """
        try:
            import json
            
            if not os.path.exists(file_path):
                print(f"ملف الإعدادات غير موجود: {file_path}")
                return False
            
            with open(file_path, 'r', encoding='utf-8') as f:
                settings_data = json.load(f)
            
            # تطبيق الإعدادات
            if 'save_directory' in settings_data:
                self.set_pdf_save_directory(settings_data['save_directory'])
            
            if 'auto_pdf_enabled' in settings_data:
                self.set_auto_pdf_enabled(settings_data['auto_pdf_enabled'])
            
            if 'quality_settings' in settings_data:
                quality_settings = settings_data['quality_settings']
                self.set_pdf_quality_settings(
                    quality_settings.get('quality', 'high'),
                    quality_settings.get('compression_level', 6),
                    quality_settings.get('paper_size_auto', True)
                )
            
            if 'filename_format' in settings_data:
                self.set_filename_format(settings_data['filename_format'])
            
            if 'include_date_folder' in settings_data:
                self.set_include_date_folder_enabled(settings_data['include_date_folder'])
            
            if 'auto_open' in settings_data:
                self.set_auto_open_enabled(settings_data['auto_open'])
            
            print(f"تم استيراد الإعدادات من: {file_path}")
            return True
            
        except Exception as e:
            print(f"خطأ في استيراد الإعدادات: {str(e)}")
            return False


class FileManager:
    """فئة إدارة ملفات PDF وأسماء الملفات والمجلدات"""
    
    def __init__(self):
        """تهيئة مدير الملفات"""
        self.config_manager = ConfigManager()
        self.path_manager = PathManager()
    
    def generate_pdf_filename(self, invoice_data):
        """إنشاء اسم ملف PDF منظم
        
        Args:
            invoice_data (dict): بيانات الفاتورة
        
        Returns:
            str: اسم الملف بدون امتداد
        """
        try:
            # استخراج البيانات المطلوبة
            invoice_number = invoice_data.get('invoice_number', '')
            invoice_date = invoice_data.get('invoice_date', '')
            customer_name = invoice_data.get('customer_name', '')
            
            # الحصول على تنسيق اسم الملف من الإعدادات
            filename_format = self.config_manager.get_filename_format()
            
            # إنشاء اسم الملف باستخدام PathManager
            base_filename = self.path_manager.generate_safe_filename(
                invoice_number, 
                invoice_date, 
                customer_name
            )
            
            # تطبيق التنسيق المخصص إذا كان مختلفاً عن الافتراضي
            if filename_format != "invoice_{number}_{date}":
                try:
                    # استبدال المتغيرات في التنسيق
                    custom_filename = filename_format.format(
                        number=invoice_number or 'unknown',
                        date=invoice_date or 'unknown',
                        customer=customer_name or 'unknown'
                    )
                    # تنظيف اسم الملف المخصص
                    custom_filename = self.path_manager.sanitize_filename(custom_filename)
                    if custom_filename:
                        base_filename = custom_filename
                except Exception as e:
                    print(f"خطأ في تطبيق التنسيق المخصص: {str(e)}")
                    # استخدام الاسم الافتراضي
            
            return base_filename
            
        except Exception as e:
            print(f"خطأ في إنشاء اسم ملف PDF: {str(e)}")
            # إنشاء اسم افتراضي بالوقت الحالي
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            return f"invoice_{timestamp}"
    
    def get_pdf_save_path(self, filename):
        """الحصول على المسار الكامل لحفظ PDF
        
        Args:
            filename (str): اسم الملف (بدون امتداد)
        
        Returns:
            str: المسار الكامل للملف مع امتداد .pdf
        """
        try:
            # الحصول على مجلد الحفظ الأساسي
            base_directory = self.config_manager.get_pdf_save_directory()
            
            # إنشاء مسار منظم حسب التاريخ إذا كان مفعلاً
            if self.config_manager.is_include_date_folder_enabled():
                from datetime import datetime
                current_date = datetime.now().strftime("%Y-%m-%d")
                organized_directory = self.path_manager.create_date_based_path(
                    base_directory, 
                    current_date
                )
            else:
                organized_directory = base_directory
            
            # التأكد من وجود المجلد
            if not self.ensure_directory_exists(organized_directory):
                print(f"تحذير: لا يمكن إنشاء المجلد {organized_directory}")
                # استخدام المجلد الأساسي كبديل
                organized_directory = base_directory
                self.ensure_directory_exists(organized_directory)
            
            # إنشاء المسار الكامل
            full_path = os.path.join(organized_directory, f"{filename}.pdf")
            
            # معالجة الملفات المكررة
            final_path = self.handle_duplicate_filename(full_path)
            
            return final_path
            
        except Exception as e:
            print(f"خطأ في الحصول على مسار حفظ PDF: {str(e)}")
            # إنشاء مسار افتراضي آمن
            safe_filename = self.path_manager.sanitize_filename(filename)
            return os.path.join(os.getcwd(), f"{safe_filename}.pdf")
    
    def ensure_directory_exists(self, directory_path):
        """التأكد من وجود المجلد وإنشاؤه إذا لم يكن موجوداً
        
        Args:
            directory_path (str): مسار المجلد
        
        Returns:
            bool: True إذا تم إنشاء المجلد بنجاح أو كان موجوداً، False خلاف ذلك
        """
        try:
            return self.path_manager.ensure_path_exists(directory_path)
        except Exception as e:
            print(f"خطأ في إنشاء المجلد {directory_path}: {str(e)}")
            return False
    
    def handle_duplicate_filename(self, file_path):
        """معالجة أسماء الملفات المكررة
        
        Args:
            file_path (str): المسار الكامل للملف
        
        Returns:
            str: مسار الملف الفريد
        """
        try:
            if not os.path.exists(file_path):
                return file_path
            
            # فصل المسار واسم الملف والامتداد
            directory = os.path.dirname(file_path)
            filename_with_ext = os.path.basename(file_path)
            filename, extension = os.path.splitext(filename_with_ext)
            
            # البحث عن رقم متاح
            counter = 1
            while True:
                new_filename = f"{filename}_{counter:03d}{extension}"
                new_path = os.path.join(directory, new_filename)
                
                if not os.path.exists(new_path):
                    return new_path
                
                counter += 1
                
                # حماية من الحلقة اللانهائية
                if counter > 999:
                    # إضافة طابع زمني كحل أخير
                    from datetime import datetime
                    timestamp = datetime.now().strftime("%H%M%S")
                    new_filename = f"{filename}_{timestamp}{extension}"
                    return os.path.join(directory, new_filename)
            
        except Exception as e:
            print(f"خطأ في معالجة الملف المكرر: {str(e)}")
            # إضافة طابع زمني كحل بديل
            try:
                directory = os.path.dirname(file_path)
                filename_with_ext = os.path.basename(file_path)
                filename, extension = os.path.splitext(filename_with_ext)
                
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                new_filename = f"{filename}_{timestamp}{extension}"
                return os.path.join(directory, new_filename)
            except:
                return file_path
    
    def validate_file_path(self, file_path):
        """التحقق من صحة مسار الملف
        
        Args:
            file_path (str): مسار الملف للتحقق منه
        
        Returns:
            dict: نتيجة التحقق مع معلومات إضافية
        """
        try:
            # التحقق من المجلد الأب
            directory = os.path.dirname(file_path)
            directory_validation = self.path_manager.validate_path(directory)
            
            result = {
                'valid': False,
                'directory_exists': directory_validation['exists'],
                'directory_writable': directory_validation['writable'],
                'file_exists': False,
                'can_create': False,
                'error_message': ''
            }
            
            # التحقق من وجود الملف
            result['file_exists'] = os.path.exists(file_path)
            
            # التحقق من إمكانية الإنشاء
            result['can_create'] = directory_validation['valid']
            
            # الملف صحيح إذا كان يمكن إنشاؤه
            result['valid'] = result['can_create']
            
            if not result['valid']:
                result['error_message'] = directory_validation['error_message']
            
            return result
            
        except Exception as e:
            return {
                'valid': False,
                'directory_exists': False,
                'directory_writable': False,
                'file_exists': False,
                'can_create': False,
                'error_message': f"خطأ في التحقق من مسار الملف: {str(e)}"
            }
    
    def get_file_info(self, file_path):
        """الحصول على معلومات الملف
        
        Args:
            file_path (str): مسار الملف
        
        Returns:
            dict: معلومات الملف
        """
        try:
            info = {
                'exists': False,
                'size': 0,
                'created': None,
                'modified': None,
                'readable': False,
                'writable': False
            }
            
            if os.path.exists(file_path):
                info['exists'] = True
                
                # حجم الملف
                info['size'] = os.path.getsize(file_path)
                
                # أوقات الإنشاء والتعديل
                stat = os.stat(file_path)
                info['created'] = stat.st_ctime
                info['modified'] = stat.st_mtime
                
                # صلاحيات القراءة والكتابة
                info['readable'] = os.access(file_path, os.R_OK)
                info['writable'] = os.access(file_path, os.W_OK)
            
            return info
            
        except Exception as e:
            print(f"خطأ في الحصول على معلومات الملف: {str(e)}")
            return {
                'exists': False,
                'size': 0,
                'created': None,
                'modified': None,
                'readable': False,
                'writable': False,
                'error': str(e)
            }
    
    def cleanup_old_files(self, days_old=30):
        """تنظيف الملفات القديمة
        
        Args:
            days_old (int): عدد الأيام لاعتبار الملف قديماً
        
        Returns:
            dict: نتائج التنظيف
        """
        try:
            from datetime import datetime, timedelta
            
            result = {
                'files_found': 0,
                'files_deleted': 0,
                'space_freed': 0,
                'errors': []
            }
            
            # الحصول على مجلد PDF
            pdf_directory = self.config_manager.get_pdf_save_directory()
            
            if not os.path.exists(pdf_directory):
                return result
            
            # حساب التاريخ الحد
            cutoff_date = datetime.now() - timedelta(days=days_old)
            cutoff_timestamp = cutoff_date.timestamp()
            
            # البحث في جميع الملفات
            for root, dirs, files in os.walk(pdf_directory):
                for file in files:
                    if file.lower().endswith('.pdf'):
                        file_path = os.path.join(root, file)
                        
                        try:
                            result['files_found'] += 1
                            
                            # التحقق من تاريخ الملف
                            file_stat = os.stat(file_path)
                            if file_stat.st_mtime < cutoff_timestamp:
                                # الملف قديم، حذفه
                                file_size = file_stat.st_size
                                os.remove(file_path)
                                
                                result['files_deleted'] += 1
                                result['space_freed'] += file_size
                                
                                print(f"تم حذف الملف القديم: {file_path}")
                        
                        except Exception as e:
                            error_msg = f"خطأ في معالجة الملف {file_path}: {str(e)}"
                            result['errors'].append(error_msg)
                            print(error_msg)
            
            return result
            
        except Exception as e:
            print(f"خطأ في تنظيف الملفات القديمة: {str(e)}")
            return {
                'files_found': 0,
                'files_deleted': 0,
                'space_freed': 0,
                'errors': [str(e)]
            }
    
    def get_directory_size(self, directory_path=None):
        """حساب حجم مجلد PDF
        
        Args:
            directory_path (str, optional): مسار المجلد. إذا لم يحدد، يستخدم مجلد PDF الافتراضي
        
        Returns:
            dict: معلومات حجم المجلد
        """
        try:
            if directory_path is None:
                directory_path = self.config_manager.get_pdf_save_directory()
            
            result = {
                'total_size': 0,
                'file_count': 0,
                'pdf_count': 0,
                'readable': True
            }
            
            if not os.path.exists(directory_path):
                return result
            
            # حساب الحجم الإجمالي
            for root, dirs, files in os.walk(directory_path):
                for file in files:
                    try:
                        file_path = os.path.join(root, file)
                        file_size = os.path.getsize(file_path)
                        
                        result['total_size'] += file_size
                        result['file_count'] += 1
                        
                        if file.lower().endswith('.pdf'):
                            result['pdf_count'] += 1
                    
                    except Exception as e:
                        print(f"خطأ في قراءة ملف {file}: {str(e)}")
                        result['readable'] = False
            
            return result
            
        except Exception as e:
            print(f"خطأ في حساب حجم المجلد: {str(e)}")
            return {
                'total_size': 0,
                'file_count': 0,
                'pdf_count': 0,
                'readable': False,
                'error': str(e)
            }
    
    def create_backup_filename(self, original_path):
        """إنشاء اسم ملف نسخة احتياطية
        
        Args:
            original_path (str): مسار الملف الأصلي
        
        Returns:
            str: مسار ملف النسخة الاحتياطية
        """
        try:
            directory = os.path.dirname(original_path)
            filename_with_ext = os.path.basename(original_path)
            filename, extension = os.path.splitext(filename_with_ext)
            
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            backup_filename = f"{filename}_backup_{timestamp}{extension}"
            backup_path = os.path.join(directory, backup_filename)
            
            return backup_path
            
        except Exception as e:
            print(f"خطأ في إنشاء اسم ملف النسخة الاحتياطية: {str(e)}")
            return f"{original_path}.backup"


class LayoutRenderer:
    """فئة رسم تخطيط الفاتورة (مشتركة بين الطباعة والـ PDF)"""
    
    def __init__(self, paper_width_mm):
        """تهيئة مُرسم التخطيط
        
        Args:
            paper_width_mm (int): عرض الورق بالمليمتر
        """
        self.paper_width = paper_width_mm
        self.spacing_calc = SpacingCalculator(paper_width_mm)
        self.font_manager = FontManager(paper_width_mm)
        self.layout_manager = AdaptiveLayoutManager(paper_width_mm)
        
        # إعدادات الألوان والأقلام
        self.text_color = Qt.GlobalColor.black
        self.line_color = Qt.GlobalColor.black
        self.background_color = Qt.GlobalColor.white
    
    def render_invoice(self, painter, page_rect, invoice_data, items_data, company_info):
        """رسم الفاتورة الكاملة
        
        Args:
            painter (QPainter): كائن الرسم
            page_rect (QRect): مستطيل الصفحة
            invoice_data (dict): بيانات الفاتورة
            items_data (list): بيانات العناصر
            company_info (dict): معلومات الشركة
        
        Returns:
            bool: True إذا تم الرسم بنجاح، False خلاف ذلك
        """
        try:
            # إعداد الرسم
            painter.setRenderHint(QPainter.Antialiasing, True)
            painter.setRenderHint(QPainter.TextAntialiasing, True)
            
            # بدء الرسم من الأعلى
            current_y = page_rect.top() + self.spacing_calc.get_section_spacing()
            
            # رسم رأس الشركة
            current_y = self.render_company_header(painter, company_info, current_y, page_rect.width())
            
            # إضافة مسافة بين الأقسام
            current_y += self.spacing_calc.get_section_spacing()
            
            # رسم معلومات الفاتورة
            current_y = self.render_invoice_info(painter, invoice_data, current_y, page_rect.width())
            
            # إضافة مسافة قبل الجدول
            current_y += self.spacing_calc.get_section_spacing()
            
            # رسم جدول العناصر
            current_y = self.render_items_table(painter, items_data, current_y, page_rect.width())
            
            # إضافة مسافة قبل المجاميع
            current_y += self.spacing_calc.get_section_spacing()
            
            # رسم المجاميع
            current_y = self.render_totals(painter, invoice_data, current_y, page_rect.width())
            
            return True
            
        except Exception as e:
            print(f"خطأ في رسم الفاتورة: {str(e)}")
            return False
    
    def render_company_header(self, painter, company_info, y_position, page_width):
        """رسم رأس الشركة
        
        Args:
            painter (QPainter): كائن الرسم
            company_info (dict): معلومات الشركة
            y_position (int): الموضع العمودي
            page_width (int): عرض الصفحة
        
        Returns:
            int: الموضع العمودي الجديد
        """
        try:
            # الحصول على الخطوط
            company_font = self.font_manager.get_company_font()
            info_font = self.font_manager.get_info_font()
            
            # إعداد القلم
            pen = QPen(self.text_color, 1)
            painter.setPen(pen)
            
            current_y = y_position
            
            # رسم اسم الشركة
            company_name = company_info.get('name', company_info.get('company_name', ''))
            if company_name and company_name.strip():
                painter.setFont(company_font)
                
                # حساب موضع النص (وسط الصفحة)
                font_metrics = QFontMetrics(company_font)
                text_width = font_metrics.horizontalAdvance(company_name)
                x_position = (page_width - text_width) // 2
                
                # رسم النص
                painter.drawText(x_position, current_y, company_name)
                current_y += font_metrics.height() + self.spacing_calc.get_line_spacing('general')
            
            # رسم عنوان الشركة
            company_address = company_info.get('address', company_info.get('company_address', ''))
            if company_address and company_address.strip():
                painter.setFont(info_font)
                
                # تقسيم العنوان إذا كان طويلاً
                wrapped_lines = self.layout_manager.wrap_text(company_address, info_font, page_width - 40)
                
                for line in wrapped_lines:
                    font_metrics = QFontMetrics(info_font)
                    text_width = font_metrics.horizontalAdvance(line)
                    x_position = (page_width - text_width) // 2
                    
                    painter.drawText(x_position, current_y, line)
                    current_y += font_metrics.height() + self.spacing_calc.get_line_spacing('general')
            
            # رسم هاتف الشركة
            company_phone = company_info.get('phone', company_info.get('company_phone', ''))
            if company_phone and company_phone.strip():
                painter.setFont(info_font)
                
                phone_text = f"هاتف: {company_phone}"
                font_metrics = QFontMetrics(info_font)
                text_width = font_metrics.horizontalAdvance(phone_text)
                x_position = (page_width - text_width) // 2
                
                painter.drawText(x_position, current_y, phone_text)
                current_y += font_metrics.height() + self.spacing_calc.get_line_spacing('general')
            
            # رسم خط فاصل
            current_y += self.spacing_calc.get_line_spacing('general')
            line_y = current_y
            painter.drawLine(20, line_y, page_width - 20, line_y)
            current_y += self.spacing_calc.get_line_spacing('general') * 2  # ضاعف المسافة قبل معلومات الفاتورة
            
            return current_y
            
        except Exception as e:
            print(f"خطأ في رسم رأس الشركة: {str(e)}")
            return y_position + 100  # إرجاع موضع افتراضي
    
    def render_invoice_info(self, painter, invoice_data, y_position, page_width):
        """رسم معلومات الفاتورة
        
        Args:
            painter (QPainter): كائن الرسم
            invoice_data (dict): بيانات الفاتورة
            y_position (int): الموضع العمودي
            page_width (int): عرض الصفحة
        
        Returns:
            int: الموضع العمودي الجديد
        """
        try:
            # الحصول على الخط
            info_font = self.font_manager.get_info_font()
            painter.setFont(info_font)
            
            # إعداد القلم
            pen = QPen(self.text_color, 1)
            painter.setPen(pen)
            
            current_y = y_position
            font_metrics = QFontMetrics(info_font)
            line_height = font_metrics.height() + self.spacing_calc.get_line_spacing('general')
            
            # معلومات الفاتورة
            invoice_number = invoice_data.get('reference_number', invoice_data.get('invoice_number', ''))
            invoice_date = invoice_data.get('date', invoice_data.get('invoice_date', ''))
            customer_name = invoice_data.get('customer_name', '')
            
            invoice_info_items = [
                ('رقم الفاتورة:', invoice_number),
                ('التاريخ:', invoice_date),
                ('العميل:', customer_name),
            ]
            
            # رسم المعلومات في عمودين
            left_margin = 20
            right_margin = page_width - 20
            
            for label, value in invoice_info_items:
                if value:  # رسم فقط إذا كانت القيمة موجودة
                    # رسم التسمية على اليسار
                    painter.drawText(left_margin, current_y, label)
                    
                    # رسم القيمة على اليمين
                    value_width = font_metrics.horizontalAdvance(str(value))
                    value_x = right_margin - value_width
                    painter.drawText(value_x, current_y, str(value))
                    
                    current_y += line_height
            
            # إضافة مسافة إضافية كبيرة
            current_y += self.spacing_calc.get_line_spacing('general') * 3  # ثلاثة أضعاف المسافة قبل الجدول
            
            return current_y
            
        except Exception as e:
            print(f"خطأ في رسم معلومات الفاتورة: {str(e)}")
            return y_position + 80  # إرجاع موضع افتراضي
    
    def render_items_table(self, painter, items_data, y_position, page_width):
        """رسم جدول العناصر
        
        Args:
            painter (QPainter): كائن الرسم
            items_data (list): بيانات العناصر
            y_position (int): الموضع العمودي
            page_width (int): عرض الصفحة
        
        Returns:
            int: الموضع العمودي الجديد
        """
        try:
            if not items_data:
                return y_position
            
            # الحصول على الخطوط
            header_font = self.font_manager.get_table_header_font()
            data_font = self.font_manager.get_table_data_font()
            
            # إعداد القلم
            pen = QPen(self.text_color, 1)
            painter.setPen(pen)
            
            current_y = y_position
            
            # حساب عرض الأعمدة
            table_width = page_width - 40  # هوامش 20 من كل جانب
            col_widths = {
                'name': int(table_width * 0.4),      # 40% لاسم المنتج
                'quantity': int(table_width * 0.15), # 15% للكمية
                'price': int(table_width * 0.2),     # 20% للسعر
                'total': int(table_width * 0.25)     # 25% للمجموع
            }
            
            # مواضع الأعمدة
            col_positions = {
                'name': 20,
                'quantity': 20 + col_widths['name'],
                'price': 20 + col_widths['name'] + col_widths['quantity'],
                'total': 20 + col_widths['name'] + col_widths['quantity'] + col_widths['price']
            }
            
            # رسم رأس الجدول
            painter.setFont(header_font)
            header_metrics = QFontMetrics(header_font)
            header_height = header_metrics.height() + (self.spacing_calc.get_line_spacing('header') * 3)  # زيادة ارتفاع رأس الجدول
            
            # رسم خلفية رأس الجدول
            header_rect = QRect(20, current_y - header_metrics.ascent(), table_width, header_height)
            painter.fillRect(header_rect, QBrush(Qt.GlobalColor.lightGray))
            
            # رسم نصوص رأس الجدول
            headers = ['المنتج', 'الكمية', 'السعر', 'المجموع']
            header_positions = ['name', 'quantity', 'price', 'total']
            
            for header, pos_key in zip(headers, header_positions):
                x_pos = col_positions[pos_key] + 5  # هامش داخلي
                painter.drawText(x_pos, current_y, header)
            
            current_y += header_height
            
            # رسم خط تحت الرأس
            painter.drawLine(20, current_y, 20 + table_width, current_y)
            current_y += self.spacing_calc.get_line_spacing('header')
            
            # رسم بيانات العناصر
            painter.setFont(data_font)
            data_metrics = QFontMetrics(data_font)
            row_height = data_metrics.height() + (self.spacing_calc.get_line_spacing('data') * 1.5)  # زيادة ارتفاع صفوف الجدول
            
            for item in items_data:
                # استخراج بيانات العنصر
                item_name = str(item.get('name', ''))
                item_quantity = str(item.get('quantity', ''))
                item_price = f"{item.get('price', 0):.2f}"
                item_total = f"{item.get('total', 0):.2f}"
                
                # تقسيم اسم المنتج إذا كان طويلاً
                wrapped_name_lines = self.layout_manager.wrap_text(
                    item_name, 
                    data_font, 
                    col_widths['name'] - 10
                )
                
                # حساب ارتفاع الصف (بناءً على عدد أسطر اسم المنتج)
                actual_row_height = len(wrapped_name_lines) * row_height
                
                # رسم اسم المنتج (متعدد الأسطر)
                name_y = current_y
                for line in wrapped_name_lines:
                    painter.drawText(col_positions['name'] + 5, name_y, line)
                    name_y += row_height
                
                # رسم باقي البيانات (في وسط الصف)
                center_y = current_y + (actual_row_height // 2)
                
                painter.drawText(col_positions['quantity'] + 5, center_y, item_quantity)
                painter.drawText(col_positions['price'] + 5, center_y, item_price)
                painter.drawText(col_positions['total'] + 5, center_y, item_total)
                
                current_y += actual_row_height
                
                # رسم خط فاصل بين الصفوف
                painter.drawLine(20, current_y, 20 + table_width, current_y)
                current_y += self.spacing_calc.get_line_spacing('data')
            
            return current_y
            
        except Exception as e:
            print(f"خطأ في رسم جدول العناصر: {str(e)}")
            return y_position + 200  # إرجاع موضع افتراضي
    
    def render_totals(self, painter, invoice_data, y_position, page_width):
        """رسم المجاميع
        
        Args:
            painter (QPainter): كائن الرسم
            invoice_data (dict): بيانات الفاتورة
            y_position (int): الموضع العمودي
            page_width (int): عرض الصفحة
        
        Returns:
            int: الموضع العمودي الجديد
        """
        try:
            # الحصول على الخطوط
            total_font = self.font_manager.get_total_font()
            info_font = self.font_manager.get_info_font()
            
            # إعداد القلم
            pen = QPen(self.text_color, 1)
            painter.setPen(pen)
            
            current_y = y_position
            
            # حساب عرض صندوق المجاميع
            box_width = min(300, page_width - 40)
            box_x = page_width - box_width - 20  # محاذاة لليمين
            
            # المجاميع المختلفة
            totals_info = []
            
            # المجموع الفرعي
            subtotal = invoice_data.get('subtotal', 0)
            if subtotal > 0:
                totals_info.append(('المجموع الفرعي:', f"{subtotal:.2f}"))
            
            # الضريبة
            tax = invoice_data.get('tax', 0)
            if tax > 0:
                totals_info.append(('الضريبة:', f"{tax:.2f}"))
            
            # الخصم
            discount = invoice_data.get('discount', 0)
            if discount > 0:
                totals_info.append(('الخصم:', f"-{discount:.2f}"))
            
            # المجموع الكلي
            total = invoice_data.get('total', 0)
            totals_info.append(('المجموع الكلي:', f"{total:.2f}"))
            
            # رسم المجاميع
            painter.setFont(info_font)
            info_metrics = QFontMetrics(info_font)
            line_height = info_metrics.height() + self.spacing_calc.get_line_spacing('general')

            # إضافة مسافة كبيرة قبل قسم المجاميع
            current_y += self.spacing_calc.get_line_spacing('general') * 3
            
            for i, (label, value) in enumerate(totals_info):
                # استخدام خط أكبر للمجموع الكلي
                if i == len(totals_info) - 1:  # المجموع الكلي
                    painter.setFont(total_font)
                    font_metrics = QFontMetrics(total_font)
                    
                    # رسم صندوق للمجموع الكلي
                    box_height = font_metrics.height() + (self.spacing_calc.get_line_spacing('general') * 2)
                    total_box = QRect(box_x, current_y - font_metrics.ascent() - self.spacing_calc.get_line_spacing('general'), 
                                    box_width, box_height)
                    painter.fillRect(total_box, QBrush(Qt.GlobalColor.lightGray))
                    painter.drawRect(total_box)
                else:
                    painter.setFont(info_font)
                    font_metrics = QFontMetrics(info_font)
                
                # رسم التسمية
                painter.drawText(box_x + 10, current_y, label)
                
                # رسم القيمة (محاذاة لليمين)
                value_width = font_metrics.horizontalAdvance(value)
                value_x = box_x + box_width - value_width - 10
                painter.drawText(value_x, current_y, value)
                
                current_y += line_height
                
                # خط فاصل قبل المجموع الكلي
                if i == len(totals_info) - 2:  # قبل المجموع الكلي مباشرة
                    current_y += self.spacing_calc.get_line_spacing('general')
                    painter.drawLine(box_x + 10, current_y - self.spacing_calc.get_line_spacing('general') // 2, 
                                   box_x + box_width - 10, current_y - self.spacing_calc.get_line_spacing('general') // 2)
            
            return current_y
            
        except Exception as e:
            print(f"خطأ في رسم المجاميع: {str(e)}")
            return y_position + 100  # إرجاع موضع افتراضي
    
    def render_footer(self, painter, footer_text, y_position, page_width):
        """رسم تذييل الفاتورة
        
        Args:
            painter (QPainter): كائن الرسم
            footer_text (str): نص التذييل
            y_position (int): الموضع العمودي
            page_width (int): عرض الصفحة
        
        Returns:
            int: الموضع العمودي الجديد
        """
        try:
            if not footer_text:
                return y_position
            
            # الحصول على الخط
            notes_font = self.font_manager.get_notes_font()
            painter.setFont(notes_font)
            
            # إعداد القلم
            pen = QPen(self.text_color, 1)
            painter.setPen(pen)
            
            current_y = y_position + self.spacing_calc.get_section_spacing()
            
            # تقسيم النص إذا كان طويلاً
            wrapped_lines = self.layout_manager.wrap_text(footer_text, notes_font, page_width - 40)
            
            for line in wrapped_lines:
                font_metrics = QFontMetrics(notes_font)
                text_width = font_metrics.horizontalAdvance(line)
                x_position = (page_width - text_width) // 2  # توسيط النص
                
                painter.drawText(x_position, current_y, line)
                current_y += font_metrics.height() + self.spacing_calc.get_line_spacing('general')
            
            return current_y
            
        except Exception as e:
            print(f"خطأ في رسم التذييل: {str(e)}")
            return y_position + 50


class PDFGenerator:
    """فئة إنشاء ملفات PDF من بيانات الفاتورة"""
    
    def __init__(self, paper_width_mm=80):
        """تهيئة مولد PDF
        
        Args:
            paper_width_mm (int): عرض الورق بالمليمتر
        """
        self.paper_width = paper_width_mm
        self.layout_renderer = LayoutRenderer(paper_width_mm)
        self.config_manager = ConfigManager()
        
        # إعدادات PDF الافتراضية
        self.default_resolution = 300  # DPI
        self.default_quality = "high"
    
    def generate_pdf(self, invoice_data, items_data, company_info, output_path):
        """إنشاء ملف PDF من بيانات الفاتورة
        
        Args:
            invoice_data (dict): بيانات الفاتورة
            items_data (list): بيانات العناصر
            company_info (dict): معلومات الشركة
            output_path (str): مسار الملف المراد إنشاؤه
        
        Returns:
            bool: True إذا تم إنشاء PDF بنجاح، False خلاف ذلك
        """
        try:
            print(f"بدء إنشاء PDF: {output_path}")
            
            # إعداد مستند PDF
            printer = self._setup_pdf_document(output_path)
            if not printer:
                return False
            
            # بدء الرسم
            painter = QPainter()
            if not painter.begin(printer):
                print("خطأ: لا يمكن بدء الرسم في PDF")
                return False
            
            try:
                # رسم محتوى الفاتورة
                success = self._render_invoice_content(
                    painter, 
                    invoice_data, 
                    items_data, 
                    company_info
                )
                
                if success:
                    print(f"تم إنشاء PDF بنجاح: {output_path}")
                else:
                    print("خطأ في رسم محتوى الفاتورة")
                
                return success
                
            finally:
                # إنهاء الرسم
                painter.end()
            
        except Exception as e:
            print(f"خطأ في إنشاء PDF: {str(e)}")
            return False
    
    def _setup_pdf_document(self, output_path):
        """إعداد مستند PDF
        
        Args:
            output_path (str): مسار الملف المراد إنشاؤه
        
        Returns:
            QPrinter: كائن الطابعة المُعد للـ PDF أو None في حالة الفشل
        """
        try:
            # إنشاء طابعة PDF
            printer = QPrinter(QPrinter.HighResolution)
            printer.setOutputFormat(QPrinter.PdfFormat)
            printer.setOutputFileName(output_path)
            
            # الحصول على إعدادات الجودة
            quality_settings = self.config_manager.get_pdf_quality_settings()
            
            # تعيين دقة الوضوح
            resolution = quality_settings.get('resolution', self.default_resolution)
            printer.setResolution(resolution)
            
            # تعيين حجم الورق
            if quality_settings.get('paper_size_auto', True):
                self._set_paper_size_auto(printer)
            else:
                self._set_paper_size_manual(printer)
            
            # تعيين الهوامش
            self._set_margins(printer)
            
            # تعيين اتجاه الصفحة
            printer.setOrientation(QPrinter.Portrait)
            
            print(f"تم إعداد PDF بدقة {resolution} DPI وحجم ورق {self.paper_width}مم")
            
            return printer
            
        except Exception as e:
            print(f"خطأ في إعداد مستند PDF: {str(e)}")
            return None
    
    def _set_paper_size_auto(self, printer):
        """تعيين حجم الورق تلقائياً حسب العرض المحدد
        
        Args:
            printer (QPrinter): كائن الطابعة
        """
        try:
            # تعيين حجم الورق بناءً على العرض
            if self.paper_width <= 58:
                # ورق حراري 58مم
                printer.setPageSize(QPrinter.Custom)
                printer.setPaperSize(QSizeF(58, 200), QPrinter.Millimeter)
                print("تم تعيين حجم الورق: 58مم × 200مم")
                
            elif self.paper_width <= 80:
                # ورق حراري 80مم
                printer.setPageSize(QPrinter.Custom)
                printer.setPaperSize(QSizeF(80, 250), QPrinter.Millimeter)
                print("تم تعيين حجم الورق: 80مم × 250مم")
                
            else:
                # ورق A4 أو أكبر
                printer.setPageSize(QPrinter.A4)
                print("تم تعيين حجم الورق: A4")
            
        except Exception as e:
            print(f"خطأ في تعيين حجم الورق التلقائي: {str(e)}")
            # استخدام A4 كافتراضي
            printer.setPageSize(QPrinter.A4)
    
    def _set_paper_size_manual(self, printer):
        """تعيين حجم الورق يدوياً
        
        Args:
            printer (QPrinter): كائن الطابعة
        """
        try:
            # تعيين حجم مخصص بناءً على العرض المحدد
            printer.setPageSize(QPrinter.Custom)
            
            # حساب الارتفاع بناءً على العرض (نسبة تقريبية)
            if self.paper_width <= 80:
                height = max(200, self.paper_width * 3)  # نسبة 1:3 تقريباً
            else:
                height = max(250, int(self.paper_width * 1.4))  # نسبة A4 تقريباً
            
            printer.setPaperSize(QSizeF(self.paper_width, height), QPrinter.Millimeter)
            print(f"تم تعيين حجم الورق يدوياً: {self.paper_width}مم × {height}مم")
            
        except Exception as e:
            print(f"خطأ في تعيين حجم الورق اليدوي: {str(e)}")
            # استخدام الحجم التلقائي كبديل
            self._set_paper_size_auto(printer)
    
    def _set_margins(self, printer):
        """تعيين هوامش الصفحة
        
        Args:
            printer (QPrinter): كائن الطابعة
        """
        try:
            # حساب الهوامش بناءً على حجم الورق
            if self.paper_width <= 58:
                # هوامش صغيرة للورق الصغير
                margin = 2
            elif self.paper_width <= 80:
                # هوامش متوسطة
                margin = 3
            else:
                # هوامش عادية للورق الكبير
                margin = 5
            
            printer.setPageMargins(margin, margin, margin, margin, QPrinter.Millimeter)
            print(f"تم تعيين الهوامش: {margin}مم")
            
        except Exception as e:
            print(f"خطأ في تعيين الهوامش: {str(e)}")
            # استخدام هوامش افتراضية
            printer.setPageMargins(5, 5, 5, 5, QPrinter.Millimeter)
    
    def _render_invoice_content(self, painter, invoice_data, items_data, company_info):
        """رسم محتوى الفاتورة في PDF
        
        Args:
            painter (QPainter): كائن الرسم
            invoice_data (dict): بيانات الفاتورة
            items_data (list): بيانات العناصر
            company_info (dict): معلومات الشركة
        
        Returns:
            bool: True إذا تم الرسم بنجاح، False خلاف ذلك
        """
        try:
            # الحصول على أبعاد الصفحة
            page_rect = painter.viewport()
            
            print(f"أبعاد الصفحة: {page_rect.width()} × {page_rect.height()}")
            
            # استخدام LayoutRenderer لرسم الفاتورة
            success = self.layout_renderer.render_invoice(
                painter, 
                page_rect, 
                invoice_data, 
                items_data, 
                company_info
            )
            
            if success:
                print("تم رسم محتوى الفاتورة بنجاح")
            else:
                print("فشل في رسم محتوى الفاتورة")
            
            return success
            
        except Exception as e:
            print(f"خطأ في رسم محتوى الفاتورة: {str(e)}")
            return False
    
    def generate_pdf_with_validation(self, invoice_data, items_data, company_info, output_path):
        """إنشاء PDF مع التحقق من صحة البيانات
        
        Args:
            invoice_data (dict): بيانات الفاتورة
            items_data (list): بيانات العناصر
            company_info (dict): معلومات الشركة
            output_path (str): مسار الملف المراد إنشاؤه
        
        Returns:
            dict: نتيجة العملية مع تفاصيل
        """
        result = {
            'success': False,
            'file_path': output_path,
            'error_message': '',
            'file_size': 0,
            'validation_errors': []
        }
        
        try:
            # التحقق من صحة البيانات
            validation_errors = self._validate_invoice_data(invoice_data, items_data, company_info)
            result['validation_errors'] = validation_errors
            
            if validation_errors:
                result['error_message'] = f"أخطاء في البيانات: {', '.join(validation_errors)}"
                print(f"تحذير: {result['error_message']}")
                # يمكن المتابعة مع تحذيرات
            
            # التحقق من صحة مسار الملف
            file_manager = FileManager()
            path_validation = file_manager.validate_file_path(output_path)
            
            if not path_validation['valid']:
                result['error_message'] = f"مسار الملف غير صحيح: {path_validation['error_message']}"
                return result
            
            # إنشاء PDF
            success = self.generate_pdf(invoice_data, items_data, company_info, output_path)
            result['success'] = success
            
            if success:
                # الحصول على حجم الملف
                if os.path.exists(output_path):
                    result['file_size'] = os.path.getsize(output_path)
                    print(f"حجم ملف PDF: {result['file_size']} بايت")
            else:
                result['error_message'] = "فشل في إنشاء ملف PDF"
            
            return result
            
        except Exception as e:
            result['error_message'] = f"خطأ غير متوقع: {str(e)}"
            print(f"خطأ في إنشاء PDF مع التحقق: {str(e)}")
            return result
    
    def _validate_invoice_data(self, invoice_data, items_data, company_info):
        """التحقق من صحة بيانات الفاتورة
        
        Args:
            invoice_data (dict): بيانات الفاتورة
            items_data (list): بيانات العناصر
            company_info (dict): معلومات الشركة
        
        Returns:
            list: قائمة بأخطاء التحقق
        """
        errors = []
        
        try:
            # التحقق من بيانات الشركة
            company_name = company_info.get('name', company_info.get('company_name', ''))
            if not company_name or not company_name.strip():
                errors.append("اسم الشركة مفقود - يرجى إدخاله في الإعدادات")
            
            # التحقق من بيانات الفاتورة الأساسية
            if not invoice_data.get('invoice_number'):
                errors.append("رقم الفاتورة مفقود")
            
            if not invoice_data.get('total'):
                errors.append("مجموع الفاتورة مفقود")
            
            # التحقق من بيانات العناصر
            if not items_data or len(items_data) == 0:
                errors.append("لا توجد عناصر في الفاتورة")
            else:
                for i, item in enumerate(items_data):
                    if not item.get('name'):
                        errors.append(f"اسم المنتج مفقود في العنصر {i+1}")
                    
                    if not isinstance(item.get('quantity'), (int, float)) or item.get('quantity', 0) <= 0:
                        errors.append(f"كمية غير صحيحة في العنصر {i+1}")
                    
                    if not isinstance(item.get('price'), (int, float)) or item.get('price', 0) < 0:
                        errors.append(f"سعر غير صحيح في العنصر {i+1}")
            
            return errors
            
        except Exception as e:
            return [f"خطأ في التحقق من البيانات: {str(e)}"]
    
    def create_test_pdf(self, output_path):
        """إنشاء PDF تجريبي للاختبار
        
        Args:
            output_path (str): مسار الملف المراد إنشاؤه
        
        Returns:
            bool: True إذا تم إنشاء PDF بنجاح
        """
        try:
            # بيانات تجريبية
            test_company_info = {
                'company_name': 'شركة الاختبار المحدودة',
                'company_address': 'عنوان الشركة التجريبي',
                'company_phone': '*********'
            }
            
            test_invoice_data = {
                'invoice_number': 'TEST-001',
                'invoice_date': '2024-12-01',
                'customer_name': 'عميل تجريبي',
                'subtotal': 100.00,
                'tax': 15.00,
                'total': 115.00
            }
            
            test_items_data = [
                {
                    'name': 'منتج تجريبي 1',
                    'quantity': 2,
                    'price': 25.00,
                    'total': 50.00
                },
                {
                    'name': 'منتج تجريبي 2',
                    'quantity': 1,
                    'price': 50.00,
                    'total': 50.00
                }
            ]
            
            # إنشاء PDF
            return self.generate_pdf(
                test_invoice_data,
                test_items_data,
                test_company_info,
                output_path
            )
            
        except Exception as e:
            print(f"خطأ في إنشاء PDF تجريبي: {str(e)}")
            return False
    
    def get_pdf_info(self, file_path):
        """الحصول على معلومات ملف PDF
        
        Args:
            file_path (str): مسار ملف PDF
        
        Returns:
            dict: معلومات الملف
        """
        info = {
            'exists': False,
            'size': 0,
            'created': None,
            'readable': False,
            'is_pdf': False
        }
        
        try:
            if os.path.exists(file_path):
                info['exists'] = True
                info['size'] = os.path.getsize(file_path)
                info['readable'] = os.access(file_path, os.R_OK)
                
                # التحقق من امتداد الملف
                info['is_pdf'] = file_path.lower().endswith('.pdf')
                
                # معلومات الوقت
                stat = os.stat(file_path)
                info['created'] = stat.st_ctime
            
            return info
            
        except Exception as e:
            print(f"خطأ في الحصول على معلومات PDF: {str(e)}")
            return info


class NotificationManager:
    """فئة إدارة الإشعارات والرسائل للمستخدم"""
    
    def __init__(self):
        """تهيئة مدير الإشعارات"""
        self.config_manager = ConfigManager()
        
        # إعدادات الإشعارات الافتراضية
        self.default_duration = 5000  # 5 ثوانٍ
        self.notification_types = {
            'success': 'نجاح',
            'error': 'خطأ',
            'warning': 'تحذير',
            'info': 'معلومات'
        }
    
    def show_pdf_success_notification(self, file_path, file_size=0, show_actions=True):
        """عرض إشعار نجاح حفظ PDF
        
        Args:
            file_path (str): مسار الملف المحفوظ
            file_size (int): حجم الملف بالبايت
            show_actions (bool): عرض أزرار الإجراءات
        
        Returns:
            dict: نتيجة الإشعار
        """
        try:
            # إعداد رسالة النجاح
            filename = os.path.basename(file_path)
            directory = os.path.dirname(file_path)
            
            # تنسيق حجم الملف
            size_text = self._format_file_size(file_size) if file_size > 0 else ""
            
            # إنشاء الرسالة
            message_parts = [
                "✅ تم حفظ الفاتورة كملف PDF بنجاح!",
                f"📄 اسم الملف: {filename}"
            ]
            
            if size_text:
                message_parts.append(f"📊 حجم الملف: {size_text}")
            
            message_parts.append(f"📁 المجلد: {directory}")
            
            message = "\n".join(message_parts)
            
            # عرض الإشعار
            result = self._show_notification(
                title="حفظ PDF ناجح",
                message=message,
                notification_type="success",
                duration=self.default_duration
            )
            
            # إضافة إجراءات إضافية
            if show_actions:
                actions_result = self._show_action_buttons(file_path)
                result.update(actions_result)
            
            return result
            
        except Exception as e:
            print(f"خطأ في عرض إشعار النجاح: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def show_error_notification(self, error_message, error_details=None):
        """عرض إشعار خطأ
        
        Args:
            error_message (str): رسالة الخطأ الرئيسية
            error_details (str, optional): تفاصيل إضافية عن الخطأ
        
        Returns:
            dict: نتيجة الإشعار
        """
        try:
            # إعداد رسالة الخطأ
            message_parts = [
                "❌ فشل في حفظ الفاتورة كملف PDF",
                f"🔍 السبب: {error_message}"
            ]
            
            if error_details:
                message_parts.append(f"📋 التفاصيل: {error_details}")
            
            # إضافة اقتراحات للحل
            suggestions = self._get_error_suggestions(error_message)
            if suggestions:
                message_parts.append("💡 اقتراحات للحل:")
                for suggestion in suggestions:
                    message_parts.append(f"   • {suggestion}")
            
            message = "\n".join(message_parts)
            
            # عرض الإشعار
            result = self._show_notification(
                title="خطأ في حفظ PDF",
                message=message,
                notification_type="error",
                duration=self.default_duration * 2  # مدة أطول للأخطاء
            )
            
            return result
            
        except Exception as e:
            print(f"خطأ في عرض إشعار الخطأ: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def show_printer_not_available_notification(self):
        """عرض إشعار عدم توفر طابعة
        
        Returns:
            dict: نتيجة الإشعار
        """
        try:
            message = """⚠️ لا توجد طابعة متاحة للطباعة
            
🔄 تم تلقائياً حفظ الفاتورة كملف PDF بدلاً من الطباعة
            
📌 يمكنك:
   • طباعة الملف لاحقاً عند توصيل طابعة
   • إرسال الملف إلكترونياً
   • حفظ الملف للمراجعة"""
            
            result = self._show_notification(
                title="تم الحفظ كـ PDF",
                message=message,
                notification_type="warning",
                duration=self.default_duration
            )
            
            return result
            
        except Exception as e:
            print(f"خطأ في عرض إشعار عدم توفر الطابعة: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def show_print_failed_notification(self, printer_name=""):
        """عرض إشعار فشل الطباعة
        
        Args:
            printer_name (str): اسم الطابعة التي فشلت
        
        Returns:
            dict: نتيجة الإشعار
        """
        try:
            printer_text = f" على الطابعة '{printer_name}'" if printer_name else ""
            
            message = f"""❌ فشلت عملية الطباعة{printer_text}
            
🔄 تم تلقائياً حفظ الفاتورة كملف PDF كبديل
            
🔧 أسباب محتملة:
   • مشكلة في اتصال الطابعة
   • نفاد الورق أو الحبر
   • خطأ في تشغيل الطابعة
            
💡 يمكنك المحاولة مرة أخرى لاحقاً أو طباعة ملف PDF"""
            
            result = self._show_notification(
                title="فشل الطباعة - تم الحفظ كـ PDF",
                message=message,
                notification_type="warning",
                duration=self.default_duration
            )
            
            return result
            
        except Exception as e:
            print(f"خطأ في عرض إشعار فشل الطباعة: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def show_validation_warning_notification(self, validation_errors):
        """عرض إشعار تحذيرات التحقق من البيانات
        
        Args:
            validation_errors (list): قائمة بأخطاء التحقق
        
        Returns:
            dict: نتيجة الإشعار
        """
        try:
            if not validation_errors:
                return {'success': True}
            
            message_parts = [
                "⚠️ تم اكتشاف بعض المشاكل في بيانات الفاتورة:",
                ""
            ]
            
            for i, error in enumerate(validation_errors, 1):
                message_parts.append(f"{i}. {error}")
            
            message_parts.extend([
                "",
                "✅ تم إنشاء PDF رغم هذه المشاكل",
                "💡 يُنصح بمراجعة البيانات وتصحيحها"
            ])
            
            message = "\n".join(message_parts)
            
            result = self._show_notification(
                title="تحذيرات في بيانات الفاتورة",
                message=message,
                notification_type="warning",
                duration=self.default_duration * 1.5
            )
            
            return result
            
        except Exception as e:
            print(f"خطأ في عرض إشعار التحذيرات: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def _show_notification(self, title, message, notification_type="info", duration=5000):
        """عرض إشعار أساسي
        
        Args:
            title (str): عنوان الإشعار
            message (str): نص الرسالة
            notification_type (str): نوع الإشعار
            duration (int): مدة العرض بالميلي ثانية
        
        Returns:
            dict: نتيجة عرض الإشعار
        """
        try:
            # طباعة الإشعار في وحدة التحكم (للاختبار)
            print(f"\n{'='*50}")
            print(f"🔔 {self.notification_types.get(notification_type, 'إشعار')}: {title}")
            print(f"{'='*50}")
            print(message)
            print(f"{'='*50}")
            
            # محاولة عرض إشعار PyQt5 إذا كان متاحاً
            try:
                from PyQt5.QtWidgets import QMessageBox, QApplication
                
                # التحقق من وجود تطبيق PyQt5
                app = QApplication.instance()
                if app is None:
                    # إنشاء تطبيق مؤقت
                    app = QApplication([])
                
                # تحديد نوع الرسالة
                if notification_type == "success":
                    icon = QMessageBox.Information
                elif notification_type == "error":
                    icon = QMessageBox.Critical
                elif notification_type == "warning":
                    icon = QMessageBox.Warning
                else:
                    icon = QMessageBox.Information
                
                # إنشاء وعرض الرسالة
                msg_box = QMessageBox()
                msg_box.setIcon(icon)
                msg_box.setWindowTitle(title)
                msg_box.setText(message)
                msg_box.setStandardButtons(QMessageBox.Ok)
                
                # عرض الرسالة (غير مُحجِب)
                msg_box.show()
                
                return {
                    'success': True,
                    'method': 'pyqt5',
                    'duration': duration
                }
                
            except ImportError:
                # PyQt5 غير متوفر، استخدام الطباعة فقط
                return {
                    'success': True,
                    'method': 'console',
                    'duration': duration
                }
            
        except Exception as e:
            print(f"خطأ في عرض الإشعار: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def _show_action_buttons(self, file_path):
        """عرض أزرار الإجراءات للملف
        
        Args:
            file_path (str): مسار الملف
        
        Returns:
            dict: نتيجة عرض الأزرار
        """
        try:
            actions = {
                'open_file': False,
                'open_folder': False,
                'copy_path': False
            }
            
            # طباعة الخيارات المتاحة
            print(f"\n📋 الإجراءات المتاحة للملف:")
            print(f"   1. فتح الملف: {file_path}")
            print(f"   2. فتح المجلد: {os.path.dirname(file_path)}")
            print(f"   3. نسخ المسار إلى الحافظة")
            
            # محاولة فتح المجلد تلقائياً إذا كان مفعلاً في الإعدادات
            if self.config_manager.is_auto_open_enabled():
                try:
                    self._open_file_location(file_path)
                    actions['open_folder'] = True
                    print("✅ تم فتح مجلد الملف تلقائياً")
                except Exception as e:
                    print(f"❌ فشل في فتح المجلد تلقائياً: {str(e)}")
            
            return {
                'success': True,
                'actions': actions,
                'file_path': file_path
            }
            
        except Exception as e:
            print(f"خطأ في عرض أزرار الإجراءات: {str(e)}")
            return {'success': False, 'error': str(e)}
    
    def _open_file_location(self, file_path):
        """فتح مجلد الملف في مستكشف الملفات
        
        Args:
            file_path (str): مسار الملف
        """
        try:
            import subprocess
            import platform
            
            system = platform.system()
            
            if system == "Windows":
                # فتح مستكشف Windows وتحديد الملف
                subprocess.run(['explorer', '/select,', file_path], check=True)
            elif system == "Darwin":  # macOS
                subprocess.run(['open', '-R', file_path], check=True)
            elif system == "Linux":
                # فتح مدير الملفات
                directory = os.path.dirname(file_path)
                subprocess.run(['xdg-open', directory], check=True)
            else:
                print(f"نظام التشغيل {system} غير مدعوم لفتح المجلد")
                
        except Exception as e:
            print(f"خطأ في فتح مجلد الملف: {str(e)}")
            raise
    
    def _format_file_size(self, size_bytes):
        """تنسيق حجم الملف بوحدة مناسبة
        
        Args:
            size_bytes (int): حجم الملف بالبايت
        
        Returns:
            str: حجم الملف منسق
        """
        try:
            if size_bytes == 0:
                return "0 بايت"
            
            # وحدات الحجم
            units = ['بايت', 'كيلوبايت', 'ميجابايت', 'جيجابايت']
            unit_index = 0
            size = float(size_bytes)
            
            while size >= 1024 and unit_index < len(units) - 1:
                size /= 1024
                unit_index += 1
            
            # تنسيق الرقم
            if size == int(size):
                return f"{int(size)} {units[unit_index]}"
            else:
                return f"{size:.1f} {units[unit_index]}"
                
        except Exception as e:
            print(f"خطأ في تنسيق حجم الملف: {str(e)}")
            return f"{size_bytes} بايت"
    
    def _get_error_suggestions(self, error_message):
        """الحصول على اقتراحات لحل الأخطاء
        
        Args:
            error_message (str): رسالة الخطأ
        
        Returns:
            list: قائمة بالاقتراحات
        """
        try:
            suggestions = []
            error_lower = error_message.lower()
            
            # اقتراحات حسب نوع الخطأ
            if "مسار" in error_lower or "path" in error_lower:
                suggestions.extend([
                    "تحقق من صحة مسار حفظ الملف",
                    "تأكد من وجود صلاحيات الكتابة في المجلد",
                    "جرب تغيير مجلد الحفظ من الإعدادات"
                ])
            
            if "مساحة" in error_lower or "space" in error_lower:
                suggestions.extend([
                    "تحقق من توفر مساحة كافية على القرص الصلب",
                    "احذف بعض الملفات غير المطلوبة",
                    "غير مجلد الحفظ إلى قرص آخر"
                ])
            
            if "صلاحية" in error_lower or "permission" in error_lower:
                suggestions.extend([
                    "شغل البرنامج كمدير (Run as Administrator)",
                    "تحقق من صلاحيات الكتابة في المجلد",
                    "غير مجلد الحفظ إلى مجلد آخر"
                ])
            
            if "ذاكرة" in error_lower or "memory" in error_lower:
                suggestions.extend([
                    "أغلق بعض البرامج الأخرى لتوفير ذاكرة",
                    "أعد تشغيل البرنامج",
                    "قلل من جودة PDF في الإعدادات"
                ])
            
            # اقتراحات عامة إذا لم توجد اقتراحات محددة
            if not suggestions:
                suggestions.extend([
                    "أعد المحاولة مرة أخرى",
                    "تحقق من إعدادات PDF",
                    "أعد تشغيل البرنامج إذا استمرت المشكلة"
                ])
            
            return suggestions
            
        except Exception as e:
            print(f"خطأ في الحصول على اقتراحات الأخطاء: {str(e)}")
            return ["أعد المحاولة مرة أخرى"]
    
    def show_custom_notification(self, title, message, notification_type="info", actions=None):
        """عرض إشعار مخصص
        
        Args:
            title (str): عنوان الإشعار
            message (str): نص الرسالة
            notification_type (str): نوع الإشعار
            actions (dict, optional): إجراءات إضافية
        
        Returns:
            dict: نتيجة الإشعار
        """
        try:
            result = self._show_notification(title, message, notification_type)
            
            if actions:
                result['actions'] = actions
            
            return result
            
        except Exception as e:
            print(f"خطأ في عرض الإشعار المخصص: {str(e)}")
            return {'success': False, 'error': str(e)}


class BarcodeHelper:
    """مساعد إنشاء الباركود - الدوال المستخدمة فقط"""

    @staticmethod
    def generate_barcode_image(barcode_text, width=200, height=100):
        """إنشاء صورة باركود باستخدام PIL"""
        try:
            from PIL import Image, ImageDraw, ImageFont

            # إنشاء صورة فارغة
            img = Image.new('RGB', (width, height), 'white')
            draw = ImageDraw.Draw(img)

            # إنشاء نمط باركود
            barcode_pattern = BarcodeHelper.generate_barcode_pattern(barcode_text)

            # حساب أبعاد الباركود مع تحسينات للجودة العالية
            total_bars = len(barcode_pattern)
            barcode_width = int(width * 0.85)  # استخدام مساحة أكبر
            bar_width = max(6, barcode_width // total_bars)  # حد أدنى أكبر لعرض الخطوط (زيادة من 4 إلى 6)
            actual_barcode_width = bar_width * total_bars
            start_x = (width - actual_barcode_width) // 2
            bar_height = int(height * 0.70)  # ارتفاع مُحسن للباركود مع زيادة للوضوح
            start_y = (height - bar_height - 30) // 2

            # رسم الخطوط
            x = start_x
            for bar in barcode_pattern:
                if bar == '1':
                    draw.rectangle([x, start_y, x + bar_width, start_y + bar_height], fill='black')
                x += bar_width

            # إضافة النص
            try:
                font = ImageFont.truetype("arial.ttf", 12)
            except:
                font = ImageFont.load_default()

            text_width = draw.textlength(barcode_text, font=font)
            text_x = (width - text_width) // 2
            text_y = start_y + bar_height + 10
            draw.text((text_x, text_y), barcode_text, fill='black', font=font)

            return img

        except ImportError:
            # إذا لم تكن PIL متوفرة، استخدم PyQt5
            return BarcodeHelper.generate_barcode_pixmap(barcode_text, width, height)
        except Exception as e:
            print(f"خطأ في إنشاء صورة الباركود: {str(e)}")
            return None

    @staticmethod
    def generate_barcode_pixmap(barcode_text, width=200, height=100, show_barcode_text=True, show_price=False, price_text=""):
        """إنشاء صورة باركود بتقنية رياضية مثالية لمنع التعرج نهائياً"""
        try:
            from PyQt5.QtGui import QPixmap, QImage
            from PyQt5.QtCore import Qt
            
            # حساب أبعاد الباركود بدقة رياضية
            barcode_pattern = BarcodeHelper.generate_barcode_pattern(barcode_text)
            total_bars = len(barcode_pattern)
            
            # حساب عرض الخط المناسب حسب المعايير الدولية
            min_bar_width = 8  # حد أدنى محسن لضمان عدم التعرج
            available_width = int(width * 0.8)  # 80% من العرض للمناطق الهادئة
            calculated_width = available_width // total_bars
            bar_width = max(min_bar_width, calculated_width)
            
            # ضمان أن العرض رقم زوجي لتجنب التشويه
            if bar_width % 2 != 0:
                bar_width += 1
                
            actual_width = bar_width * total_bars
            # ضبط ارتفاع الباركود بناءً على وجود نص أسفله (رقم/سعر)
            text_elements = 0
            if show_barcode_text:
                text_elements += 1
            if show_price and price_text:
                text_elements += 1
            if text_elements == 0:
                barcode_height = int(height * 0.80)  # تقليل ارتفاع الباركود قليلاً
            elif text_elements == 1:
                barcode_height = int(height * 0.65)  # تقليل ارتفاع الباركود قليلاً
            else:
                barcode_height = int(height * 0.55)  # تقليل ارتفاع الباركود قليلاً لترك مساحة أكبر للنصوص
            
            # إنشاء صورة مونوكروم بدقة مثالية (1 بت لكل بكسل)
            barcode_image = QImage(actual_width, barcode_height, QImage.Format_Mono)
            barcode_image.fill(1)  # ملء بالأبيض (1 = أبيض)
            
            # رسم الخطوط مباشرة على البكسلات - طريقة مثالية لمنع التعرج
            current_x = 0
            for bar in barcode_pattern:
                if bar == '1':
                    # ملء البكسلات مباشرة بالأسود (0 = أسود)
                    for x in range(current_x, current_x + bar_width):
                        for y in range(barcode_height):
                            if x < actual_width:  # تأكد من عدم تجاوز الحدود
                                barcode_image.setPixel(x, y, 0)  # أسود نقي
                current_x += bar_width
            
            # إنشاء pixmap نهائي مع إضافة النص والخلفية
            final_pixmap = QPixmap(width, height)
            final_pixmap.fill(Qt.GlobalColor.white)
            
            painter = QPainter(final_pixmap)
            # تعطيل جميع أنواع التنعيم لضمان الخطوط الحادة
            painter.setRenderHint(QPainter.Antialiasing, False)
            painter.setRenderHint(QPainter.TextAntialiasing, True)  # نص محسن فقط
            painter.setRenderHint(QPainter.SmoothPixmapTransform, False)
            painter.setRenderHint(QPainter.HighQualityAntialiasing, False)
            
            # رسم الباركود في الوسط بدون أي تشويه
            barcode_pixmap = QPixmap.fromImage(barcode_image)
            x_pos = (width - actual_width) // 2
            # تمركز رأسي مع ترك مساحة للنصوص أسفل الباركود داخل الصورة
            y_pos = max(0, (height - barcode_height) // 2 - int(height * 0.02))
            
            # رسم الباركوح بدون أي تكبير أو تحويل (1:1 mapping)
            painter.drawPixmap(x_pos, y_pos, barcode_pixmap)
            
            # إضافة النصوص داخل الصورة: رقم الباركود ثم السعر (إن وجد)
            # تم تحسين المسافات بين العناصر لمنع التداخل
            number_font_size = max(11, int(height * 0.16))
            font = QFont("Courier New", number_font_size, QFont.Bold)
            font.setHintingPreference(QFont.PreferFullHinting)
            painter.setFont(font)
            painter.setPen(Qt.GlobalColor.black)

            try:
                dpi_y = painter.device().logicalDpiY()
            except Exception:
                dpi_y = 96
            one_mm_px = max(1, int(round(dpi_y / 25.4)))
            
            # حساب المسافات بين العناصر - تحسين المسافة بين الرقم والسعر
            gap_number = one_mm_px * 3  # مسافة أكبر بين الباركود والرقم
            price_gap = one_mm_px * 6   # مسافة أكبر بكثير بين الرقم والسعر لضمان عدم التداخل

            metrics = painter.fontMetrics()
            current_baseline = y_pos + barcode_height

            if show_barcode_text:
                number_rect = metrics.boundingRect(barcode_text)
                number_x = (width - number_rect.width()) // 2
                number_baseline = current_baseline + gap_number + metrics.ascent()
                bottom_limit = height - max(4, int(height * 0.02))
                if number_baseline + metrics.descent() > bottom_limit:
                    number_baseline = bottom_limit - metrics.descent()
                painter.drawText(number_x, number_baseline, barcode_text)
                current_baseline = number_baseline + metrics.descent()

            if show_price and price_text:
                price_font = QFont("Courier New", number_font_size, QFont.Bold)
                painter.setFont(price_font)
                price_metrics = painter.fontMetrics()
                price_rect = price_metrics.boundingRect(price_text)
                price_x = (width - price_rect.width()) // 2
                # استخدام مسافة أكبر بكثير بين الرقم والسعر لضمان عدم التداخل
                price_baseline = current_baseline + price_gap + price_metrics.ascent()
                bottom_limit2 = height - max(4, int(height * 0.02))
                if price_baseline + price_metrics.descent() > bottom_limit2:
                    price_baseline = bottom_limit2 - price_metrics.descent()
                painter.drawText(price_x, price_baseline, price_text)
            
            painter.end()
            return final_pixmap

        except Exception as e:
            print(f"خطأ في إنشاء Pixmap الباركود: {str(e)}")
            return None

    @staticmethod
    def generate_barcode_pattern(barcode_text):
        """توليد نمط خطوط الباركود Codabar احترافي ودقيق"""
        # جدول أنماط Codabar
        codabar_patterns = {
            '0': '0000011', '1': '0000110', '2': '0001001', '3': '1100000', '4': '0010010',
            '5': '1000010', '6': '0100001', '7': '0100100', '8': '0110000', '9': '1001000',
            '-': '0001100', '$': '0011000', ':': '1000101', '/': '1010001', '.': '1010100',
            '+': '0010101', 'A': '0011010', 'B': '0101001', 'C': '0001011', 'D': '0001110'
        }
        
        def convert_pattern_to_bars(pattern, narrow_width=1, wide_width=3):
            bars = []
            for i, bit in enumerate(pattern):
                is_bar = (i % 2 == 0)
                width = wide_width if bit == '1' else narrow_width
                bars.extend(['1'] * width if is_bar else ['0'] * width)
            return bars
        
        # تنظيف النص وإضافة أحرف البداية/النهاية
        clean_text = ''.join(c for c in barcode_text.upper() if c in '0*********-$:/.+ABCD')
        if not clean_text.startswith(('A', 'B', 'C', 'D')): clean_text = 'A' + clean_text
        if not clean_text.endswith(('A', 'B', 'C', 'D')): clean_text = clean_text + 'A'
        
        # بناء النمط
        full_pattern = []
        for i, char in enumerate(clean_text):
            char_pattern = codabar_patterns.get(char, codabar_patterns['0'])
            full_pattern.extend(convert_pattern_to_bars(char_pattern))
            if i < len(clean_text) - 1: full_pattern.extend(['0'])  # مسافة فاصلة
        
        return ''.join(full_pattern)


class ESCPOSInvoicePrinter:
    """طابعة الفواتير - الدوال المستخدمة فقط"""

    def __init__(self):
        """تهيئة طابعة الفواتير"""
        pass

    def print_invoice(self, invoice_data, items_data, company_info, printer_name=None):
        """طباعة الفاتورة - الدالة الرئيسية المستخدمة"""
        try:
            print("بدء طباعة الفاتورة بالتصميم المرئي المحسن...")
            
            # تشخيص إضافي للطابعة
            if printer_name:
                print(f"الطابعة المحددة: {printer_name}")
                # تحقق خاص لطابعة Xprinter XP-235B
                if "xprinter" in printer_name.lower() or "xp-235" in printer_name.lower():
                    print("تم اكتشاف طابعة Xprinter XP-235B - استخدام إعدادات خاصة")
            else:
                print("استخدام الطابعة الافتراضية")
                
                # محاولة اكتشاف الطابعة الافتراضية
                from utils.thermal_printer_helper import PrinterDetector
                detector = PrinterDetector()
                default_printer = detector.get_default_printer()
                if default_printer:
                    print(f"الطابعة الافتراضية المكتشفة: {default_printer}")
                    # تحقق خاص لطابعة Xprinter XP-235B
                    if "xprinter" in default_printer.lower() or "xp-235" in default_printer.lower():
                        print("تم اكتشاف طابعة Xprinter XP-235B كافتراضية - استخدام إعدادات خاصة")

            # التحقق من صحة البيانات قبل الطباعة
            is_valid, errors, warnings = self.validate_print_data(invoice_data, company_info)
            
            if not is_valid:
                print("❌ لا يمكن طباعة الفاتورة بسبب أخطاء في البيانات:")
                for error in errors:
                    print(f"  - {error}")
                return False
            
            if warnings:
                print("⚠️ سيتم طباعة الفاتورة مع التحذيرات التالية:")
                for warning in warnings:
                    print(f"  - {warning}")

            # محاولات متعددة للطباعة
            max_attempts = 3
            for attempt in range(1, max_attempts + 1):
                print(f"محاولة الطباعة رقم {attempt} من {max_attempts}...")
                
                # الطريقة الأولى: التصميم المرئي المحسن
                print("جاري تجربة طريقة التصميم المرئي المحسن...")
                success = self.print_invoice_with_enhanced_visual_design(
                    invoice_data, items_data, company_info, printer_name
                )

                if success:
                    print(f"✅ نجحت طباعة التصميم المرئي المحسن في المحاولة رقم {attempt}")
                    return True
                
                # انتظار قصير قبل المحاولة التالية
                if attempt < max_attempts:
                    import time
                    time.sleep(1)  # انتظار ثانية واحدة
            
            # محاولة أخيرة باستخدام طريقة بديلة للطابعات المشكلة
            print("جاري تجربة طريقة بديلة للطباعة...")
            try:
                from PyQt5.QtPrintSupport import QPrinter, QPrinterInfo
                from PyQt5.QtCore import QSettings
                from utils.invoice_designer import InvoiceDesigner
                
                # إنشاء كائن InvoiceDesigner
                from utils.settings_manager import SettingsManager
                settings_manager = SettingsManager()
                paper_width = settings_manager.get_paper_width_safe()
                
                paper_size = "80mm"
                if paper_width == 58:
                    paper_size = "58mm"
                elif paper_width == 210:
                    paper_size = "A4"
                
                # إنشاء مصمم الفاتورة مع إعدادات خاصة
                invoice_designer = InvoiceDesigner(company_info=company_info, paper_size=paper_size)
                
                # استخدام طريقة طباعة مباشرة
                from PyQt5.QtPrintSupport import QPrinter
                printer = QPrinter(QPrinter.HighResolution)
                
                # تعيين الطابعة المحددة
                if printer_name:
                    printer.setPrinterName(printer_name)
                
                # تعيين حجم الورق
                if paper_size == "58mm":
                    from PyQt5.QtCore import QSizeF
                    printer.setPageSize(QPrinter.Custom)
                    printer.setPaperSize(QSizeF(58, 200), QPrinter.Millimeter)
                elif paper_size == "A4":
                    printer.setPageSize(QPrinter.A4)
                else:  # 80mm
                    from PyQt5.QtCore import QSizeF
                    printer.setPageSize(QPrinter.Custom)
                    printer.setPaperSize(QSizeF(80, 200), QPrinter.Millimeter)
                
                # توليد HTML للفاتورة
                html = invoice_designer.generate_invoice_html(invoice_data, items_data)
                
                # إنشاء مستند HTML
                from PyQt5.QtGui import QTextDocument
                document = QTextDocument()
                document.setHtml(html)
                
                # طباعة المستند
                document.print_(printer)
                
                print("✅ نجحت الطباعة باستخدام الطريقة البديلة")
                return True
            except Exception as alt_error:
                print(f"❌ فشلت الطريقة البديلة للطباعة: {str(alt_error)}")
            
            print("❌ فشلت جميع طرق الطباعة")
            return False

        except Exception as e:
            print(f"خطأ في طباعة الفاتورة: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def print_invoice_with_enhanced_visual_design(self, invoice_data, items_data, company_info, printer_name=None, custom_printer=None):
        """طباعة الفاتورة بتصميم مرئي محسن"""
        try:
            # قراءة إعداد حجم الورقة من الإعدادات باستخدام المفتاح الموحد
            from utils.settings_manager import SettingsManager
            from utils.invoice_designer import InvoiceDesigner
            settings_manager = SettingsManager()
            paper_width = settings_manager.get_paper_width_safe()
            
            # تعيين خيار التوسيط للفاتورة
            settings_manager.set_setting("invoice_design", "center_content", True)

            print(f"حجم الورقة المقروء من الإعدادات: {paper_width}مم")
            
            # استخدام مصمم الفاتورة المحدث للطباعة
            paper_size = "80mm"
            if paper_width == 58:
                paper_size = "58mm"
            elif paper_width == 210:
                paper_size = "A4"
            
            # إنشاء كائن InvoiceDesigner وتوليد HTML للفاتورة
            invoice_designer = InvoiceDesigner(company_info=company_info, paper_size=paper_size)
            
            # استخدام الطريقة المباشرة للطباعة من InvoiceDesigner
            return invoice_designer.print_invoice(invoice_data, items_data, printer_name)

            # إنشاء طابعة أو استخدام الطابعة المخصصة
            if custom_printer:
                printer = custom_printer
            else:
                printer = QPrinter(QPrinter.HighResolution)
                printer.setOutputFormat(QPrinter.NativeFormat)

                # تعيين حجم الورق بناءً على الإعداد المحفوظ
                printer.setPageSize(QPrinter.Custom)
                if paper_width == 80:
                    printer.setPaperSize(QSizeF(80, 250), QPrinter.Millimeter)
                    print("تم تعيين حجم الورق: 80مم")
                elif paper_width == 210:  # A4 paper
                    printer.setPaperSize(QSizeF(210, 297), QPrinter.Millimeter)  # A4 size
                    print("تم تعيين حجم الورق: A4")
                else:
                    printer.setPaperSize(QSizeF(58, 200), QPrinter.Millimeter)
                    print("تم تعيين حجم الورق: 58مم")

            # تعيين هوامش أعلى/أسفل قابلة للتخصيص (مم) في جميع الحالات
            try:
                top_margin_mm = settings.value("invoice_design/margin_top_mm", 5, type=float)
                bottom_margin_mm = settings.value("invoice_design/margin_bottom_mm", 5, type=float)
            except Exception:
                top_margin_mm = 5
                bottom_margin_mm = 5
            # الحفاظ على 5مم لليسار/اليمين كما هو لضمان الاتساق الأفقي
            printer.setPageMargins(5, top_margin_mm, 5, bottom_margin_mm, QPrinter.Millimeter)

            # تعيين الطابعة المحددة
            if printer_name and printer_name != "الطابعة الافتراضية" and not custom_printer:
                printer.setPrinterName(printer_name)

            # بدء الرسم
            painter = QPainter()
            if not painter.begin(printer):
                return False

            # إعداد الخطوط
            company_font = QFont("Tahoma", 13, QFont.Bold)
            info_font = QFont("Tahoma", 10)
            invoice_info_font = QFont("Tahoma", 10)
            table_header_font = QFont("Tahoma", 9, QFont.Bold)
            table_data_font = QFont("Tahoma", 8)
            total_font = QFont("Tahoma", 12, QFont.Bold)     # من 10 إلى 9
            notes_font = QFont("Tahoma", 10)

            # إعداد القلم
            pen = QPen(Qt.GlobalColor.black, 1)
            painter.setPen(pen)

            # الحصول على أبعاد الصفحة
            page_rect = printer.pageRect()
            width = page_rect.width()

            # تعديل الهوامش بناءً على حجم الورقة لضمان التوسيط المثالي
            from utils.settings_manager import SettingsManager
            settings_manager = SettingsManager()
            paper_width = settings_manager.get_paper_width_safe()

            if paper_width == 80:
                # هوامش للتوسيط المثالي - تحريك قوي جداً جهة اليمين
                total_margin = 60  # إجمالي الهوامش
                margin_left = 10   # هامش أيسر صغير جداً لتحريك الفاتورة يميناً
                margin_right = 50  # هامش أيمن كبير جداً لتحريك الفاتورة يميناً
                margin = margin_left  # استخدام الهامش الأيسر للحسابات
                content_width = width - total_margin
            elif paper_width == 210:  # A4 paper
                # هوامش لورقة A4 - هوامش أكبر لتنسيق أفضل
                total_margin = 120  # هوامش أكبر لورقة A4
                margin_left = 40   # هامش أيسر
                margin_right = 80  # هامش أيمن
                margin = margin_left
                content_width = width - total_margin
            else:
                # الهوامش العادية للمقاس الصغير
                margin_left = 30
                margin_right = 30
                margin = margin_left  # للتوافق مع الكود القديم
                content_width = width - (margin_left + margin_right)

            # تم حساب content_width أعلاه حسب نوع الورقة
            # ابدأ الرسم من أعلى pageRect (الذي يأخذ الهوامش في الاعتبار) مع إزاحة إضافية لمنع قص النص
            y_pos = page_rect.top() + 25

            # إنشاء مدير التخطيط التكيفي الجديد
            try:
                layout_manager = AdaptiveLayoutManager(paper_width, content_width)
                
                # رسم معلومات الشركة باستخدام النظام الجديد
                y_pos = layout_manager.layout_company_section(painter, company_info, y_pos)
                # مسافة أمان إضافية لمنع تكدس النص
                y_pos += 15
                
                # رسم معلومات الفاتورة باستخدام النظام الجديد
                invoice_data_formatted = {
                    'invoice_number': invoice_data.get('reference_number', 'INV-001'),
                    'date': invoice_data.get('date', ''),
                    'customer_name': invoice_data.get('customer_name', 'عميل نقدي')
                }
                y_pos = layout_manager.layout_invoice_section(painter, invoice_data_formatted, y_pos)
                # مسافة أمان إضافية قبل الجدول
                y_pos += 20
                
            except Exception as e:
                print(f"خطأ في استخدام النظام الجديد، العودة للنظام القديم: {str(e)}")
                
                # النظام الاحتياطي القديم في حالة الخطأ
                painter.setFont(company_font)
                fm = QFontMetrics(company_font)
                text_height = fm.height() + fm.descent() + 24

                # اسم الشركة
                company_name = company_info.get('name', company_info.get('company_name', ''))
                if company_name and company_name.strip():
                    painter.drawText(QRect(margin_left, y_pos, content_width, text_height),
                                   Qt.AlignmentFlag.AlignCenter, company_name)
                else:
                    # إذا لم يكن هناك اسم شركة، لا نعرض شيئاً أو نعرض رسالة تحذير
                    painter.drawText(QRect(margin_left, y_pos, content_width, text_height),
                                   Qt.AlignmentFlag.AlignCenter, "[يرجى إدخال اسم الشركة في الإعدادات]")
                y_pos += text_height + 50  # زيادة من 36 إلى 50

                # رقم الهاتف
                painter.setFont(info_font)
                fm = QFontMetrics(info_font)
                text_height = fm.height() + fm.descent() + 20
                company_phone = company_info.get('phone', company_info.get('company_phone', ''))
                if company_phone and company_phone.strip():
                    painter.drawText(QRect(margin_left, y_pos, content_width, text_height),
                                   Qt.AlignmentFlag.AlignCenter, f"هاتف: {company_phone}")
                else:
                    # لا نعرض شيئاً إذا لم يكن هناك رقم هاتف
                    text_height = 0
                y_pos += text_height + 40  # زيادة من 26 إلى 40

                # العنوان
                company_address = company_info.get('address', company_info.get('company_address', ''))
                if company_address and company_address.strip():
                    painter.drawText(QRect(margin_left, y_pos, content_width, text_height),
                                   Qt.AlignmentFlag.AlignCenter, company_address)
                else:
                    # لا نعرض شيئاً إذا لم يكن هناك عنوان
                    text_height = 0
                y_pos += text_height + 50  # زيادة من 34 إلى 50

                # خط فاصل
                painter.drawLine(margin_left, y_pos, width - margin_right, y_pos)
                y_pos += 45

                # معلومات الفاتورة
                painter.setFont(invoice_info_font)
                fm = QFontMetrics(invoice_info_font)
                text_height = fm.height() + fm.descent() + 20

                # رقم الفاتورة
                invoice_number = invoice_data.get('reference_number', invoice_data.get('invoice_number', ''))
                if invoice_number and invoice_number.strip():
                    painter.drawText(QRect(margin_left, y_pos, content_width, text_height),
                                   Qt.AlignmentFlag.AlignCenter, f"رقم الفاتورة: {invoice_number}")
                else:
                    painter.drawText(QRect(margin_left, y_pos, content_width, text_height),
                                   Qt.AlignmentFlag.AlignCenter, "[رقم الفاتورة غير محدد]")
                y_pos += text_height + 45  # زيادة من 30 إلى 45

                # التاريخ
                painter.drawText(QRect(margin_left, y_pos, content_width, text_height),
                               Qt.AlignmentFlag.AlignCenter, f"{invoice_data.get('date', '')}")
                y_pos += text_height + 45  # زيادة من 30 إلى 45

                # العميل
                painter.drawText(QRect(margin_left, y_pos, content_width, text_height),
                               Qt.AlignmentFlag.AlignCenter, f"العميل: {invoice_data.get('customer_name', 'عميل نقدي')}")
                y_pos += text_height + 55  # زيادة من 40 إلى 55

            # جدول المنتجات
            # قراءة حجم الورقة لتعديل حجم الجدول
            from utils.settings_manager import SettingsManager
            settings_manager = SettingsManager()
            paper_width = settings_manager.get_paper_width_safe()

            # تعديل حجم الجدول بناءً على حجم الورقة مع تحسين ارتفاع الرأس
            if paper_width == 80:
                # حجم محسن للمقاس الكبير (80مم) - توزيع أفضل للأعمدة
                col1_width = int(content_width * 0.42)  # المنتج - زيادة أكثر
                col2_width = int(content_width * 0.19)  # السعر
                col3_width = int(content_width * 0.16)  # الكمية
                col4_width = content_width - col1_width - col2_width - col3_width  # الإجمالي
                row_height = 110  # زيادة الارتفاع لضمان ظهور النص كاملاً
                cell_padding = 20  # تقليل الحشو لإعطاء مساحة أكبر للنص
            elif paper_width == 210:  # A4 paper
                # حجم محسن لورقة A4 - توزيع أفضل للفاتورة العادية
                col1_width = int(content_width * 0.40)  # المنتج
                col2_width = int(content_width * 0.20)  # السعر
                col3_width = int(content_width * 0.15)  # الكمية
                col4_width = content_width - col1_width - col2_width - col3_width  # الإجمالي
                row_height = 120  # ارتفاع محسن لورقة A4
                cell_padding = 25  # حشو محسن لورقة A4
            else:
                # الحجم العادي للمقاس الصغير (58مم)
                col1_width = int(content_width * 0.45)  # المنتج
                col2_width = int(content_width * 0.18)  # السعر
                col3_width = int(content_width * 0.15)  # الكمية
                col4_width = content_width - col1_width - col2_width - col3_width  # الإجمالي
                row_height = 100  # زيادة الارتفاع للورق الصغير أيضاً
                cell_padding = 18  # تقليل الحشو لإعطاء مساحة أكبر للنص

            # رسم رأس الجدول
            painter.setFont(table_header_font)
            header_rect = QRect(margin_left, y_pos, content_width, row_height + 12)
            painter.fillRect(header_rect, QBrush(Qt.GlobalColor.lightGray))
            painter.drawRect(header_rect)

            # عناوين الأعمدة (من اليمين لليسار)
            x_offset = margin_left + content_width - col1_width
            painter.drawText(QRect(x_offset + cell_padding, y_pos + cell_padding,
                                 col1_width - (cell_padding * 2), row_height - (cell_padding * 2)),
                           Qt.AlignmentFlag.AlignCenter, "المنتج")

            x_offset -= col2_width
            painter.drawText(QRect(x_offset + cell_padding, y_pos + cell_padding,
                                 col2_width - (cell_padding * 2), row_height - (cell_padding * 2)),
                           Qt.AlignmentFlag.AlignCenter, "السعر")

            x_offset -= col3_width
            painter.drawText(QRect(x_offset + cell_padding, y_pos + cell_padding,
                                 col3_width - (cell_padding * 2), row_height - (cell_padding * 2)),
                           Qt.AlignmentFlag.AlignCenter, "الكمية")

            x_offset -= col4_width
            painter.drawText(QRect(x_offset + cell_padding, y_pos + cell_padding,
                                 col4_width - (cell_padding * 2), row_height - (cell_padding * 2)),
                           Qt.AlignmentFlag.AlignCenter, "الإجمالي")

            # رسم خطوط فاصلة عمودية في رأس الجدول
            x_offset = margin_left + content_width - col1_width
            painter.drawLine(x_offset, y_pos, x_offset, y_pos + row_height)
            x_offset -= col2_width
            painter.drawLine(x_offset, y_pos, x_offset, y_pos + row_height)
            x_offset -= col3_width
            painter.drawLine(x_offset, y_pos, x_offset, y_pos + row_height)

            # رسم الحد الخارجي الأيمن لرأس الجدول (الحد المفقود)
            right_border_x = width - margin_right
            painter.drawLine(right_border_x, y_pos, right_border_x, y_pos + row_height)

            y_pos += (row_height + 12)

            # رسم صفوف البيانات
            painter.setFont(table_data_font)
            fm = QFontMetrics(table_data_font)
            data_row_height = fm.height() + fm.descent() + cell_padding * 2 + 80  # زيادة إضافية لمنع قص النص

            for item in items_data:
                # رسم مستطيل الصف
                row_rect = QRect(margin_left, y_pos, content_width, data_row_height + 10)
                painter.drawRect(row_rect)

                # اسم المنتج (مع تقصير إذا لزم الأمر)
                x_offset = margin_left + content_width - col1_width
                product_name = item.get('product_name', '')
                if len(product_name) > 25:  # حد محسن
                    product_name = product_name[:22] + "..."
                painter.drawText(QRect(x_offset + cell_padding, y_pos + cell_padding,
                                     col1_width - (cell_padding * 2), data_row_height - (cell_padding * 2)),
                               Qt.AlignmentFlag.AlignCenter, product_name)

                # السعر
                x_offset -= col2_width
                painter.drawText(QRect(x_offset + cell_padding, y_pos + cell_padding,
                                     col2_width - (cell_padding * 2), data_row_height - (cell_padding * 2)),
                               Qt.AlignmentFlag.AlignCenter, f"{item.get('unit_price', 0):.2f}")

                # الكمية
                x_offset -= col3_width
                painter.drawText(QRect(x_offset + cell_padding, y_pos + cell_padding,
                                     col3_width - (cell_padding * 2), data_row_height - (cell_padding * 2)),
                               Qt.AlignmentFlag.AlignCenter, str(item.get('quantity', 0)))

                # الإجمالي
                x_offset -= col4_width
                painter.drawText(QRect(x_offset + cell_padding, y_pos + cell_padding,
                                     col4_width - (cell_padding * 2), data_row_height - (cell_padding * 2)),
                               Qt.AlignmentFlag.AlignCenter, f"{item.get('total_price', 0):.2f}")

                # رسم خطوط فاصلة عمودية لصفوف البيانات
                x_offset = margin_left + content_width - col1_width
                painter.drawLine(x_offset, y_pos, x_offset, y_pos + data_row_height)
                x_offset -= col2_width
                painter.drawLine(x_offset, y_pos, x_offset, y_pos + data_row_height)
                x_offset -= col3_width
                painter.drawLine(x_offset, y_pos, x_offset, y_pos + data_row_height)

                # رسم الحد الخارجي الأيمن لعمود المنتج (الحد المفقود)
                right_border_x = width - margin_right
                painter.drawLine(right_border_x, y_pos, right_border_x, y_pos + data_row_height)

                y_pos += data_row_height + 12

            y_pos += 35

            # مربع الإجمالي مع تحسين الأبعاد والهوامش
            painter.setFont(total_font)
            
            # حساب أبعاد محسنة حسب حجم الورق
            if paper_width == 80:
                total_box_width = int(content_width * 0.85)  # عرض أكبر
                total_box_height = 74  # ارتفاع أكبر لمنع قص النص
                padding = 18  # مسافة داخلية
            elif paper_width == 210:  # A4
                total_box_width = int(content_width * 0.75)
                total_box_height = 120  # زيادة الارتفاع من 84 إلى 120
                padding = 22
            else:  # 58mm
                total_box_width = int(content_width * 0.90)  # عرض أكبر للورق الصغير
                total_box_height = 68
                padding = 14
            
            # توسيط المربع مع ضبط الهوامش
            total_box_x = margin_left + (content_width - total_box_width) // 2
            
            # التأكد من أن المربع لا يتجاوز حدود الصفحة
            if total_box_x < margin_left:
                total_box_x = margin_left
                total_box_width = content_width - 20  # ترك هامش صغير
            
            total_rect = QRect(total_box_x, y_pos, total_box_width, total_box_height)
            painter.drawRect(total_rect)
            
            # رسم النص مع مسافة داخلية محسنة
            text_rect = QRect(total_box_x + padding, y_pos + padding, 
                            total_box_width - (padding * 2), total_box_height - (padding * 2))
            painter.drawText(text_rect, Qt.AlignmentFlag.AlignCenter,
                           f"الإجمالي {invoice_data.get('total', 0):.2f} ج.م")

            y_pos += total_box_height + 35

            # خط فاصل قبل الملاحظات
            painter.drawLine(margin_left, y_pos, width - margin_right, y_pos)
            y_pos += 25

            # الملاحظات
            painter.setFont(notes_font)
            fm = QFontMetrics(notes_font)
            text_height = fm.height() + fm.descent() + 24  # إضافة مساحة أكبر أعلى/أسفل النص
            notes_rect = QRect(margin_left, y_pos, content_width, text_height + 28)
            painter.drawText(notes_rect, Qt.AlignmentFlag.AlignCenter,
                           company_info.get('notes', 'شكراً لتعاملكم معنا'))

            painter.end()
            return True

        except Exception as e:
            print(f"فشل في طباعة التصميم المرئي المحسن: {str(e)}")
            return False

    def print_invoice_with_dialog(self, invoice_data, items_data, company_info, parent_widget=None):
        """طباعة الفاتورة مع عرض مربع حوار الطباعة"""
        try:
            # قراءة إعداد حجم الورقة من الإعدادات باستخدام المفتاح الموحد
            from utils.settings_manager import SettingsManager
            settings_manager = SettingsManager()
            paper_width = settings_manager.get_paper_width_safe()

            print(f"حجم الورقة المقروء من الإعدادات (مع الحوار): {paper_width}مم")

            # إنشاء طابعة
            printer = QPrinter(QPrinter.HighResolution)
            printer.setOutputFormat(QPrinter.NativeFormat)

            # تعيين حجم الورق بناءً على الإعداد المحفوظ
            printer.setPageSize(QPrinter.Custom)
            if paper_width == 80:
                printer.setPaperSize(QSizeF(80, 250), QPrinter.Millimeter)
                print("تم تعيين حجم الورق (مع الحوار): 80مم")
            elif paper_width == 210:  # A4 paper
                printer.setPaperSize(QSizeF(210, 297), QPrinter.Millimeter)
                print("تم تعيين حجم الورق (مع الحوار): A4")
            else:
                printer.setPaperSize(QSizeF(58, 200), QPrinter.Millimeter)
                print("تم تعيين حجم الورق (مع الحوار): 58مم")

            # تعيين هوامش أعلى/أسفل قابلة للتخصيص (مم) لمسار الحوار أيضاً
            try:
                top_margin_mm = settings.value("invoice_design/margin_top_mm", 5, type=float)
                bottom_margin_mm = settings.value("invoice_design/margin_bottom_mm", 5, type=float)
            except Exception:
                top_margin_mm = 5
                bottom_margin_mm = 5
            printer.setPageMargins(5, top_margin_mm, 5, bottom_margin_mm, QPrinter.Millimeter)

            # عرض مربع حوار الطباعة
            print_dialog = QPrintDialog(printer, parent_widget)
            print_dialog.setWindowTitle("طباعة الفاتورة")

            if print_dialog.exec_() == QPrintDialog.Accepted:
                # استخدام التصميم المرئي المحسن للطباعة
                success = self.print_invoice_with_enhanced_visual_design(invoice_data, items_data, company_info, None)
                return success
            else:
                # المستخدم ألغى الطباعة
                return False

        except Exception as e:
            print(f"فشل في طباعة الفاتورة مع الحوار: {str(e)}")
            return False

    def create_preview_widget(self, invoice_data, items_data, company_info, parent=None):
        """إنشاء widget معاينة يستخدم نفس التصميم المرئي للطباعة"""
        try:
            print("بدء إنشاء widget المعاينة...")

            # التحقق من صحة البيانات
            if not invoice_data or not items_data or not company_info:
                print("بيانات غير صحيحة للمعاينة")
                return self._create_error_widget("بيانات غير صحيحة للمعاينة", parent)

            # إنشاء widget المعاينة
            preview_widget = QWidget(parent)
            layout = QVBoxLayout(preview_widget)
            layout.setContentsMargins(5, 5, 5, 5)

            # حساب أبعاد المعاينة بناءً على حجم الورقة
            from utils.settings_manager import SettingsManager
            settings_manager = SettingsManager()
            paper_width = settings_manager.get_paper_width_safe()

            if paper_width == 80:
                preview_width = 400  # أعرض للمقاس الكبير
                preview_height = 800
            elif paper_width == 210:  # A4 paper
                preview_width = 600  # عرض أكبر لورقة A4
                preview_height = 800
            else:
                preview_width = 300  # العرض العادي للمقاس الصغير
                preview_height = 700

            print(f"أبعاد المعاينة: {preview_width}x{preview_height}")

            # إنشاء pixmap لرسم المعاينة
            pixmap = QPixmap(preview_width, preview_height)
            if pixmap.isNull():
                print("فشل في إنشاء QPixmap")
                return self._create_error_widget("فشل في إنشاء QPixmap", parent)

            pixmap.fill(Qt.GlobalColor.white)

            # رسم المعاينة باستخدام نفس منطق الطباعة
            painter = QPainter()
            if not painter.begin(pixmap):
                print("فشل في بدء QPainter")
                return self._create_error_widget("فشل في بدء QPainter", parent)

            try:
                self._draw_invoice_preview(painter, invoice_data, items_data, company_info, preview_width, preview_height)
                print("تم رسم المعاينة بنجاح")
            except Exception as draw_error:
                print(f"خطأ في رسم المعاينة: {str(draw_error)}")
                painter.end()
                return self._create_error_widget(f"خطأ في رسم المعاينة: {str(draw_error)}", parent)

            painter.end()

            # إنشاء label لعرض الصورة
            image_label = QLabel()
            image_label.setPixmap(pixmap)
            image_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            image_label.setStyleSheet("""
                QLabel {
                    border: 1px solid #ddd;
                    background-color: white;
                    margin: 2px;
                }
            """)

            layout.addWidget(image_label)
            preview_widget.setFixedSize(preview_width + 20, preview_height + 20)

            print("تم إنشاء widget المعاينة بنجاح")
            return preview_widget

        except Exception as e:
            print(f"خطأ عام في إنشاء widget المعاينة: {str(e)}")
            import traceback
            traceback.print_exc()
            return self._create_error_widget(f"خطأ عام: {str(e)}", parent)

    def _create_error_widget(self, error_message, parent=None):
        """إنشاء widget خطأ آمن"""
        try:
            error_widget = QWidget(parent)
            error_layout = QVBoxLayout(error_widget)
            error_label = QLabel(f"خطأ في عرض المعاينة:\n{error_message}")
            error_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            error_label.setStyleSheet("""
                QLabel {
                    color: red;
                    padding: 20px;
                    border: 1px solid red;
                    background-color: #ffe6e6;
                }
            """)
            error_layout.addWidget(error_label)
            error_widget.setFixedSize(300, 200)
            return error_widget
        except Exception as e:
            print(f"خطأ في إنشاء widget الخطأ: {str(e)}")
            return None

    def _draw_invoice_preview(self, painter, invoice_data, items_data, company_info, width, height):
        """رسم معاينة الفاتورة باستخدام نفس منطق الطباعة"""
        try:
            print("بدء رسم معاينة الفاتورة...")

            # التحقق من صحة المعاملات
            if not painter or not invoice_data or not items_data or not company_info:
                print("معاملات غير صحيحة للرسم")
                return

            # إعداد الخطوط (مطابقة للطباعة) - أحجام مصغرة قليلاً
            try:
                company_font = QFont("Tahoma", 11, QFont.Bold)      # من 12 إلى 11
                info_font = QFont("Tahoma", 8)                      # من 9 إلى 8
                invoice_info_font = QFont("Tahoma", 8)              # من 9 إلى 8
                table_header_font = QFont("Tahoma", 8)  # إزالة Bold
                table_data_font = QFont("Tahoma", 7)                # من 8 إلى 7
                total_font = QFont("Tahoma", 9, QFont.Bold)         # من 10 إلى 9
                notes_font = QFont("Tahoma", 8)                     # من 9 إلى 8
                print("تم إعداد الخطوط")
            except Exception as font_error:
                print(f"خطأ في إعداد الخطوط: {str(font_error)}")
                return

            # إعداد القلم
            try:
                pen = QPen(Qt.GlobalColor.black, 1)
                painter.setPen(pen)
                print("تم إعداد القلم")
            except Exception as pen_error:
                print(f"خطأ في إعداد القلم: {str(pen_error)}")
                return

            # الهوامش والأبعاد (مطابقة للطباعة)
            # تعديل الهوامش بناءً على حجم الورقة لضمان التوسيط المثالي (مطابق للطباعة)
            from utils.settings_manager import SettingsManager
            settings_manager = SettingsManager()
            paper_width = settings_manager.get_paper_width_safe()

            if paper_width == 80:
                # هوامش للتوسيط المثالي - تحريك قوي جداً جهة اليمين (مطابق للطباعة)
                total_margin = 60  # إجمالي الهوامش
                margin_left = 10   # هامش أيسر صغير جداً لتحريك الفاتورة يميناً
                margin_right = 50  # هامش أيمن كبير جداً لتحريك الفاتورة يميناً
                margin = margin_left  # استخدام الهامش الأيسر للحسابات
                content_width = width - total_margin
            else:
                # الهوامش العادية للمقاس الصغير
                margin_left = 30
                margin_right = 30
                margin = margin_left  # للتوافق مع الكود القديم
                content_width = width - (margin_left + margin_right)
            y_pos = 40

            print(f"الهوامش: margin={margin}, content_width={content_width}")

            # استخدام نفس النظام الجديد المستخدم في الطباعة
            try:
                # إنشاء مدير التخطيط التكيفي للمعاينة
                layout_manager = AdaptiveLayoutManager(paper_width, content_width)
                
                # رسم معلومات الشركة باستخدام النظام الجديد
                y_pos = layout_manager.layout_company_section(painter, company_info, y_pos)
                print("تم رسم معلومات الشركة باستخدام النظام الجديد")
                
            except Exception as layout_error:
                print(f"خطأ في استخدام النظام الجديد للمعاينة، العودة للنظام القديم: {str(layout_error)}")
                
                # النظام الاحتياطي القديم في حالة الخطأ
                try:
                    painter.setFont(company_font)
                    fm = QFontMetrics(company_font)
                    text_height = fm.height() + 10

                    # اسم الشركة
                    company_name = company_info.get('name', company_info.get('company_name', ''))
                    if company_name and company_name.strip():
                        painter.drawText(QRect(margin_left, y_pos, content_width, text_height),
                                       Qt.AlignmentFlag.AlignCenter, str(company_name))
                    else:
                        painter.drawText(QRect(margin_left, y_pos, content_width, text_height),
                                       Qt.AlignmentFlag.AlignCenter, "[يرجى إدخال اسم الشركة]")
                    y_pos += text_height + 15

                    # رقم الهاتف
                    painter.setFont(info_font)
                    fm = QFontMetrics(info_font)
                    text_height = fm.height() + 8
                    company_phone = company_info.get('phone', company_info.get('company_phone', ''))
                    if company_phone and company_phone.strip():
                        painter.drawText(QRect(margin_left, y_pos, content_width, text_height),
                                       Qt.AlignmentFlag.AlignCenter, f"هاتف: {company_phone}")
                    else:
                        text_height = 0  # لا نعرض شيئاً إذا لم يكن هناك رقم هاتف
                    y_pos += text_height + 12

                    # العنوان
                    company_address = company_info.get('address', company_info.get('company_address', ''))
                    if company_address and company_address.strip():
                        painter.drawText(QRect(margin_left, y_pos, content_width, text_height),
                                       Qt.AlignmentFlag.AlignCenter, str(company_address))
                    else:
                        text_height = 0  # لا نعرض شيئاً إذا لم يكن هناك عنوان
                    y_pos += text_height + 25

                    # خط فاصل
                    painter.drawLine(margin_left, y_pos, width - margin_right, y_pos)
                    y_pos += 30

                    print("تم استخدام النظام الاحتياطي القديم للشركة")

                except Exception as fallback_error:
                    print(f"خطأ في النظام الاحتياطي: {str(fallback_error)}")
                    return

            # رسم معلومات الفاتورة باستخدام النظام الجديد
            try:
                # إنشاء مدير التخطيط إذا لم يكن موجوداً (في حالة فشل الجزء السابق)
                if 'layout_manager' not in locals():
                    layout_manager = AdaptiveLayoutManager(paper_width, content_width)
                
                # رسم معلومات الفاتورة باستخدام النظام الجديد
                invoice_data_formatted = {
                    'invoice_number': invoice_data.get('reference_number', 'INV-001'),
                    'date': invoice_data.get('date', ''),
                    'customer_name': invoice_data.get('customer_name', 'عميل نقدي')
                }
                y_pos = layout_manager.layout_invoice_section(painter, invoice_data_formatted, y_pos)
                print("تم رسم معلومات الفاتورة باستخدام النظام الجديد")
                
            except Exception as invoice_layout_error:
                print(f"خطأ في استخدام النظام الجديد لمعلومات الفاتورة، العودة للنظام القديم: {str(invoice_layout_error)}")
                
                # النظام الاحتياطي القديم لمعلومات الفاتورة
                try:
                    painter.setFont(invoice_info_font)
                    fm = QFontMetrics(invoice_info_font)
                    text_height = fm.height() + fm.descent() + 12

                    # رقم الفاتورة
                    invoice_number = invoice_data.get('reference_number', invoice_data.get('invoice_number', ''))
                    if invoice_number and invoice_number.strip():
                        painter.drawText(QRect(margin_left, y_pos, content_width, text_height),
                                       Qt.AlignmentFlag.AlignCenter, f"رقم الفاتورة: {invoice_number}")
                    else:
                        painter.drawText(QRect(margin_left, y_pos, content_width, text_height),
                                       Qt.AlignmentFlag.AlignCenter, "[رقم الفاتورة غير محدد]")
                    y_pos += text_height + 22

                    # التاريخ
                    invoice_date = str(invoice_data.get('date', ''))
                    painter.drawText(QRect(margin_left, y_pos, content_width, text_height),
                                   Qt.AlignmentFlag.AlignCenter, f"{invoice_date}")
                    y_pos += text_height + 22

                    # العميل
                    customer_name = str(invoice_data.get('customer_name', 'عميل نقدي'))
                    painter.drawText(QRect(margin_left, y_pos, content_width, text_height),
                                   Qt.AlignmentFlag.AlignCenter, f"العميل: {customer_name}")
                    y_pos += text_height + 32

                    print("تم استخدام النظام الاحتياطي القديم لمعلومات الفاتورة")

                except Exception as invoice_fallback_error:
                    print(f"خطأ في النظام الاحتياطي لمعلومات الفاتورة: {str(invoice_fallback_error)}")
                    return

            # رسم جدول المنتجات (مبسط للمعاينة)
            try:
                # قراءة حجم الورقة لتعديل حجم الجدول في المعاينة
                from utils.settings_manager import SettingsManager
                settings_manager = SettingsManager()
                paper_width = settings_manager.get_paper_width_safe()

                # تعديل حجم الجدول بناءً على حجم الورقة (مطابق للطباعة المحسنة)
                if paper_width == 80:
                    # حجم محسن للمقاس الكبير (80مم) - توزيع أفضل للأعمدة
                    col1_width = int(content_width * 0.42)  # المنتج - زيادة أكثر
                    col2_width = int(content_width * 0.19)  # السعر
                    col3_width = int(content_width * 0.16)  # الكمية
                    col4_width = content_width - col1_width - col2_width - col3_width  # الإجمالي
                    row_height = 28  # زيادة الارتفاع للمعاينة أيضاً
                    cell_padding = 5  # حشو محسن للمعاينة
                elif paper_width == 210:  # A4 paper
                    # حجم محسن لورقة A4
                    col1_width = int(content_width * 0.40)  # المنتج
                    col2_width = int(content_width * 0.20)  # السعر
                    col3_width = int(content_width * 0.15)  # الكمية
                    col4_width = content_width - col1_width - col2_width - col3_width  # الإجمالي
                    row_height = 30  # ارتفاع محسن للمعاينة
                    cell_padding = 6  # حشو محسن
                else:
                    # الحجم العادي للمقاس الصغير (58مم)
                    col1_width = int(content_width * 0.45)  # المنتج
                    col2_width = int(content_width * 0.18)  # السعر
                    col3_width = int(content_width * 0.15)  # الكمية
                    col4_width = content_width - col1_width - col2_width - col3_width  # الإجمالي
                    row_height = 26  # ارتفاع محسن للمعاينة
                    cell_padding = 4  # حشو محسن

                # رسم رأس الجدول
                painter.setFont(table_header_font)
                header_rect = QRect(margin_left, y_pos, content_width, row_height)
                painter.fillRect(header_rect, QBrush(Qt.GlobalColor.lightGray))
                painter.drawRect(header_rect)

                # عناوين الأعمدة
                x_offset = margin_left + content_width - col1_width
                painter.drawText(QRect(x_offset + cell_padding, y_pos + cell_padding,
                                     col1_width - (cell_padding * 2), row_height - (cell_padding * 2)),
                               Qt.AlignmentFlag.AlignCenter, "المنتج")

                x_offset -= col2_width
                painter.drawText(QRect(x_offset + cell_padding, y_pos + cell_padding,
                                     col2_width - (cell_padding * 2), row_height - (cell_padding * 2)),
                               Qt.AlignmentFlag.AlignCenter, "السعر")

                x_offset -= col3_width
                painter.drawText(QRect(x_offset + cell_padding, y_pos + cell_padding,
                                     col3_width - (cell_padding * 2), row_height - (cell_padding * 2)),
                               Qt.AlignmentFlag.AlignCenter, "الكمية")

                x_offset -= col4_width
                painter.drawText(QRect(x_offset + cell_padding, y_pos + cell_padding,
                                     col4_width - (cell_padding * 2), row_height - (cell_padding * 2)),
                               Qt.AlignmentFlag.AlignCenter, "الإجمالي")

                # رسم خطوط فاصلة عمودية في رأس الجدول
                x_offset = margin_left + content_width - col1_width
                painter.drawLine(x_offset, y_pos, x_offset, y_pos + row_height)
                x_offset -= col2_width
                painter.drawLine(x_offset, y_pos, x_offset, y_pos + row_height)
                x_offset -= col3_width
                painter.drawLine(x_offset, y_pos, x_offset, y_pos + row_height)

                # رسم الحد الخارجي الأيمن لرأس الجدول (الحد المفقود)
                right_border_x = width - margin_right
                painter.drawLine(right_border_x, y_pos, right_border_x, y_pos + row_height)

                y_pos += row_height

                # رسم صفوف البيانات
                painter.setFont(table_data_font)
                for item in items_data:
                    row_rect = QRect(margin_left, y_pos, content_width, row_height)
                    painter.drawRect(row_rect)

                    # اسم المنتج
                    x_offset = margin_left + content_width - col1_width
                    product_name = item.get('product_name', '')
                    if len(product_name) > 25:
                        product_name = product_name[:22] + "..."
                    painter.drawText(QRect(x_offset + cell_padding, y_pos + cell_padding,
                                         col1_width - (cell_padding * 2), row_height - (cell_padding * 2)),
                                   Qt.AlignmentFlag.AlignCenter, product_name)

                    # السعر
                    x_offset -= col2_width
                    painter.drawText(QRect(x_offset + cell_padding, y_pos + cell_padding,
                                         col2_width - (cell_padding * 2), row_height - (cell_padding * 2)),
                                   Qt.AlignmentFlag.AlignCenter, f"{item.get('unit_price', 0):.2f}")

                    # الكمية
                    x_offset -= col3_width
                    painter.drawText(QRect(x_offset + cell_padding, y_pos + cell_padding,
                                         col3_width - (cell_padding * 2), row_height - (cell_padding * 2)),
                                   Qt.AlignmentFlag.AlignCenter, str(item.get('quantity', 0)))

                    # الإجمالي
                    x_offset -= col4_width
                    painter.drawText(QRect(x_offset + cell_padding, y_pos + cell_padding,
                                         col4_width - (cell_padding * 2), row_height - (cell_padding * 2)),
                                   Qt.AlignmentFlag.AlignCenter, f"{item.get('total_price', 0):.2f}")

                    # رسم خطوط فاصلة عمودية لصفوف البيانات
                    x_offset = margin_left + content_width - col1_width
                    painter.drawLine(x_offset, y_pos, x_offset, y_pos + row_height)
                    x_offset -= col2_width
                    painter.drawLine(x_offset, y_pos, x_offset, y_pos + row_height)
                    x_offset -= col3_width
                    painter.drawLine(x_offset, y_pos, x_offset, y_pos + row_height)

                    # رسم الحد الخارجي الأيمن لعمود المنتج (الحد المفقود)
                    right_border_x = width - margin_right
                    painter.drawLine(right_border_x, y_pos, right_border_x, y_pos + row_height)

                    y_pos += row_height

                y_pos += 20

                print("تم رسم جدول المنتجات")

            except Exception as table_error:
                print(f"خطأ في رسم الجدول: {str(table_error)}")
                return

            # مربع الإجمالي مع تحسين الأبعاد (مطابق للطباعة)
            try:
                painter.setFont(total_font)
                
                # حساب أبعاد محسنة حسب حجم الورق (مطابق للطباعة)
                if paper_width == 80:
                    total_box_width = int(content_width * 0.85)  # عرض أكبر
                    total_box_height = 45  # ارتفاع محسن للمعاينة
                    padding = 12  # مسافة داخلية
                elif paper_width == 210:  # A4
                    total_box_width = int(content_width * 0.75)
                    total_box_height = 70  # زيادة ارتفاع المعاينة أيضاً
                    padding = 15
                else:  # 58mm
                    total_box_width = int(content_width * 0.90)  # عرض أكبر للورق الصغير
                    total_box_height = 40
                    padding = 10
                
                # توسيط المربع مع ضبط الهوامش
                total_box_x = margin_left + (content_width - total_box_width) // 2
                
                # التأكد من أن المربع لا يتجاوز حدود الصفحة
                if total_box_x < margin_left:
                    total_box_x = margin_left
                    total_box_width = content_width - 20  # ترك هامش صغير

                total_rect = QRect(total_box_x, y_pos, total_box_width, total_box_height)
                painter.drawRect(total_rect)
                
                # رسم النص مع مسافة داخلية محسنة
                text_rect = QRect(total_box_x + padding, y_pos + padding, 
                                total_box_width - (padding * 2), total_box_height - (padding * 2))
                painter.drawText(text_rect, Qt.AlignmentFlag.AlignCenter,
                               f"الإجمالي {invoice_data.get('total', 0):.2f} ج.م")

                y_pos += total_box_height + 20

                print("تم رسم مربع الإجمالي")

            except Exception as total_error:
                print(f"خطأ في رسم الإجمالي: {str(total_error)}")
                return

            # الملاحظات
            try:
                painter.setFont(notes_font)
                fm = QFontMetrics(notes_font)
                text_height = fm.height() + 8
                notes_rect = QRect(margin_left, y_pos, content_width, text_height + 15)
                painter.drawText(notes_rect, Qt.AlignmentFlag.AlignCenter,
                               company_info.get('notes', 'شكراً لتعاملكم معنا'))

                print("تم رسم الملاحظات")

            except Exception as notes_error:
                print(f"خطأ في رسم الملاحظات: {str(notes_error)}")
                return

            print("تم رسم المعاينة بنجاح")

        except Exception as e:
            print(f"خطأ عام في رسم المعاينة: {str(e)}")
            import traceback
            traceback.print_exc()

    def create_invoice_data(self, invoice_data, items_data, company_info):
        """إنشاء بيانات الفاتورة بصيغة ESC/POS"""
        try:
            # بناء محتوى الفاتورة
            content = []

            # رأس الفاتورة
            content.append(b'\x1B\x40')  # تهيئة الطابعة
            content.append(b'\x1B\x61\x01')  # توسيط النص

            # معلومات الشركة
            company_name = company_info.get('name', company_info.get('company_name', ''))
            company_phone = company_info.get('phone', company_info.get('company_phone', ''))
            company_address = company_info.get('address', company_info.get('company_address', ''))
            
            if company_name and company_name.strip():
                content.append(f"{company_name}\n".encode('utf-8'))
            else:
                content.append("[يرجى إدخال اسم الشركة في الإعدادات]\n".encode('utf-8'))
                
            if company_phone and company_phone.strip():
                content.append(f"هاتف: {company_phone}\n".encode('utf-8'))
                
            if company_address and company_address.strip():
                content.append(f"{company_address}\n".encode('utf-8'))
            content.append(b'================================\n')

            # معلومات الفاتورة
            invoice_number = invoice_data.get('reference_number', invoice_data.get('invoice_number', ''))
            invoice_date = invoice_data.get('date', invoice_data.get('invoice_date', ''))
            customer_name = invoice_data.get('customer_name', '')
            
            if invoice_number and invoice_number.strip():
                content.append(f"رقم الفاتورة: {invoice_number}\n".encode('utf-8'))
            else:
                content.append("[رقم الفاتورة غير محدد]\n".encode('utf-8'))
                
            if invoice_date and invoice_date.strip():
                content.append(f"التاريخ: {invoice_date}\n".encode('utf-8'))
                
            if customer_name and customer_name.strip():
                content.append(f"العميل: {customer_name}\n".encode('utf-8'))
            else:
                content.append("العميل: عميل نقدي\n".encode('utf-8'))
            content.append(b'================================\n')

            # جدول المنتجات
            for item in items_data:
                product_name = item.get('product_name', '')
                if len(product_name) > 25:
                    product_name = product_name[:22] + "..."

                content.append(f"{product_name}\n".encode('utf-8'))
                content.append(f"الكمية: {item.get('quantity', 0)} × {item.get('unit_price', 0):.2f} = {item.get('total_price', 0):.2f}\n".encode('utf-8'))
                content.append(b'--------------------------------\n')

            # الإجمالي
            content.append(b'================================\n')
            content.append(f"الإجمالي {invoice_data.get('total', 0):.2f} ج.م\n".encode('utf-8'))
            content.append(b'================================\n')

            # الملاحظات
            content.append(f"{company_info.get('notes', 'شكراً لتعاملكم معنا')}\n".encode('utf-8'))

            # قطع الورق
            content.append(b'\x0A\x0A\x0A')
            content.append(b'\x1D\x56\x41\x10')

            return b''.join(content)

        except Exception as e:
            print(f"خطأ في إنشاء بيانات الفاتورة: {str(e)}")
            return None

    def extract_invoice_data(self, invoice_data):
        """استخراج بيانات الفاتورة مع التوافق بين التنسيقات المختلفة"""
        try:
            # استخراج رقم الفاتورة مع دعم التنسيقات المختلفة
            invoice_number = invoice_data.get('reference_number', invoice_data.get('invoice_number', ''))
            
            # استخراج التاريخ مع دعم التنسيقات المختلفة
            invoice_date = invoice_data.get('date', invoice_data.get('invoice_date', ''))
            
            # استخراج اسم العميل
            customer_name = invoice_data.get('customer_name', '')
            
            return {
                'invoice_number': invoice_number.strip() if invoice_number else '',
                'date': invoice_date.strip() if invoice_date else '',
                'customer_name': customer_name.strip() if customer_name else ''
            }
            
        except Exception as e:
            print(f"خطأ في استخراج بيانات الفاتورة: {str(e)}")
            return {
                'invoice_number': '',
                'date': '',
                'customer_name': ''
            }

    def validate_print_data(self, invoice_data, company_info):
        """التحقق من صحة البيانات قبل الطباعة"""
        try:
            errors = []
            warnings = []
            
            # التحقق من بيانات الشركة
            company_name = company_info.get('name', company_info.get('company_name', ''))
            if not company_name or not company_name.strip():
                errors.append("اسم الشركة مطلوب - يرجى إدخاله في الإعدادات")
            
            company_phone = company_info.get('phone', company_info.get('company_phone', ''))
            if not company_phone or not company_phone.strip():
                warnings.append("رقم الهاتف غير محدد - يُنصح بإدخاله في الإعدادات")
            
            company_address = company_info.get('address', company_info.get('company_address', ''))
            if not company_address or not company_address.strip():
                warnings.append("عنوان الشركة غير محدد - يُنصح بإدخاله في الإعدادات")
            
            # التحقق من بيانات الفاتورة باستخدام الدالة المساعدة
            extracted_data = self.extract_invoice_data(invoice_data)
            
            if not extracted_data['invoice_number']:
                errors.append("رقم الفاتورة مطلوب")
            
            if not extracted_data['date']:
                warnings.append("تاريخ الفاتورة غير محدد")
            
            if not extracted_data['customer_name']:
                warnings.append("اسم العميل غير محدد")
            
            # طباعة النتائج
            if errors:
                print("❌ أخطاء في البيانات:")
                for error in errors:
                    print(f"  - {error}")
            
            if warnings:
                print("⚠️ تحذيرات:")
                for warning in warnings:
                    print(f"  - {warning}")
            
            return len(errors) == 0, errors, warnings
            
        except Exception as e:
            print(f"خطأ في التحقق من صحة البيانات: {str(e)}")
            return False, [f"خطأ في التحقق: {str(e)}"], []

    def get_real_company_settings(self, settings=None):
        """قراءة معلومات الشركة الحقيقية من الإعدادات باستخدام SettingsManager الموحد"""
        try:
            from utils.settings_manager import SettingsManager
            
            # استخدام SettingsManager الموحد
            settings_manager = SettingsManager()
            company_info = settings_manager.get_real_company_settings()
            
            # إزالة metadata للتوافق مع الكود الحالي
            if '_metadata' in company_info:
                metadata = company_info.pop('_metadata')
                
                # طباعة تحذيرات إذا كانت البيانات غير مكتملة
                if not metadata.get('has_real_data', False):
                    print("⚠️ تحذير: يتم استخدام بيانات شركة غير مكتملة في الطباعة")
                    if metadata.get('empty_fields'):
                        print(f"الحقول الفارغة: {', '.join(metadata['empty_fields'])}")
                    if metadata.get('default_fields'):
                        print(f"الحقول الافتراضية: {', '.join(metadata['default_fields'])}")
            
            # التأكد من وجود قيم للحقول الفارغة (للتوافق مع الكود الحالي)
            if not company_info.get('name'):
                company_info['name'] = ""
            if not company_info.get('phone'):
                company_info['phone'] = ""
            if not company_info.get('address'):
                company_info['address'] = ""
            if not company_info.get('notes'):
                company_info['notes'] = "شكراً لتعاملكم معنا"
            
            return company_info
            
        except Exception as e:
            print(f"خطأ في قراءة إعدادات الشركة: {str(e)}")
            # إرجاع بيانات فارغة بدلاً من افتراضية
            return {
                'name': "",
                'phone': "",
                'address': "",
                'notes': "شكراً لتعاملكم معنا"
            }


class BarcodeMultiPrinter:
    """مدير طباعة الباركود البسيط"""

    def __init__(self):
        pass

    def print_barcode_best_method(self, barcode_text, printer_name=None, show_barcode_text=True, show_price=False, price_text=""):
        """طباعة الباركود بطريقة واحدة مباشرة وموثوقة
        show_barcode_text: عرض رقم الباركود أسفل الباركود
        show_price: عرض السعر أسفل رقم الباركود
        """
        print(f"بدء طباعة الباركود: {barcode_text}")
        print(f"الطابعة المحددة: {printer_name}")

        try:
            # إنشاء صورة الباركود بدقة فائقة محسنة ومضاعفة - زيادة الارتفاع
            barcode_pixmap = BarcodeHelper.generate_barcode_pixmap(barcode_text, 3600, 1440, show_barcode_text, show_price, price_text)
            if not barcode_pixmap:
                print("فشل في إنشاء صورة الباركود")
                return False, "فشل في إنشاء صورة الباركود"

            # إنشاء طابعة بأعلى دقة ممكنة
            printer = QPrinter(QPrinter.HighResolution)
            printer.setOutputFormat(QPrinter.NativeFormat)

            # تعيين حجم الورق المناسب للباركود - حجم استيكر واحد فقط
            printer.setPageSize(QPrinter.Custom)
            printer.setPaperSize(QSizeF(40, 25), QPrinter.Millimeter)  # حجم استيكر واحد فقط

            # هوامش صغيرة جداً لتجنب طباعة عدة استيكرات
            printer.setPageMargins(0.1, 0.1, 0.1, 0.1, QPrinter.Millimeter)  # هوامش أصغر

            # إعدادات فائقة الجودة للطباعة الاحترافية
            printer.setResolution(2400)  # دقة فائقة للباركود (2400 DPI)
            printer.setColorMode(QPrinter.GrayScale)  # طباعة بالأبيض والأسود
            printer.setPageOrder(QPrinter.FirstPageFirst)
            printer.setOrientation(QPrinter.Portrait)

            # إعدادات إضافية لطابعات الاستيكرات
            printer.setDuplex(QPrinter.DuplexNone)  # طباعة على وجه واحد فقط
            printer.setCopyCount(1)  # نسخة واحدة فقط
            printer.setFromTo(1, 1)  # طباعة صفحة واحدة فقط
            printer.setPageOrder(QPrinter.FirstPageFirst)  # ترتيب الصفحات
            printer.setCollateCopies(False)  # عدم تجميع النسخ

            # إعدادات جودة إضافية احترافية
            printer.setFullPage(True)  # استخدام كامل الصفحة بدون هوامش إضافية
            printer.setOutputFormat(QPrinter.NativeFormat)  # أفضل جودة للطابعة المحلية

            # إعدادات إضافية لضمان طباعة استيكر واحد فقط
            try:
                printer.setNumCopies(1)  # عدد النسخ = 1
                printer.setPrintRange(QPrinter.AllPages)  # طباعة جميع الصفحات (صفحة واحدة)
            except:
                pass  # تجاهل الأخطاء إذا لم تكن مدعومة

            # إعدادات متقدمة للجودة الفائقة
            try:
                # محاولة تطبيق إعدادات متقدمة إذا كانت مدعومة
                printer.setPageSize(QPrinter.Custom)  # التأكد من الحجم المخصص
                printer.setCreator("SmartManager Barcode Printer")  # تحديد المصدر
                printer.setDocName(f"Barcode_{barcode_text}")  # اسم المستند
            except:
                pass  # تجاهل الأخطاء إذا لم تكن الإعدادات مدعومة

            # تعيين الطابعة المحددة
            if printer_name and printer_name != "الطابعة الافتراضية":
                printer.setPrinterName(printer_name)
                print(f"تم تعيين الطابعة: {printer_name}")

            # بدء الطباعة مع إعدادات جودة عالية
            painter = QPainter()
            if not painter.begin(printer):
                print("فشل في بدء الطباعة")
                return False, "فشل في بدء الطباعة"

            try:
                # إعدادات الرسم للحصول على أفضل جودة بدون تعرج
                painter.setRenderHint(QPainter.Antialiasing, False)  # إيقاف التنعيم للخطوط الحادة
                painter.setRenderHint(QPainter.TextAntialiasing, False)  # إيقاف تنعيم النص
                painter.setRenderHint(QPainter.SmoothPixmapTransform, False)  # منع التنعيم في تحويل الصور
                # الحصول على أبعاد الصفحة
                page_rect = printer.pageRect()
                page_width = page_rect.width()
                page_height = page_rect.height()

                print(f"أبعاد الصفحة: {page_width} x {page_height}")

                # حساب حجم الباركود (90% من عرض الصفحة)
                barcode_width = int(page_width * 0.90)
                barcode_height = int(barcode_width * 0.40)  # زيادة النسبة من 30% إلى 40% لباركود أطول وأوضح

                # وضع الباركود في وسط الصفحة ثم رفعه قليلاً لإفساح مكان للسعر
                x = (page_width - barcode_width) // 2  # توسيط أفقي
                y = (page_height - barcode_height) // 2
                # رفع الباركود لأعلى بمقدار 2 ملم لإتاحة مساحة للسعر أسفل الرقم
                try:
                    dpi_y = painter.device().logicalDpiY()
                except Exception:
                    dpi_y = 96
                two_mm = max(2, int(round((dpi_y / 25.4) * 2)))
                y = max(0, y - two_mm)

                # التأكد من أن الباركود لا يتجاوز حدود الصفحة
                if x + barcode_width > page_width:
                    barcode_width = page_width - x - 5
                if y < 0:  # إذا كان الباركود يتجاوز الحد العلوي
                    y = 10  # ضعه بهامش صغير من الأعلى
                if y + barcode_height > page_height:
                    y = page_height - barcode_height - 10  # ضعه في الأسفل مع هامش

                print(f"موضع الباركود: x={x}, y={y}, width={barcode_width}, height={barcode_height}")

                # رسم الباركود بأعلى جودة ممكنة
                # استخدام تحويل سلس للحفاظ على جودة الصورة
                scaled_pixmap = barcode_pixmap.scaled(
                    barcode_width, barcode_height,
                    Qt.AspectRatioMode.KeepAspectRatio,
                    Qt.TransformationMode.SmoothTransformation  # تحويل سلس للحفاظ على الجودة
                )
                painter.drawPixmap(x, y, scaled_pixmap)

                # السعر مدمج داخل الصورة مسبقاً

                print("تم رسم الباركود بنجاح")
                return True, "تم طباعة الباركود بنجاح"

            finally:
                painter.end()

        except Exception as e:
            print(f"خطأ في طباعة الباركود: {str(e)}")
            return False, f"خطأ في الطباعة: {str(e)}"


class SpacingCalculator:
    """حاسبة المسافات الديناميكية للفواتير"""
    
    def __init__(self, paper_width: int):
        """
        تهيئة حاسبة المسافات مع تحقق شامل من صحة المدخلات
        
        Args:
            paper_width: عرض الورق بالمليمتر (58، 80، 210)
            
        Raises:
            ValueError: إذا كان حجم الورق غير صحيح
            TypeError: إذا كان نوع المدخل غير صحيح
        """
        try:
            # التحقق من نوع المدخل
            if not isinstance(paper_width, (int, float)):
                raise TypeError(f"حجم الورق يجب أن يكون رقماً، تم استلام: {type(paper_width)}")
            
            # تحويل إلى int إذا كان float
            paper_width = int(paper_width)
            
            # التحقق من صحة حجم الورق
            if paper_width <= 0:
                raise ValueError(f"حجم الورق يجب أن يكون موجباً، تم استلام: {paper_width}")
            
            if paper_width > 1000:  # حد أقصى معقول
                print(f"تحذير: حجم الورق كبير جداً ({paper_width}مم)، قد يؤثر على الأداء")
            
            if paper_width < 30:  # حد أدنى معقول
                print(f"تحذير: حجم الورق صغير جداً ({paper_width}مم)، قد يؤثر على جودة التخطيط")
            
            self.paper_width = paper_width
            self.base_spacing_multiplier = self._get_base_multiplier()
            
            # معاملات التصحيح لأحجام الورق المختلفة
            self.paper_correction_factors = {
                58: {
                    'spacing_multiplier': 0.8,  # مسافات أصغر للورق الضيق
                    'min_spacing': 8,
                    'max_spacing': 40
                },
                80: {
                    'spacing_multiplier': 1.0,  # مسافات عادية
                    'min_spacing': 12,
                    'max_spacing': 50
                },
                210: {  # A4
                    'spacing_multiplier': 2.5,  # مسافات أكبر بشكل ملحوظ للورق الكبير - محسن
                    'min_spacing': 35,  # زيادة المسافة الدنيا
                    'max_spacing': 100  # زيادة المسافة العليا
                }
            }
            
            # الحصول على معاملات التصحيح للورق الحالي
            self.correction_factor = self._get_correction_factor()
            
            # التحقق من صحة معاملات التصحيح
            if not self.correction_factor or not isinstance(self.correction_factor, dict):
                raise ValueError("فشل في الحصول على معاملات التصحيح الصحيحة")
            
            # تسجيل نجاح التهيئة
            print(f"تم تهيئة حاسبة المسافات بنجاح لورق {paper_width}مم")
            
        except (ValueError, TypeError) as e:
            print(f"خطأ في تهيئة حاسبة المسافات: {str(e)}")
            # استخدام قيم افتراضية آمنة
            self.paper_width = 80  # قيمة افتراضية آمنة
            self.base_spacing_multiplier = 1.0
            self.paper_correction_factors = {
                80: {
                    'spacing_multiplier': 1.0,
                    'min_spacing': 12,
                    'max_spacing': 50
                }
            }
            self.correction_factor = self.paper_correction_factors[80]
            print("تم استخدام قيم افتراضية آمنة (80مم)")
            
        except Exception as e:
            print(f"خطأ غير متوقع في تهيئة حاسبة المسافات: {str(e)}")
            # قيم طوارئ
            self.paper_width = 80
            self.base_spacing_multiplier = 1.0
            self.correction_factor = {
                'spacing_multiplier': 1.0,
                'min_spacing': 10,
                'max_spacing': 50
            }
    
    def _get_base_multiplier(self) -> float:
        """حساب معامل المسافة الأساسي بناءً على حجم الورق"""
        if self.paper_width <= 58:
            return 0.8
        elif self.paper_width <= 80:
            return 1.0
        else:  # A4 وأحجام أكبر
            return 1.2
    
    def _get_correction_factor(self) -> dict:
        """الحصول على معامل التصحيح للورق الحالي"""
        # البحث عن أقرب حجم ورق مدعوم
        if self.paper_width <= 58:
            return self.paper_correction_factors[58]
        elif self.paper_width <= 80:
            return self.paper_correction_factors[80]
        else:
            return self.paper_correction_factors[210]
    
    def calculate_text_spacing(self, font: QFont) -> dict:
        """
        حساب المسافات للنص بناءً على الخط وحجم الورق مع معالجة شاملة للأخطاء
        
        Args:
            font: الخط المستخدم
            
        Returns:
            dict: قاموس يحتوي على مقاييس المسافات
            
        Raises:
            TypeError: إذا لم يكن الخط من نوع QFont
        """
        # التحقق من صحة المدخلات
        if font is None:
            print("تحذير: تم تمرير خط فارغ (None)، استخدام خط افتراضي")
            font = QFont("Arial", 10)
        
        if not isinstance(font, QFont):
            print(f"خطأ: نوع الخط غير صحيح {type(font)}، استخدام خط افتراضي")
            font = QFont("Arial", 10)
        
        try:
            # التحقق من صحة معاملات التصحيح
            if not hasattr(self, 'correction_factor') or not self.correction_factor:
                print("تحذير: معاملات التصحيح غير متوفرة، استخدام قيم افتراضية")
                self.correction_factor = {
                    'spacing_multiplier': 1.0,
                    'min_spacing': 10,
                    'max_spacing': 50
                }
            
            # حساب مقاييس الخط باستخدام QFontMetrics
            try:
                fm = QFontMetrics(font)
            except Exception as fm_error:
                print(f"خطأ في إنشاء QFontMetrics: {str(fm_error)}")
                # استخدام خط بديل
                fallback_font = QFont()
                fallback_font.setPointSize(10)
                fm = QFontMetrics(fallback_font)
            
            # حساب الارتفاع الفعلي للنص مع descent
            try:
                actual_height = fm.height()
                ascent = fm.ascent()
                descent = fm.descent()
                leading = fm.leading()
                
                # التحقق من صحة القيم
                if actual_height <= 0:
                    print("تحذير: ارتفاع الخط غير صحيح، استخدام قيمة افتراضية")
                    actual_height = 16
                    ascent = 12
                    descent = 4
                    leading = 2
                    
            except Exception as metrics_error:
                print(f"خطأ في حساب مقاييس الخط: {str(metrics_error)}")
                actual_height = 16
                ascent = 12
                descent = 4
                leading = 2
            
            # حساب المسافات بناءً على حجم الخط ونوع الورق
            try:
                font_size = font.pointSize()
                if font_size <= 0:  # في حالة عدم تحديد حجم النقطة
                    pixel_size = font.pixelSize()
                    if pixel_size > 0:
                        font_size = pixel_size / 1.33  # تحويل تقريبي من البكسل للنقطة
                    else:
                        font_size = 10  # قيمة افتراضية آمنة
                        print("تحذير: لم يتم العثور على حجم خط صحيح، استخدام 10 نقطة")
                
                # التحقق من معقولية حجم الخط
                if font_size > 72:
                    print(f"تحذير: حجم الخط كبير جداً ({font_size})، تقليله إلى 72")
                    font_size = 72
                elif font_size < 6:
                    print(f"تحذير: حجم الخط صغير جداً ({font_size})، زيادته إلى 6")
                    font_size = 6
                    
            except Exception as size_error:
                print(f"خطأ في حساب حجم الخط: {str(size_error)}")
                font_size = 10
            
            # حساب المسافات الأساسية مع حماية من القسمة على صفر
            try:
                spacing_multiplier = self.correction_factor.get('spacing_multiplier', 1.0)
                if spacing_multiplier <= 0:
                    spacing_multiplier = 1.0
                
                base_top_padding = int(font_size * 0.4 * spacing_multiplier)
                base_bottom_padding = int(font_size * 0.3 * spacing_multiplier)
                base_line_spacing = int(font_size * 0.2 * spacing_multiplier)
                
            except Exception as calc_error:
                print(f"خطأ في حساب المسافات الأساسية: {str(calc_error)}")
                base_top_padding = 8
                base_bottom_padding = 6
                base_line_spacing = 4
            
            # تطبيق الحدود الدنيا والعليا مع حماية من القيم غير الصحيحة
            try:
                min_spacing = self.correction_factor.get('min_spacing', 8)
                max_spacing = self.correction_factor.get('max_spacing', 50)
                
                # التحقق من صحة الحدود
                if min_spacing <= 0:
                    min_spacing = 8
                if max_spacing <= min_spacing:
                    max_spacing = min_spacing + 20
                
                top_padding = max(min_spacing // 2, min(base_top_padding, max_spacing // 2))
                bottom_padding = max(min_spacing // 3, min(base_bottom_padding, max_spacing // 3))
                line_spacing = max(4, min(base_line_spacing, max_spacing // 4))
                
            except Exception as bounds_error:
                print(f"خطأ في تطبيق حدود المسافات: {str(bounds_error)}")
                top_padding = 8
                bottom_padding = 6
                line_spacing = 4
            
            # إنشاء النتيجة النهائية مع التحقق من صحة جميع القيم
            result = {
                'height': max(1, actual_height),
                'ascent': max(1, ascent),
                'descent': max(0, descent),
                'leading': max(0, leading),
                'top_padding': max(0, top_padding),
                'bottom_padding': max(0, bottom_padding),
                'line_spacing': max(1, line_spacing),
                'total_height': max(1, actual_height + top_padding + bottom_padding)
            }
            
            return result
            
        except Exception as e:
            print(f"خطأ عام في حساب مسافات النص: {str(e)}")
            # إرجاع قيم افتراضية آمنة ومضمونة
            return {
                'height': 20,
                'ascent': 16,
                'descent': 4,
                'leading': 2,
                'top_padding': 10,
                'bottom_padding': 8,
                'line_spacing': 5,
                'total_height': 38
            }
    
    def get_section_spacing(self, section_type: str) -> int:
        """
        الحصول على مسافة القسم حسب النوع
        
        Args:
            section_type: نوع القسم ('company_header', 'invoice_info', 'table_header', etc.)
            
        Returns:
            int: المسافة المناسبة بالبكسل
        """
        try:
            # تعريف المسافات الأساسية لكل نوع قسم - محسنة خصيصاً لـ A4
            base_spacings = {
                'company_header': 60,      # مسافة بعد رأس الشركة - زيادة كبيرة لـ A4
                'company_info': 50,        # مسافة بين عناصر معلومات الشركة - زيادة لـ A4
                'separator_line': 55,      # مسافة حول الخط الفاصل - زيادة لـ A4
                'invoice_info': 50,        # مسافة بين عناصر معلومات الفاتورة - زيادة لـ A4
                'table_header': 35,        # مسافة قبل رأس الجدول - زيادة لـ A4
                'table_row': 25,          # مسافة بين صفوف الجدول - زيادة لـ A4
                'total_section': 50,       # مسافة حول قسم الإجمالي - زيادة لـ A4
                'notes_section': 45,       # مسافة قبل الملاحظات - زيادة لـ A4
                'end_spacing': 55          # مسافة نهائية - زيادة لـ A4
            }
            
            # الحصول على المسافة الأساسية
            base_spacing = base_spacings.get(section_type, 20)
            
            # تطبيق معامل التصحيح حسب حجم الورق
            adjusted_spacing = int(base_spacing * self.correction_factor['spacing_multiplier'])
            
            # تطبيق الحدود الدنيا والعليا
            final_spacing = max(self.correction_factor['min_spacing'] // 2,
                              min(adjusted_spacing, self.correction_factor['max_spacing']))
            
            return final_spacing
            
        except Exception as e:
            print(f"خطأ في حساب مسافة القسم {section_type}: {str(e)}")
            # إرجاع قيمة افتراضية آمنة
            return 20
    
    def get_paper_specific_adjustments(self) -> dict:
        """
        الحصول على التعديلات الخاصة بحجم الورق
        
        Returns:
            dict: قاموس التعديلات الخاصة بالورق
        """
        return {
            'margin_adjustment': self.correction_factor['spacing_multiplier'],
            'font_size_adjustment': 1.0 if self.paper_width >= 80 else 0.9,
            'table_cell_padding': int(20 * self.correction_factor['spacing_multiplier']),
            'separator_thickness': 1 if self.paper_width <= 58 else 2
        }
    
    def validate_spacing_bounds(self, spacing_value: int, spacing_type: str = 'general') -> int:
        """
        التحقق من صحة قيم المسافات وتطبيق الحدود
        
        Args:
            spacing_value: قيمة المسافة المراد التحقق منها
            spacing_type: نوع المسافة للتحقق المخصص
            
        Returns:
            int: قيمة المسافة المصححة
        """
        try:
            min_val = self.correction_factor['min_spacing']
            max_val = self.correction_factor['max_spacing']
            
            # تعديل الحدود حسب نوع المسافة
            if spacing_type == 'padding':
                min_val = min_val // 2
                max_val = max_val // 2
            elif spacing_type == 'line':
                min_val = 2
                max_val = max_val // 4
            
            return max(min_val, min(spacing_value, max_val))
            
        except Exception as e:
            print(f"خطأ في التحقق من حدود المسافة: {str(e)}")
            return max(5, min(spacing_value, 50))  # قيم افتراضية آمنة
    
    def get_line_spacing(self, row_type: str = 'data') -> int:
        """
        حساب المسافة بين الأسطر حسب نوع الصف
        
        Args:
            row_type: نوع الصف ('header', 'data', 'general')
        
        Returns:
            int: المسافة بالبكسل
        """
        try:
            # تعريف المسافات الأساسية لكل نوع صف
            base_line_spacings = {
                'header': 12,    # مسافة أكبر لصف العناوين
                'data': 6,       # مسافة أصغر لصفوف البيانات  
                'general': 15     # زيادة كبيرة في المسافة المتوسطة لفصل معلومات الشركة
            }
            
            # الحصول على المسافة الأساسية
            base_spacing = base_line_spacings.get(row_type, 8)
            
            # تطبيق معامل التصحيح حسب حجم الورق
            spacing_multiplier = self.correction_factor.get('spacing_multiplier', 1.0)
            
            # زيادة المسافات بشكل كبير للورق A4 لتحسين القراءة
            if self.paper_width >= 210:  # A4 أو أكبر
                spacing_multiplier = max(spacing_multiplier, 3.5)
                if row_type == 'header':
                    spacing_multiplier = 4.0
                elif row_type == 'general':
                    spacing_multiplier = 3.5
            else:
                # زيادة المسافات أيضاً للأحجام الأخرى
                spacing_multiplier = max(spacing_multiplier, 2.0)
                if row_type == 'header':
                    spacing_multiplier = 2.5
                elif row_type == 'general':
                    spacing_multiplier = 2.2
            
            adjusted_spacing = int(base_spacing * spacing_multiplier)
            
            # تطبيق الحدود الدنيا والعليا
            # زيادة الحد الأدنى للورق A4
            if self.paper_width >= 210:
                min_spacing = 15 if row_type == 'data' else 20
                max_spacing = self.correction_factor.get('max_spacing', 80) // 2
            else:
                min_spacing = 5 if row_type == 'data' else 8
                max_spacing = self.correction_factor.get('max_spacing', 50) // 4
            
            final_spacing = max(min_spacing, min(adjusted_spacing, max_spacing))
            
            return final_spacing
            
        except Exception as e:
            print(f"خطأ في حساب مسافة الأسطر لنوع {row_type}: {str(e)}")
            # إرجاع قيم افتراضية آمنة حسب النوع
            if row_type == 'header':
                return 10
            elif row_type == 'data':
                return 5
            else:
                return 8


class FontManager:
    """مدير الخطوط للفواتير"""
    
    def __init__(self, paper_width: int):
        """
        تهيئة مدير الخطوط مع تحقق شامل من صحة المدخلات
        
        Args:
            paper_width: عرض الورق بالمليمتر (58، 80، 210)
            
        Raises:
            ValueError: إذا كان حجم الورق غير صحيح
            TypeError: إذا كان نوع المدخل غير صحيح
        """
        try:
            # التحقق من نوع المدخل
            if not isinstance(paper_width, (int, float)):
                print(f"تحذير: نوع حجم الورق غير صحيح {type(paper_width)}، استخدام قيمة افتراضية")
                paper_width = 80
            
            # تحويل إلى int إذا كان float
            paper_width = int(paper_width)
            
            # التحقق من صحة حجم الورق
            if paper_width <= 0:
                print(f"تحذير: حجم الورق غير صحيح ({paper_width})، استخدام قيمة افتراضية")
                paper_width = 80
            
            self.paper_width = paper_width
            
            # الحصول على أحجام الخطوط الأساسية مع معالجة الأخطاء
            try:
                self.base_font_sizes = self._get_base_font_sizes()
            except Exception as e:
                print(f"خطأ في الحصول على أحجام الخطوط: {str(e)}")
                # استخدام أحجام افتراضية آمنة
                self.base_font_sizes = {
                    'company_name': 14,
                    'company_info': 10,
                    'invoice_info': 9,
                    'table_header': 9,
                    'table_data': 8,
                    'total': 11,
                    'notes': 8
                }
            
            # التحقق من صحة أحجام الخطوط
            self._validate_font_sizes()
            
            print(f"تم تهيئة مدير الخطوط بنجاح لورق {paper_width}مم")
            
        except Exception as e:
            print(f"خطأ في تهيئة مدير الخطوط: {str(e)}")
            # قيم طوارئ
            self.paper_width = 80
            self.base_font_sizes = {
                'company_name': 14,
                'company_info': 10,
                'invoice_info': 9,
                'table_header': 9,
                'table_data': 8,
                'total': 11,
                'notes': 8
            }
    
    def _validate_font_sizes(self):
        """التحقق من صحة أحجام الخطوط وإصلاحها إذا لزم الأمر"""
        try:
            required_fonts = ['company_name', 'company_info', 'invoice_info', 
                             'table_header', 'table_data', 'total', 'notes']
            
            for font_type in required_fonts:
                if font_type not in self.base_font_sizes:
                    print(f"تحذير: نوع الخط {font_type} مفقود، إضافة قيمة افتراضية")
                    self.base_font_sizes[font_type] = 10
                
                # التحقق من صحة حجم الخط
                size = self.base_font_sizes[font_type]
                if not isinstance(size, (int, float)) or size <= 0:
                    print(f"تحذير: حجم خط غير صحيح لـ {font_type}: {size}")
                    self.base_font_sizes[font_type] = 10
                elif size > 72:
                    print(f"تحذير: حجم خط كبير جداً لـ {font_type}: {size}")
                    self.base_font_sizes[font_type] = 24
                elif size < 6:
                    print(f"تحذير: حجم خط صغير جداً لـ {font_type}: {size}")
                    self.base_font_sizes[font_type] = 8
                    
        except Exception as e:
            print(f"خطأ في التحقق من أحجام الخطوط: {str(e)}")
        
    def _get_base_font_sizes(self) -> dict:
        """تحديد أحجام الخطوط الأساسية حسب حجم الورق"""
        if self.paper_width <= 58:
            return {
                'company_name': 14,
                'company_info': 10,
                'invoice_info': 9,
                'table_header': 9,
                'table_data': 8,
                'total': 11,
                'notes': 8
            }
        elif self.paper_width <= 80:
            return {
                'company_name': 16,
                'company_info': 12,
                'invoice_info': 11,
                'table_header': 10,
                'table_data': 9,
                'total': 13,
                'notes': 9
            }
        else:  # A4 وأحجام أكبر
            return {
                'company_name': 20,
                'company_info': 14,
                'invoice_info': 13,
                'table_header': 12,
                'table_data': 11,
                'total': 16,
                'notes': 11
            }
    
    def get_optimized_fonts(self) -> dict:
        """
        الحصول على الخطوط المحسنة حسب حجم الورق مع معالجة شاملة للأخطاء
        
        Returns:
            dict: قاموس الخطوط المحسنة
            
        Raises:
            RuntimeError: إذا فشل في إنشاء أي خط
        """
        fonts = {}
        failed_fonts = []
        
        try:
            # التحقق من وجود أحجام الخطوط
            if not hasattr(self, 'base_font_sizes') or not self.base_font_sizes:
                print("تحذير: أحجام الخطوط غير متوفرة، استخدام قيم افتراضية")
                self.base_font_sizes = {
                    'company_name': 14, 'company_info': 10, 'invoice_info': 9,
                    'table_header': 9, 'table_data': 8, 'total': 11, 'notes': 8
                }
            
            # قائمة الخطوط المتاحة للتجربة
            available_fonts = ["Arial", "Tahoma", "Calibri", "Times New Roman", "DejaVu Sans"]
            
            for font_type, size in self.base_font_sizes.items():
                font_created = False
                
                # تجربة إنشاء الخط مع خطوط مختلفة
                for font_family in available_fonts:
                    try:
                        font = QFont()
                        font.setFamily(font_family)
                        
                        # التحقق من صحة حجم الخط
                        validated_size = self._validate_font_size(size, font_type)
                        font.setPointSize(validated_size)
                        
                        # تطبيق خصائص خاصة حسب نوع الخط
                        try:
                            if font_type in ['company_name', 'table_header', 'total']:
                                font.setBold(True)
                        except Exception as bold_error:
                            print(f"تحذير: فشل في تطبيق الخط العريض لـ {font_type}: {str(bold_error)}")
                        
                        # تحسين جودة الخط مع معالجة الأخطاء
                        try:
                            font.setStyleHint(QFont.SansSerif)
                            font.setHintingPreference(QFont.PreferDefaultHinting)
                        except Exception as hint_error:
                            print(f"تحذير: فشل في تحسين جودة الخط لـ {font_type}: {str(hint_error)}")
                        
                        # اختبار الخط للتأكد من صحته
                        if self._test_font(font):
                            fonts[font_type] = font
                            font_created = True
                            break
                        else:
                            print(f"تحذير: الخط {font_family} غير صالح لـ {font_type}")
                            
                    except Exception as font_error:
                        print(f"خطأ في إنشاء خط {font_family} لـ {font_type}: {str(font_error)}")
                        continue
                
                # إذا فشل في إنشاء الخط، استخدم خط افتراضي بسيط
                if not font_created:
                    try:
                        fallback_font = QFont()
                        fallback_font.setPointSize(max(8, min(size, 16)))  # حجم آمن
                        fonts[font_type] = fallback_font
                        failed_fonts.append(font_type)
                        print(f"تحذير: استخدام خط افتراضي لـ {font_type}")
                    except Exception as fallback_error:
                        print(f"خطأ حرج: فشل في إنشاء خط افتراضي لـ {font_type}: {str(fallback_error)}")
                        failed_fonts.append(font_type)
            
            # التحقق من نجاح إنشاء الخطوط الأساسية
            required_fonts = ['company_name', 'company_info', 'invoice_info', 'table_data']
            missing_required = [f for f in required_fonts if f not in fonts]
            
            if missing_required:
                print(f"تحذير: فشل في إنشاء خطوط أساسية: {missing_required}")
                # إنشاء خطوط طوارئ للخطوط المفقودة
                for font_type in missing_required:
                    try:
                        emergency_font = QFont()
                        emergency_font.setPointSize(10)
                        fonts[font_type] = emergency_font
                    except:
                        pass
            
            if failed_fonts:
                print(f"تم إنشاء الخطوط مع بعض المشاكل في: {failed_fonts}")
            else:
                print("تم إنشاء جميع الخطوط بنجاح")
            
            return fonts if fonts else self._get_fallback_fonts()
            
        except Exception as e:
            print(f"خطأ عام في إنشاء الخطوط المحسنة: {str(e)}")
            return self._get_fallback_fonts()
    
    def _validate_font_size(self, size: int, font_type: str) -> int:
        """التحقق من صحة حجم الخط وإصلاحه إذا لزم الأمر"""
        try:
            if not isinstance(size, (int, float)):
                print(f"تحذير: حجم خط غير صحيح لـ {font_type}: {size}")
                return 10
            
            size = int(size)
            
            if size <= 0:
                print(f"تحذير: حجم خط غير موجب لـ {font_type}: {size}")
                return 8
            elif size > 72:
                print(f"تحذير: حجم خط كبير جداً لـ {font_type}: {size}")
                return 24
            elif size < 6:
                print(f"تحذير: حجم خط صغير جداً لـ {font_type}: {size}")
                return 8
            
            return size
            
        except Exception as e:
            print(f"خطأ في التحقق من حجم الخط لـ {font_type}: {str(e)}")
            return 10
    
    def _test_font(self, font: QFont) -> bool:
        """اختبار الخط للتأكد من صحته"""
        try:
            # محاولة إنشاء QFontMetrics للاختبار
            fm = QFontMetrics(font)
            # اختبار بسيط لحساب عرض نص
            width = fm.horizontalAdvance("Test") if hasattr(fm, 'horizontalAdvance') else fm.width("Test")
            return width > 0
        except Exception:
            return False
    
    def _get_fallback_fonts(self) -> dict:
        """إنشاء خطوط احتياطية آمنة في حالة الخطأ مع معالجة شاملة"""
        print("إنشاء خطوط احتياطية آمنة...")
        
        try:
            fonts = {}
            
            # قائمة أحجام الخطوط الاحتياطية
            fallback_sizes = {
                'company_name': 12,
                'company_info': 9,
                'invoice_info': 9,
                'table_header': 9,
                'table_data': 8,
                'total': 10,
                'notes': 8
            }
            
            # محاولة إنشاء خطوط مختلفة لكل نوع
            for font_type, size in fallback_sizes.items():
                font_created = False
                
                # تجربة عدة طرق لإنشاء الخط
                for attempt in range(3):
                    try:
                        if attempt == 0:
                            # المحاولة الأولى: خط عادي
                            font = QFont()
                            font.setPointSize(size)
                        elif attempt == 1:
                            # المحاولة الثانية: خط بعائلة محددة
                            font = QFont("Arial")
                            font.setPointSize(size)
                        else:
                            # المحاولة الثالثة: خط افتراضي بسيط
                            font = QFont()
                            font.setPointSize(10)  # حجم ثابت آمن
                        
                        # اختبار الخط
                        if self._test_font(font):
                            fonts[font_type] = font
                            font_created = True
                            break
                            
                    except Exception as e:
                        print(f"فشلت المحاولة {attempt + 1} لإنشاء خط {font_type}: {str(e)}")
                        continue
                
                # إذا فشلت جميع المحاولات، إنشاء خط أساسي جداً
                if not font_created:
                    try:
                        basic_font = QFont()
                        fonts[font_type] = basic_font
                        print(f"استخدام خط أساسي لـ {font_type}")
                    except Exception as basic_error:
                        print(f"خطأ حرج: فشل في إنشاء خط أساسي لـ {font_type}: {str(basic_error)}")
            
            # التحقق من وجود خطوط أساسية على الأقل
            if not fonts:
                print("تحذير: فشل في إنشاء أي خطوط احتياطية")
                # محاولة أخيرة لإنشاء خط واحد على الأقل
                try:
                    emergency_font = QFont()
                    return {
                        'company_name': emergency_font,
                        'company_info': emergency_font,
                        'invoice_info': emergency_font,
                        'table_header': emergency_font,
                        'table_data': emergency_font,
                        'total': emergency_font,
                        'notes': emergency_font
                    }
                except Exception as emergency_error:
                    print(f"خطأ حرج: فشل في إنشاء خط طوارئ: {str(emergency_error)}")
                    return {}
            
            print(f"تم إنشاء {len(fonts)} خط احتياطي بنجاح")
            return fonts
            
        except Exception as e:
            print(f"خطأ عام في إنشاء الخطوط الاحتياطية: {str(e)}")
            # إرجاع قاموس فارغ كحل أخير
            return {}
    
    def calculate_text_metrics(self, font: QFont, text: str) -> dict:
        """
        حساب مقاييس النص بدقة باستخدام QFontMetrics
        
        Args:
            font: الخط المستخدم
            text: النص المراد قياسه
            
        Returns:
            dict: مقاييس النص
            {
                'width': int,
                'height': int,
                'ascent': int,
                'descent': int,
                'leading': int
            }
        """
        try:
            fm = QFontMetrics(font)
            
            # حساب عرض النص
            text_width = fm.horizontalAdvance(text) if hasattr(fm, 'horizontalAdvance') else fm.width(text)
            
            # حساب مقاييس الارتفاع
            height = fm.height()
            ascent = fm.ascent()
            descent = fm.descent()
            leading = fm.leading()
            
            return {
                'width': text_width,
                'height': height,
                'ascent': ascent,
                'descent': descent,
                'leading': leading,
                'bounding_rect_height': ascent + descent,
                'line_spacing': height + leading
            }
            
        except Exception as e:
            print(f"خطأ في حساب مقاييس النص '{text}': {str(e)}")
            # إرجاع قيم افتراضية آمنة
            return {
                'width': len(text) * 8,  # تقدير تقريبي
                'height': 20,
                'ascent': 16,
                'descent': 4,
                'leading': 2,
                'bounding_rect_height': 20,
                'line_spacing': 22
            }
    
    def get_font_for_text_length(self, text: str, max_width: int, base_font_type: str = 'table_data') -> QFont:
        """
        الحصول على خط مناسب لطول النص المحدد
        
        Args:
            text: النص المراد قياسه
            max_width: العرض الأقصى المتاح
            base_font_type: نوع الخط الأساسي
            
        Returns:
            QFont: الخط المناسب
        """
        try:
            fonts = self.get_optimized_fonts()
            base_font = fonts.get(base_font_type, QFont())
            
            # البدء بحجم الخط الأساسي
            current_size = base_font.pointSize()
            min_size = max(6, current_size - 4)  # حد أدنى للحجم
            
            # تجربة أحجام مختلفة حتى يناسب النص العرض المتاح
            for size in range(current_size, min_size - 1, -1):
                test_font = QFont(base_font)
                test_font.setPointSize(size)
                
                metrics = self.calculate_text_metrics(test_font, text)
                if metrics['width'] <= max_width:
                    return test_font
            
            # إذا لم يناسب أي حجم، إرجاع أصغر حجم
            final_font = QFont(base_font)
            final_font.setPointSize(min_size)
            return final_font
            
        except Exception as e:
            print(f"خطأ في تحديد خط مناسب للنص: {str(e)}")
            # إرجاع خط افتراضي آمن
            fallback_font = QFont()
            fallback_font.setPointSize(8)
            return fallback_font
    
    def validate_font(self, font: QFont) -> QFont:
        """
        التحقق من صحة الخط وإصلاحه إذا لزم الأمر
        
        Args:
            font: الخط المراد التحقق منه
            
        Returns:
            QFont: الخط المصحح
        """
        try:
            # التحقق من حجم الخط
            if font.pointSize() <= 0:
                if font.pixelSize() > 0:
                    # تحويل من البكسل إلى النقطة
                    font.setPointSize(max(8, int(font.pixelSize() / 1.33)))
                else:
                    font.setPointSize(10)  # حجم افتراضي آمن
            
            # التأكد من أن الحجم ضمن الحدود المعقولة
            current_size = font.pointSize()
            if current_size < 6:
                font.setPointSize(6)
            elif current_size > 72:
                font.setPointSize(72)
            
            return font
            
        except Exception as e:
            print(f"خطأ في التحقق من صحة الخط: {str(e)}")
            # إرجاع خط افتراضي آمن
            safe_font = QFont()
            safe_font.setPointSize(10)
            return safe_font


class AdaptiveLayoutManager:
    """مدير التخطيط التكيفي للفواتير"""
    
    def __init__(self, paper_width: int, content_width: int):
        """
        تهيئة مدير التخطيط التكيفي
        
        Args:
            paper_width: عرض الورق بالمليمتر
            content_width: عرض المحتوى بالبكسل
        """
        self.paper_width = paper_width
        self.content_width = content_width
        self.spacing_calc = SpacingCalculator(paper_width)
        self.font_manager = FontManager(paper_width)
        
        # الحصول على الخطوط المحسنة
        self.fonts = self.font_manager.get_optimized_fonts()
        
        # إعدادات التخطيط
        self.layout_config = {
            'center_alignment': True,
            'rtl_support': True,
            'auto_wrap': True,
            'collision_detection': True
        }
    
    def layout_company_section(self, painter: QPainter, company_info: dict, y_start: int) -> int:
        """
        تخطيط قسم معلومات الشركة مع منع التداخل
        
        Args:
            painter: كائن الرسم
            company_info: معلومات الشركة
            y_start: الموضع Y للبداية
            
        Returns:
            int: الموضع Y النهائي بعد رسم القسم
        """
        try:
            current_y = y_start
            
            # رسم اسم الشركة
            if company_info.get('name'):
                current_y = self._draw_company_name(painter, company_info['name'], current_y)
            
            # رسم رقم الهاتف
            if company_info.get('phone'):
                current_y = self._draw_company_phone(painter, company_info['phone'], current_y)
            
            # رسم العنوان
            if company_info.get('address'):
                current_y = self._draw_company_address(painter, company_info['address'], current_y)
            
            # إضافة مسافة بعد قسم الشركة
            section_spacing = self.spacing_calc.get_section_spacing('company_header')
            current_y += section_spacing
            
            return current_y
            
        except Exception as e:
            print(f"خطأ في تخطيط قسم الشركة: {str(e)}")
            return y_start + 100  # قيمة افتراضية آمنة
    
    def _draw_company_name(self, painter: QPainter, company_name: str, y_pos: int) -> int:
        """رسم اسم الشركة مع حساب المسافات الدقيقة"""
        try:
            font = self.fonts['company_name']
            painter.setFont(font)
            
            # حساب مقاييس النص
            text_metrics = self.font_manager.calculate_text_metrics(font, company_name)
            spacing_info = self.spacing_calc.calculate_text_spacing(font)
            
            # حساب موضع النص (وسط الصفحة)
            text_x = (self.content_width - text_metrics['width']) // 2
            text_y = y_pos + spacing_info['top_padding'] + text_metrics['ascent']
            
            # رسم النص
            painter.drawText(text_x, text_y, company_name)
            
            # إرجاع الموضع التالي
            return text_y + text_metrics['descent'] + spacing_info['bottom_padding']
            
        except Exception as e:
            print(f"خطأ في رسم اسم الشركة: {str(e)}")
            return y_pos + 30
    
    def _draw_company_phone(self, painter: QPainter, phone: str, y_pos: int) -> int:
        """رسم رقم هاتف الشركة مع حساب المسافات الدقيقة"""
        try:
            font = self.fonts['company_info']
            painter.setFont(font)
            
            # إضافة نص توضيحي
            phone_text = f"هاتف: {phone}"
            
            # حساب مقاييس النص
            text_metrics = self.font_manager.calculate_text_metrics(font, phone_text)
            spacing_info = self.spacing_calc.calculate_text_spacing(font)
            
            # حساب موضع النص (وسط الصفحة)
            text_x = (self.content_width - text_metrics['width']) // 2
            text_y = y_pos + spacing_info['top_padding'] + text_metrics['ascent']
            
            # رسم النص
            painter.drawText(text_x, text_y, phone_text)
            
            # إرجاع الموضع التالي
            return text_y + text_metrics['descent'] + spacing_info['bottom_padding']
            
        except Exception as e:
            print(f"خطأ في رسم رقم الهاتف: {str(e)}")
            return y_pos + 25
    
    def _draw_company_address(self, painter: QPainter, address: str, y_pos: int) -> int:
        """رسم عنوان الشركة مع دعم النصوص الطويلة"""
        try:
            font = self.fonts['company_info']
            painter.setFont(font)
            
            # تقسيم العنوان إلى أسطر إذا كان طويلاً
            lines = self._wrap_text(address, font, self.content_width - 20)
            
            current_y = y_pos
            spacing_info = self.spacing_calc.calculate_text_spacing(font)
            
            for line in lines:
                # حساب مقاييس النص
                text_metrics = self.font_manager.calculate_text_metrics(font, line)
                
                # حساب موضع النص (وسط الصفحة)
                text_x = (self.content_width - text_metrics['width']) // 2
                text_y = current_y + spacing_info['top_padding'] + text_metrics['ascent']
                
                # رسم النص
                painter.drawText(text_x, text_y, line)
                
                # الانتقال للسطر التالي
                current_y = text_y + text_metrics['descent'] + spacing_info['line_spacing']
            
            # إضافة مسافة إضافية بعد آخر سطر
            return current_y + spacing_info['bottom_padding']
            
        except Exception as e:
            print(f"خطأ في رسم عنوان الشركة: {str(e)}")
            return y_pos + 40
    
    def layout_invoice_section(self, painter: QPainter, invoice_data: dict, y_start: int) -> int:
        """
        تخطيط قسم معلومات الفاتورة مع منع التداخل
        
        Args:
            painter: كائن الرسم
            invoice_data: بيانات الفاتورة
            y_start: الموضع Y للبداية
            
        Returns:
            int: الموضع Y النهائي بعد رسم القسم
        """
        try:
            current_y = y_start
            
            # رسم خط فاصل
            current_y = self._draw_separator_line(painter, current_y)
            
            # رسم رقم الفاتورة
            invoice_number = invoice_data.get('reference_number', invoice_data.get('invoice_number', ''))
            if invoice_number and invoice_number.strip():
                current_y = self._draw_invoice_number(painter, invoice_number, current_y)
            
            # رسم التاريخ
            if invoice_data.get('date'):
                current_y = self._draw_invoice_date(painter, invoice_data['date'], current_y)
            
            # رسم اسم العميل
            if invoice_data.get('customer_name'):
                current_y = self._draw_customer_name(painter, invoice_data['customer_name'], current_y)
            
            # إضافة مسافة بعد قسم الفاتورة
            section_spacing = self.spacing_calc.get_section_spacing('invoice_info')
            current_y += section_spacing
            
            return current_y
            
        except Exception as e:
            print(f"خطأ في تخطيط قسم الفاتورة: {str(e)}")
            return y_start + 120  # قيمة افتراضية آمنة
    
    def _draw_separator_line(self, painter: QPainter, y_pos: int) -> int:
        """رسم خط فاصل بين الأقسام"""
        try:
            spacing = self.spacing_calc.get_section_spacing('separator_line')
            line_y = y_pos + spacing // 2
            
            # رسم الخط
            line_start = 10
            line_end = self.content_width - 10
            painter.drawLine(line_start, line_y, line_end, line_y)
            
            return line_y + spacing // 2
            
        except Exception as e:
            print(f"خطأ في رسم الخط الفاصل: {str(e)}")
            return y_pos + 20
    
    def _draw_invoice_number(self, painter: QPainter, invoice_number: str, y_pos: int) -> int:
        """رسم رقم الفاتورة"""
        try:
            font = self.fonts['invoice_info']
            painter.setFont(font)
            
            invoice_text = f"رقم الفاتورة: {invoice_number}"
            
            # حساب مقاييس النص
            text_metrics = self.font_manager.calculate_text_metrics(font, invoice_text)
            spacing_info = self.spacing_calc.calculate_text_spacing(font)
            
            # حساب موضع النص (محاذاة يمين)
            text_x = self.content_width - text_metrics['width'] - 10
            text_y = y_pos + spacing_info['top_padding'] + text_metrics['ascent']
            
            # رسم النص
            painter.drawText(text_x, text_y, invoice_text)
            
            return text_y + text_metrics['descent'] + spacing_info['bottom_padding']
            
        except Exception as e:
            print(f"خطأ في رسم رقم الفاتورة: {str(e)}")
            return y_pos + 25
    
    def _draw_invoice_date(self, painter: QPainter, date: str, y_pos: int) -> int:
        """رسم تاريخ الفاتورة"""
        try:
            font = self.fonts['invoice_info']
            painter.setFont(font)
            
            date_text = f"التاريخ: {date}"
            
            # حساب مقاييس النص
            text_metrics = self.font_manager.calculate_text_metrics(font, date_text)
            spacing_info = self.spacing_calc.calculate_text_spacing(font)
            
            # حساب موضع النص (محاذاة يمين)
            text_x = self.content_width - text_metrics['width'] - 10
            text_y = y_pos + spacing_info['top_padding'] + text_metrics['ascent']
            
            # رسم النص
            painter.drawText(text_x, text_y, date_text)
            
            return text_y + text_metrics['descent'] + spacing_info['bottom_padding']
            
        except Exception as e:
            print(f"خطأ في رسم تاريخ الفاتورة: {str(e)}")
            return y_pos + 25
    
    def _draw_customer_name(self, painter: QPainter, customer_name: str, y_pos: int) -> int:
        """رسم اسم العميل مع دعم الأسماء الطويلة"""
        try:
            font = self.fonts['invoice_info']
            painter.setFont(font)
            
            customer_text = f"العميل: {customer_name}"
            
            # التحقق من طول النص وتعديل الخط إذا لزم الأمر
            adjusted_font = self.font_manager.get_font_for_text_length(
                customer_text, self.content_width - 20, 'invoice_info'
            )
            painter.setFont(adjusted_font)
            
            # حساب مقاييس النص
            text_metrics = self.font_manager.calculate_text_metrics(adjusted_font, customer_text)
            spacing_info = self.spacing_calc.calculate_text_spacing(adjusted_font)
            
            # حساب موضع النص (محاذاة يمين)
            text_x = self.content_width - text_metrics['width'] - 10
            text_y = y_pos + spacing_info['top_padding'] + text_metrics['ascent']
            
            # رسم النص
            painter.drawText(text_x, text_y, customer_text)
            
            return text_y + text_metrics['descent'] + spacing_info['bottom_padding']
            
        except Exception as e:
            print(f"خطأ في رسم اسم العميل: {str(e)}")
            return y_pos + 25
    
    def _wrap_text(self, text: str, font: QFont, max_width: int) -> list:
        """تقسيم النص إلى أسطر متعددة حسب العرض المتاح"""
        try:
            words = text.split()
            lines = []
            current_line = ""
            
            for word in words:
                test_line = f"{current_line} {word}".strip()
                text_metrics = self.font_manager.calculate_text_metrics(font, test_line)
                
                if text_metrics['width'] <= max_width:
                    current_line = test_line
                else:
                    if current_line:
                        lines.append(current_line)
                        current_line = word
                    else:
                        # الكلمة طويلة جداً، قسمها
                        lines.append(word)
            
            if current_line:
                lines.append(current_line)
            
            return lines if lines else [text]
            
        except Exception as e:
            print(f"خطأ في تقسيم النص: {str(e)}")
            return [text]
    
    def check_collision(self, y1: int, height1: int, y2: int, height2: int) -> bool:
        """فحص التداخل بين عنصرين"""
        try:
            return not (y1 + height1 < y2 or y2 + height2 < y1)
        except:
            return False
    
    def get_safe_y_position(self, elements: list, desired_y: int, element_height: int) -> int:
        """الحصول على موضع Y آمن بدون تداخل"""
        try:
            safe_y = desired_y
            
            for element in elements:
                if self.check_collision(safe_y, element_height, element['y'], element['height']):
                    safe_y = element['y'] + element['height'] + 10  # مسافة أمان
            
            return safe_y
            
        except Exception as e:
            print(f"خطأ في حساب الموضع الآمن: {str(e)}")
            return desired_y


def create_thermal_printer(paper_width_mm=None):
    """إنشاء طابعة حرارية محسنة - دالة مستقلة للاستخدام السهل"""
    try:
        # قراءة حجم الورق من الإعدادات إذا لم يُحدد
        if paper_width_mm is None:
            from utils.settings_manager import SettingsManager
            settings_manager = SettingsManager()
            paper_width_mm = settings_manager.get_paper_width_safe()
            print(f"حجم الورقة المقروء من الإعدادات: {paper_width_mm}مم")

        printer = QPrinter()

        # تعيين حجم الورق المخصص
        if paper_width_mm == 58:
            printer.setPaperSize(QSizeF(58, 100), QPrinter.Millimeter)
            print("تم إنشاء طابعة حرارية: 58مم")
        elif paper_width_mm == 80:
            printer.setPaperSize(QSizeF(80, 120), QPrinter.Millimeter)
            print("تم إنشاء طابعة حرارية: 80مم")
        elif paper_width_mm == 210:  # A4 paper
            printer.setPaperSize(QSizeF(210, 297), QPrinter.Millimeter)
            print("تم إنشاء طابعة A4")
        else:
            printer.setPaperSize(QSizeF(paper_width_mm, 100), QPrinter.Millimeter)
            print(f"تم إنشاء طابعة حرارية: {paper_width_mm}مم")

        # إعدادات الطباعة محسنة للطباعة الحرارية
        printer.setPageMargins(0.5, 0.5, 0.5, 0.5, QPrinter.Millimeter)
        printer.setResolution(203)  # DPI للطابعات الحرارية
        printer.setColorMode(QPrinter.GrayScale)
        printer.setOutputFormat(QPrinter.NativeFormat)
        printer.setPageOrder(QPrinter.FirstPageFirst)
        printer.setOrientation(QPrinter.Portrait)

        return printer

    except Exception as e:
        print(f"خطأ في إنشاء الطابعة الحرارية: {str(e)}")
        return None
