"""
وحدة إدارة الفواتير - توفر وظائف للتعامل مع الفواتير في قاعدة البيانات
"""

from models.database import db
from models.customers import CustomerModel
from models.products import ProductModel
from utils.date_utils import DateTimeUtils
import datetime
import random
import string

class InvoiceModel:
    """نموذج التعامل مع بيانات الفواتير"""

    @staticmethod
    def get_all_invoices():
        """استرجاع جميع الفواتير من قاعدة البيانات"""
        query = """
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount,
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status,
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            ORDER BY i.date DESC
        """
        return db.fetch_all(query)

    @staticmethod
    def get_invoice_by_id(invoice_id):
        """استرجاع فاتورة بواسطة المعرف"""
        query = """
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount,
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status,
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE i.id = ?
        """
        return db.fetch_one(query, (invoice_id,))

    @staticmethod
    def get_invoice_by_reference(reference_number):
        """استرجاع فاتورة بواسطة الرقم المرجعي"""
        query = """
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount,
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status,
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE i.reference_number = ?
        """
        return db.fetch_one(query, (reference_number,))

    @staticmethod
    def get_invoice_id_by_reference(reference_number):
        """استرجاع معرف الفاتورة بواسطة الرقم المرجعي"""
        invoice = InvoiceModel.get_invoice_by_reference(reference_number)
        return invoice.get('id') if invoice else None

    @staticmethod
    def search_invoices(search_text=None, start_date=None, end_date=None, status=None, customer_id=None):
        """البحث عن فواتير بواسطة معايير مختلفة"""
        base_query = """
            SELECT i.id, i.reference_number, i.date, i.subtotal, i.tax, i.discount,
                   i.total, i.paid_amount, i.remaining_amount, i.payment_method, i.status,
                   i.notes, c.name as customer_name, c.id as customer_id
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE 1=1
        """
        params = []

        # إضافة فلتر البحث النصي
        if search_text:
            base_query += " AND (i.reference_number LIKE ? OR c.name LIKE ? OR i.notes LIKE ?)"
            search_pattern = f"%{search_text}%"
            params.extend([search_pattern, search_pattern, search_pattern])

        # إضافة فلتر تاريخ البدء
        if start_date:
            base_query += " AND (i.date >= ? OR i.date LIKE ?)"
            date_pattern = f"{start_date}%"
            params.extend([start_date, date_pattern])
            print(f"فلتر تاريخ البدء: {start_date}")

        # إضافة فلتر تاريخ الانتهاء
        if end_date:
            tomorrow = (datetime.datetime.strptime(end_date, "%Y/%m/%d") + datetime.timedelta(days=1)).strftime("%Y/%m/%d")
            base_query += " AND i.date < ?"
            params.append(tomorrow)
            print(f"فلتر تاريخ الانتهاء: {end_date} (قبل {tomorrow})")

        # إضافة فلتر الحالة
        if status and status != "الكل":
            base_query += " AND i.status = ?"
            params.append(status)

        # إضافة فلتر العميل
        if customer_id:
            base_query += " AND i.customer_id = ?"
            params.append(customer_id)

        # طباعة الاستعلام للتصحيح
        print(f"استعلام البحث: {base_query}")
        print(f"معلمات البحث: {params}")

        # ترتيب النتائج حسب التاريخ (الأحدث أولاً)
        base_query += " ORDER BY i.date DESC"

        # تنفيذ الاستعلام
        results = db.fetch_all(base_query, params)
        print(f"تم العثور على {len(results)} فاتورة")
        return results

    @staticmethod
    def get_invoice_items(invoice_id):
        """استرجاع عناصر فاتورة محددة"""
        query = """
            SELECT id, invoice_id, product_id, product_name, product_code,
                   quantity, unit_price, total_price
            FROM invoice_items
            WHERE invoice_id = ?
        """
        return db.fetch_all(query, (invoice_id,))

    @staticmethod
    def get_invoice_items_count(invoice_id):
        """استرجاع عدد عناصر فاتورة محددة"""
        query = """
            SELECT COUNT(*) as count
            FROM invoice_items
            WHERE invoice_id = ?
        """
        result = db.fetch_one(query, (invoice_id,))
        return result.get('count', 0) if result else 0

    @staticmethod
    def generate_reference_number():
        """توليد رقم مرجعي فريد للفاتورة مكون من 7 أرقام فقط"""
        # توليد رقم عشوائي مكون من 7 أرقام
        invoice_number = random.randint(1000000, 9999999)

        # التحقق من عدم وجود فاتورة بنفس الرقم المرجعي
        while InvoiceModel.get_invoice_by_reference(str(invoice_number)):
            invoice_number = random.randint(1000000, 9999999)

        return str(invoice_number)

    @staticmethod
    def add_invoice(invoice_data, items_data):
        """إضافة فاتورة جديدة مع عناصرها إلى قاعدة البيانات"""
        try:
            # بدء معاملة قاعدة البيانات
            if not db.conn:
                if not db.connect():
                    db.log_error("فشل الاتصال بقاعدة البيانات عند إنشاء الفاتورة")
                    return None

            # التأكد من أن التاريخ يحتوي على الوقت
            if 'date' in invoice_data:
                invoice_data['date'] = DateTimeUtils.convert_old_date_to_datetime(invoice_data['date'])
            else:
                invoice_data['date'] = DateTimeUtils.get_current_date_time()

            # إدراج بيانات الفاتورة الرئيسية
            query = """
                INSERT INTO invoices (
                    reference_number, customer_id, date, subtotal,
                    tax, discount, total, paid_amount, remaining_amount,
                    payment_method, status, notes
                )
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                invoice_data.get('reference_number'),
                invoice_data.get('customer_id'),
                invoice_data.get('date'),
                invoice_data.get('subtotal', 0),
                invoice_data.get('tax', 0),
                invoice_data.get('discount', 0),
                invoice_data.get('total', 0),
                invoice_data.get('paid_amount', 0),
                invoice_data.get('remaining_amount', 0),
                invoice_data.get('payment_method', 'نقدي'),
                invoice_data.get('status', 'مدفوعة'),
                invoice_data.get('notes', '')
            )

            if not db.execute(query, params):
                db.log_error("فشل إنشاء الفاتورة الرئيسية")
                return None

            invoice_id = db.get_last_insert_id()

            # إدراج عناصر الفاتورة وتحديث المخزون
            if items_data and invoice_id:
                for item in items_data:
                    # إدراج عنصر الفاتورة
                    item_query = """
                        INSERT INTO invoice_items (
                            invoice_id, product_id, product_name, product_code,
                            quantity, unit_price, total_price
                        )
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    """
                    item_params = (
                        invoice_id,
                        item.get('product_id'),
                        item.get('product_name'),
                        item.get('product_code'),
                        item.get('quantity', 0),
                        item.get('unit_price', 0),
                        item.get('total_price', 0)
                    )

                    if not db.execute(item_query, item_params):
                        db.log_error(f"فشل إضافة عنصر الفاتورة: {item.get('product_name')}")
                        # نستمر للعناصر الأخرى بدلاً من إلغاء العملية بالكامل

                    # تحديث كمية المنتج في المخزون
                    product_id = item.get('product_id')
                    quantity = item.get('quantity', 0)

                    if product_id and quantity > 0:
                        # الحصول على المنتج وكميته الحالية
                        product = ProductModel.get_product_by_id(product_id)

                        if product:
                            # حساب الكمية الجديدة
                            current_quantity = product.get('stock', 0)
                            new_quantity = current_quantity - quantity

                            # تحديث المخزون
                            success = ProductModel.update_product_quantity(product_id, new_quantity)

                            if not success:
                                db.log_error(f"فشل تحديث كمية المنتج {product_id} ({item.get('product_name')})")
                                print(f"فشل تحديث كمية المنتج {product_id} {item.get('product_name')} من {current_quantity} إلى {new_quantity}")
                            else:
                                print(f"تم تحديث كمية المنتج {product_id} {item.get('product_name')} من {current_quantity} إلى {new_quantity}")

            # تحديث إجمالي مشتريات العميل
            customer_id = invoice_data.get('customer_id')
            total_amount = invoice_data.get('total', 0)
            if customer_id:
                CustomerModel.update_customer_purchases(customer_id, total_amount)

            # حفظ جميع التغييرات
            db.commit()
            return invoice_id

        except Exception as e:
            # التراجع عن التغييرات في حالة حدوث خطأ
            if db.conn:
                db.rollback()
            db.log_error(f"خطأ في إنشاء الفاتورة: {str(e)}")
            print(f"خطأ في إنشاء الفاتورة: {str(e)}")
            return None

    @staticmethod
    def update_invoice(invoice_id, invoice_data):
        """تحديث بيانات فاتورة موجودة (بدون تحديث العناصر)"""
        try:
            # الحصول على بيانات الفاتورة الحالية للحفاظ على البيانات التي لم يتم تعديلها
            current_invoice = InvoiceModel.get_invoice_by_id(invoice_id)
            if not current_invoice:
                db.log_error(f"الفاتورة غير موجودة: {invoice_id}")
                return False

            # حفظ الحالة الحالية للمقارنة
            old_status = current_invoice.get('status', '')

            # التأكد من أن التاريخ يحتوي على الوقت
            if 'date' in invoice_data:
                invoice_data['date'] = DateTimeUtils.convert_old_date_to_datetime(invoice_data['date'])
            else:
                invoice_data['date'] = current_invoice.get('date')

            # الحفاظ على بيانات العميل إذا لم يتم تقديمها
            if 'customer_id' not in invoice_data or invoice_data['customer_id'] is None:
                invoice_data['customer_id'] = current_invoice.get('customer_id')

            # دمج البيانات الموجودة مع البيانات الجديدة للحقول الأخرى
            for field in ['subtotal', 'tax', 'discount', 'total', 'paid_amount',
                         'remaining_amount', 'payment_method', 'status', 'notes']:
                if field not in invoice_data:
                    invoice_data[field] = current_invoice.get(field, 0 if field in ['subtotal', 'tax', 'discount', 'total', 'paid_amount', 'remaining_amount'] else '')

            query = """
                UPDATE invoices
                SET customer_id = ?,
                    date = ?,
                    subtotal = ?,
                    tax = ?,
                    discount = ?,
                    total = ?,
                    paid_amount = ?,
                    remaining_amount = ?,
                    payment_method = ?,
                    status = ?,
                    notes = ?
                WHERE id = ?
            """
            params = (
                invoice_data.get('customer_id'),
                invoice_data.get('date'),
                invoice_data.get('subtotal', 0),
                invoice_data.get('tax', 0),
                invoice_data.get('discount', 0),
                invoice_data.get('total', 0),
                invoice_data.get('paid_amount', 0),
                invoice_data.get('remaining_amount', 0),
                invoice_data.get('payment_method', 'نقداً'),
                invoice_data.get('status', 'مدفوعة'),
                invoice_data.get('notes', ''),
                invoice_id
            )

            success = db.execute(query, params)
            if success:
                # تحديث حالة المنتجات إذا تغيرت حالة الفاتورة
                new_status = invoice_data.get('status', 'مدفوعة')
                if old_status != new_status:
                    InvoiceModel.update_invoice_items_payment_status(invoice_id, new_status, invoice_data.get('total', 0))

                db.commit()
                return True
            return False

        except Exception as e:
            # التراجع عن التغييرات في حالة حدوث خطأ
            if db.conn:
                db.rollback()
            db.log_error(f"خطأ في تحديث الفاتورة: {str(e)}")
            print(f"خطأ في تحديث الفاتورة: {str(e)}")
            return False

    @staticmethod
    def update_invoice_items_payment_status(invoice_id, invoice_status, invoice_total):
        """تحديث حالة دفع جميع منتجات الفاتورة بناءً على حالة الفاتورة"""
        try:
            if invoice_status == "مدفوعة":
                # إذا كانت الفاتورة مدفوعة، تحديث جميع المنتجات لتكون مدفوعة
                update_query = """
                    UPDATE invoice_items
                    SET is_paid = 1, paid_amount = total_price
                    WHERE invoice_id = ?
                """
                db.execute(update_query, (invoice_id,))

            elif invoice_status == "غير مدفوعة":
                # إذا كانت الفاتورة غير مدفوعة، تحديث جميع المنتجات لتكون غير مدفوعة
                update_query = """
                    UPDATE invoice_items
                    SET is_paid = 0, paid_amount = 0
                    WHERE invoice_id = ?
                """
                db.execute(update_query, (invoice_id,))

            # في حالة "مدفوع جزئياً" لا نقوم بتحديث المنتجات تلقائياً
            # لأن المستخدم قد يكون قام بدفع منتجات محددة

            return True

        except Exception as e:
            db.log_error(f"خطأ في تحديث حالة منتجات الفاتورة: {str(e)}")
            return False

    @staticmethod
    def pay_invoice_item(invoice_item_id, customer_id, amount, notes=""):
        """دفع منتج فردي من فاتورة"""
        try:
            # الحصول على بيانات المنتج في الفاتورة
            item_query = """
                SELECT ii.*, i.id as invoice_id, i.status as invoice_status, i.total as invoice_total,
                       i.paid_amount as invoice_paid_amount, i.remaining_amount as invoice_remaining_amount
                FROM invoice_items ii
                JOIN invoices i ON ii.invoice_id = i.id
                WHERE ii.id = ?
            """
            item = db.fetch_one(item_query, (invoice_item_id,))

            if not item:
                return False, "المنتج غير موجود"

            # التحقق من أن المنتج لم يتم دفعه بالكامل
            item_total = item.get('total_price', 0)
            item_paid = item.get('paid_amount', 0)
            item_remaining = item_total - item_paid

            if item_remaining <= 0:
                return False, "تم دفع هذا المنتج بالكامل مسبقاً"

            # التحقق من أن المبلغ لا يتجاوز المبلغ المتبقي
            if amount > item_remaining:
                return False, f"المبلغ يتجاوز المبلغ المتبقي للمنتج ({item_remaining:.2f} ج.م)"

            # تحديث بيانات المنتج
            new_paid_amount = item_paid + amount
            is_fully_paid = new_paid_amount >= item_total

            update_item_query = """
                UPDATE invoice_items
                SET paid_amount = ?, is_paid = ?
                WHERE id = ?
            """
            db.execute(update_item_query, (new_paid_amount, is_fully_paid, invoice_item_id))

            # إضافة سجل الدفعة
            payment_date = datetime.datetime.now().strftime("%Y/%m/%d %H:%M:%S")
            payment_query = """
                INSERT INTO invoice_item_payments (invoice_item_id, customer_id, amount, payment_date, notes)
                VALUES (?, ?, ?, ?, ?)
            """
            db.execute(payment_query, (invoice_item_id, customer_id, amount, payment_date, notes))

            # إضافة سجل في جدول المدفوعات العام مع اسم المنتج في الملاحظات
            payment_general_query = """
                INSERT INTO payments (invoice_id, customer_id, amount, payment_method, payment_date, notes, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """
            payment_method = "نقدي"  # أو استخرج طريقة الدفع الحقيقية إذا كانت متوفرة
            created_at = updated_at = payment_date
            db.execute(payment_general_query, (
                item.get('invoice_id'),
                customer_id,
                amount,
                payment_method,
                payment_date,
                notes,
                created_at,
                updated_at
            ))

            # تحديث حالة الفاتورة
            invoice_id = item.get('invoice_id')
            InvoiceModel.update_invoice_payment_status(invoice_id)

            db.commit()
            return True, "تم دفع المنتج بنجاح"

        except Exception as e:
            db.rollback()
            db.log_error(f"خطأ في دفع المنتج: {str(e)}")
            return False, f"خطأ في دفع المنتج: {str(e)}"

    @staticmethod
    def update_invoice_payment_status(invoice_id):
        """تحديث حالة دفع الفاتورة بناءً على حالة دفع المنتجات"""
        try:
            # الحصول على جميع منتجات الفاتورة
            items_query = """
                SELECT id, total_price, paid_amount, is_paid
                FROM invoice_items
                WHERE invoice_id = ?
            """
            items = db.fetch_all(items_query, (invoice_id,))

            if not items:
                return False

            # حساب إجمالي المبلغ المدفوع للفاتورة
            total_invoice_amount = sum(item.get('total_price', 0) for item in items)
            total_paid_amount = sum(item.get('paid_amount', 0) for item in items)
            remaining_amount = total_invoice_amount - total_paid_amount

            # تحديد حالة الفاتورة
            if remaining_amount <= 0:
                status = "مدفوعة"
            elif total_paid_amount > 0:
                status = "مدفوع جزئياً"
            else:
                status = "غير مدفوعة"

            # تحديث الفاتورة
            update_query = """
                UPDATE invoices
                SET paid_amount = ?, remaining_amount = ?, status = ?
                WHERE id = ?
            """
            db.execute(update_query, (total_paid_amount, remaining_amount, status, invoice_id))

            return True

        except Exception as e:
            db.log_error(f"خطأ في تحديث حالة الفاتورة: {str(e)}")
            return False

    @staticmethod
    def update_invoice_with_items(invoice_id, invoice_data, invoice_items):
        """تحديث بيانات فاتورة موجودة مع عناصرها"""
        try:
            # بدء معاملة قاعدة البيانات
            if not db.conn:
                db.connect()

            # تحديث بيانات الفاتورة الأساسية
            success = InvoiceModel.update_invoice(invoice_id, invoice_data)
            if not success:
                return False

            # حذف العناصر الحالية للفاتورة
            delete_items_query = "DELETE FROM invoice_items WHERE invoice_id = ?"
            db.execute(delete_items_query, (invoice_id,))

            # إضافة العناصر الجديدة
            for item in invoice_items:
                insert_item_query = """
                    INSERT INTO invoice_items (
                        invoice_id, product_id, product_name, product_code,
                        quantity, unit_price, total_price, created_at, updated_at
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """

                from utils.date_utils import DateTimeUtils
                current_time = DateTimeUtils.get_current_date_time()

                params = (
                    invoice_id,
                    item.get('product_id'),
                    item.get('product_name'),
                    item.get('product_code'),
                    item.get('quantity'),
                    item.get('unit_price'),
                    item.get('total_price'),
                    current_time,
                    current_time
                )

                db.execute(insert_item_query, params)

            # حفظ التغييرات
            db.commit()
            return True

        except Exception as e:
            # التراجع عن التغييرات في حالة حدوث خطأ
            if db.conn:
                db.rollback()
            db.log_error(f"خطأ في تحديث الفاتورة مع العناصر: {str(e)}")
            print(f"خطأ في تحديث الفاتورة مع العناصر: {str(e)}")
            return False

    @staticmethod
    def delete_invoice(invoice_id):
        """حذف فاتورة مع عناصرها من قاعدة البيانات"""
        try:
            # بدء معاملة قاعدة البيانات
            if not db.conn:
                db.connect()

            # استرجاع عناصر الفاتورة وإعادة كميات المنتجات إلى المخزون
            items = InvoiceModel.get_invoice_items(invoice_id)
            for item in items:
                product_id = item.get('product_id')
                quantity = item.get('quantity', 0)
                if product_id:
                    product = ProductModel.get_product_by_id(product_id)
                    if product:
                        new_quantity = product.get('stock', 0) + quantity
                        ProductModel.update_product_quantity(product_id, new_quantity)

            # حذف عناصر الفاتورة (سيتم حذفها تلقائيًا بسبب الـ ON DELETE CASCADE)
            # حذف الفاتورة
            delete_query = "DELETE FROM invoices WHERE id = ?"
            db.execute(delete_query, (invoice_id,))

            # حفظ التغييرات
            db.commit()
            return True
        except Exception as e:
            # التراجع عن التغييرات في حالة حدوث خطأ
            db.rollback()
            db.log_error(f"خطأ في حذف الفاتورة: {str(e)}")
            return False

    @staticmethod
    def delete_invoice_item(invoice_id, product_code, quantity=1):
        """حذف عنصر من الفاتورة وتحديث الفاتورة الرئيسية"""
        try:
            # بدء معاملة قاعدة البيانات
            if not db.conn:
                db.connect()

            # البحث عن العنصر المراد حذفه
            query = """
                SELECT * FROM invoice_items
                WHERE invoice_id = ? AND product_code = ?
                LIMIT 1
            """
            item = db.fetch_one(query, (invoice_id, product_code))

            if not item:
                db.log_error(f"العنصر غير موجود في الفاتورة: {product_code}")
                return False

            item_id = item.get('id')
            item_total = item.get('total_price', 0)

            # حذف العنصر من الفاتورة
            delete_query = "DELETE FROM invoice_items WHERE id = ?"
            db.execute(delete_query, (item_id,))

            # تحديث إجمالي الفاتورة
            invoice_query = "SELECT * FROM invoices WHERE id = ?"
            invoice = db.fetch_one(invoice_query, (invoice_id,))

            if invoice:
                new_subtotal = invoice.get('subtotal', 0) - item_total
                new_total = invoice.get('total', 0) - item_total

                update_query = """
                    UPDATE invoices
                    SET subtotal = ?, total = ?
                    WHERE id = ?
                """
                db.execute(update_query, (new_subtotal, new_total, invoice_id))

            # حفظ التغييرات
            db.commit()
            return True
        except Exception as e:
            # التراجع عن التغييرات في حالة حدوث خطأ
            db.rollback()
            db.log_error(f"خطأ في حذف عنصر الفاتورة: {str(e)}")
            return False

    @staticmethod
    def get_invoice_stats():
        """استرجاع إحصائيات الفواتير"""
        # إجمالي عدد الفواتير
        total_query = "SELECT COUNT(*) as total FROM invoices"
        total_result = db.fetch_one(total_query)
        total_invoices = total_result.get('total', 0) if total_result else 0

        # إجمالي قيمة الفواتير
        value_query = "SELECT SUM(total) as total_value FROM invoices"
        value_result = db.fetch_one(value_query)
        total_value = value_result.get('total_value', 0) if value_result else 0

        # متوسط قيمة الفاتورة
        if total_invoices > 0:
            avg_value = total_value / total_invoices
        else:
            avg_value = 0

        # عدد الفواتير غير المدفوعة
        unpaid_query = "SELECT COUNT(*) as count FROM invoices WHERE status = 'غير مدفوعة'"
        unpaid_result = db.fetch_one(unpaid_query)
        unpaid_count = unpaid_result.get('count', 0) if unpaid_result else 0

        # إجمالي المبالغ غير المدفوعة
        unpaid_amount_query = "SELECT SUM(remaining_amount) as total FROM invoices WHERE status = 'غير مدفوعة'"
        unpaid_amount_result = db.fetch_one(unpaid_amount_query)
        unpaid_amount = unpaid_amount_result.get('total', 0) if unpaid_amount_result else 0

        return {
            "total_invoices": total_invoices,
            "total_value": total_value,
            "avg_value": avg_value,
            "unpaid_count": unpaid_count,
            "unpaid_amount": unpaid_amount
        }

    @staticmethod
    def get_daily_sales(date=None):
        """استرجاع مبيعات يوم محدد"""
        if not date:
            date = datetime.datetime.now().strftime("%Y/%m/%d")

        query = """
            SELECT i.id, i.reference_number, i.time, i.total, c.name as customer_name
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE i.date = ?
            ORDER BY i.time DESC
        """
        return db.fetch_all(query, (date,))

    @staticmethod
    def fix_invoice_items_payment_status():
        """إصلاح حالة دفع المنتجات لتتطابق مع حالة الفواتير"""
        try:
            # الحصول على جميع الفواتير
            invoices_query = """
                SELECT id, status, total
                FROM invoices
            """
            invoices = db.fetch_all(invoices_query)

            fixed_count = 0

            for invoice in invoices:
                invoice_id = invoice.get('id')
                invoice_status = invoice.get('status')
                invoice_total = invoice.get('total', 0)

                # تحديث حالة المنتجات بناءً على حالة الفاتورة
                if invoice_status == "مدفوعة":
                    # تحديث جميع المنتجات لتكون مدفوعة
                    update_query = """
                        UPDATE invoice_items
                        SET is_paid = 1, paid_amount = total_price
                        WHERE invoice_id = ? AND (is_paid != 1 OR paid_amount != total_price)
                    """
                    result = db.execute(update_query, (invoice_id,))
                    if result and db.cursor.rowcount > 0:
                        fixed_count += 1

                elif invoice_status == "غير مدفوعة":
                    # تحديث جميع المنتجات لتكون غير مدفوعة
                    update_query = """
                        UPDATE invoice_items
                        SET is_paid = 0, paid_amount = 0
                        WHERE invoice_id = ? AND (is_paid != 0 OR paid_amount != 0)
                    """
                    result = db.execute(update_query, (invoice_id,))
                    if result and db.cursor.rowcount > 0:
                        fixed_count += 1

            db.commit()
            return True, f"تم إصلاح {fixed_count} فاتورة"

        except Exception as e:
            db.rollback()
            db.log_error(f"خطأ في إصلاح حالة منتجات الفواتير: {str(e)}")
            return False, f"خطأ في الإصلاح: {str(e)}"

    @staticmethod
    def get_monthly_sales(year, month):
        """استرجاع مبيعات شهر محدد"""
        # تنسيق تاريخ البداية والنهاية للشهر المحدد
        if month < 10:
            month_str = f"0{month}"
        else:
            month_str = str(month)

        start_date = f"{year}/{month_str}/01"

        # تحديد تاريخ نهاية الشهر
        if month == 12:
            end_date = f"{year+1}/01/01"
        else:
            next_month = month + 1
            if next_month < 10:
                next_month_str = f"0{next_month}"
            else:
                next_month_str = str(next_month)
            end_date = f"{year}/{next_month_str}/01"

        query = """
            SELECT i.id, i.reference_number, i.date, i.total, c.name as customer_name, i.status
            FROM invoices i
            LEFT JOIN customers c ON i.customer_id = c.id
            WHERE i.date >= ? AND i.date < ?
            ORDER BY i.date DESC
        """
        return db.fetch_all(query, (start_date, end_date))

    @staticmethod
    def sync_invoice_items_payment_status():
        """مزامنة حالة دفع المنتجات مع حالة الفواتير لإصلاح التضارب"""
        try:
            # الحصول على جميع الفواتير
            invoices_query = """
                SELECT id, status, total, paid_amount, remaining_amount
                FROM invoices
            """
            invoices = db.fetch_all(invoices_query)

            fixed_count = 0

            for invoice in invoices:
                invoice_id = invoice.get('id')
                invoice_status = invoice.get('status')

                # تحديث حالة المنتجات بناءً على حالة الفاتورة
                if invoice_status == "مدفوعة":
                    # تحديث جميع المنتجات لتكون مدفوعة
                    update_query = """
                        UPDATE invoice_items
                        SET is_paid = 1, paid_amount = total_price
                        WHERE invoice_id = ? AND (is_paid != 1 OR paid_amount != total_price)
                    """
                    result = db.execute(update_query, (invoice_id,))
                    if result and db.cursor.rowcount > 0:
                        fixed_count += 1

                elif invoice_status == "غير مدفوعة":
                    # تحديث جميع المنتجات لتكون غير مدفوعة
                    update_query = """
                        UPDATE invoice_items
                        SET is_paid = 0, paid_amount = 0
                        WHERE invoice_id = ? AND (is_paid != 0 OR paid_amount != 0)
                    """
                    result = db.execute(update_query, (invoice_id,))
                    if result and db.cursor.rowcount > 0:
                        fixed_count += 1

                # في حالة "مدفوع جزئياً" نترك المنتجات كما هي
                # لأن المستخدم قد يكون قام بدفع منتجات محددة

            db.commit()
            return True, f"تم إصلاح {fixed_count} فاتورة"

        except Exception as e:
            db.rollback()
            db.log_error(f"خطأ في مزامنة حالة منتجات الفواتير: {str(e)}")
            return False, f"خطأ في المزامنة: {str(e)}"