#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
وحدة العناصر المخصصة - توفر عناصر واجهة مخصصة ووظائف مساعدة
"""

from PyQt5.QtWidgets import QMessageBox, QWidget
from PyQt5.QtCore import Qt
from PyQt5.QtGui import QIcon

class CustomMessageBox(QMessageBox):
    """صندوق رسائل مخصص مع دعم RTL وتنسيق محسن"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setLayoutDirection(Qt.RightToLeft)
        self.setStyleSheet("""
            QMessageBox {
                background-color: white;
                font-family: 'Arial';
                font-size: 12px;
            }
            QMessageBox QLabel {
                color: #333;
                padding: 10px;
            }
            QMessageBox QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
                min-width: 80px;
            }
            QMessageBox QPushButton:hover {
                background-color: #0056b3;
            }
            QMessageBox QPushButton:pressed {
                background-color: #004085;
            }
        """)

def show_information(parent, title, message):
    """عرض رسالة معلومات"""
    msg_box = CustomMessageBox(parent)
    msg_box.setIcon(QMessageBox.Information)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.setStandardButtons(QMessageBox.Ok)
    msg_box.setDefaultButton(QMessageBox.Ok)
    return msg_box.exec_()

def show_warning(parent, title, message):
    """عرض رسالة تحذير"""
    msg_box = CustomMessageBox(parent)
    msg_box.setIcon(QMessageBox.Warning)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.setStandardButtons(QMessageBox.Ok)
    msg_box.setDefaultButton(QMessageBox.Ok)
    return msg_box.exec_()

def show_error(parent, title, message):
    """عرض رسالة خطأ"""
    msg_box = CustomMessageBox(parent)
    msg_box.setIcon(QMessageBox.Critical)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.setStandardButtons(QMessageBox.Ok)
    msg_box.setDefaultButton(QMessageBox.Ok)
    return msg_box.exec_()

def show_question(parent, title, message):
    """عرض رسالة سؤال مع خيارات نعم/لا"""
    msg_box = CustomMessageBox(parent)
    msg_box.setIcon(QMessageBox.Question)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.setStandardButtons(QMessageBox.Yes | QMessageBox.No)
    msg_box.setDefaultButton(QMessageBox.No)
    
    # تخصيص نص الأزرار
    yes_button = msg_box.button(QMessageBox.Yes)
    no_button = msg_box.button(QMessageBox.No)
    
    if yes_button:
        yes_button.setText("نعم")
    if no_button:
        no_button.setText("لا")
    
    result = msg_box.exec_()
    return result == QMessageBox.Yes

def show_confirmation(parent, title, message):
    """عرض رسالة تأكيد مع خيارات موافق/إلغاء"""
    msg_box = CustomMessageBox(parent)
    msg_box.setIcon(QMessageBox.Question)
    msg_box.setWindowTitle(title)
    msg_box.setText(message)
    msg_box.setStandardButtons(QMessageBox.Ok | QMessageBox.Cancel)
    msg_box.setDefaultButton(QMessageBox.Cancel)
    
    # تخصيص نص الأزرار
    ok_button = msg_box.button(QMessageBox.Ok)
    cancel_button = msg_box.button(QMessageBox.Cancel)
    
    if ok_button:
        ok_button.setText("موافق")
    if cancel_button:
        cancel_button.setText("إلغاء")
    
    result = msg_box.exec_()
    return result == QMessageBox.Ok
