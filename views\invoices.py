from PyQt5.QtCore import QSettings
from PyQt5.QtCore import Qt
from PyQt5.QtWidgets import QAbstractItemView
from PyQt5.QtWidgets import QApplication
from PyQt5.QtWidgets import QDialog
from PyQt5.QtWidgets import QTableWidget
from PyQt5.QtWidgets import QTableWidgetItem
from PyQt5.QtWidgets import QPushButton
from PyQt5.QtWidgets import QMenu
from PyQt5.QtWidgets import QMessageBox
from PyQt5.QtWidgets import QAction
from PyQt5.QtWidgets import QLabel
from PyQt5.QtWidgets import QHBoxLayout
from PyQt5.QtWidgets import QVBoxLayout
from PyQt5.QtWidgets import QInputDialog
from utils.invoice_designer import InvoiceDesigner
from utils.settings_manager import SettingsManager
from utils.settings_manager import SettingsManager
from utils.thermal_printer_helper import ESCPOSInvoicePrinter
from utils.date_utils import DateTimeUtils

from controllers.user_controller import UserController
from models.invoices import InvoiceModel
from models.products import ProductModel
from models.customers import CustomerModel

from styles import AppStyles


class InvoicesView(QDialog):
    """نافذة عرض الفواتير"""

    def __init__(self, parent):
        super().__init__(parent)
        self.parent = parent

        self.setWindowTitle("فواتير البيع")
        self.setMinimumSize(800, 600)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # إنشاء التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إنشاء عنوان الصفحة
        title_label = QLabel("فواتير البيع")
        title_label.setObjectName("dialog_title")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        layout.addWidget(title_label)

        # إضافة صف معلومات الفاتورة
        info_layout = QHBoxLayout()

        # القسم الأول: العميل والتاريخ
        left_info = QVBoxLayout()
        customer_label = QLabel(f"<b>العميل:</b> {invoice['customer_name'] if invoice['customer_name'] else 'عميل غير مسجل'}")
        customer_label.setObjectName("info_label")
        date_label = QLabel(f"<b>التاريخ:</b> {DateTimeUtils.format_date_for_display(invoice['date'])}")
        date_label.setObjectName("info_label")
        reference_label = QLabel(f"<b>الرقم المرجعي:</b> {invoice['reference_number']}")
        reference_label.setObjectName("info_label")

        left_info.addWidget(customer_label)
        left_info.addWidget(date_label)
        left_info.addWidget(reference_label)
        info_layout.addLayout(left_info)

        info_layout.addStretch()

        # القسم الثاني: الحالة والإجمالي
        right_info = QVBoxLayout()
        status_label = QLabel(f"<b>حالة الفاتورة:</b> {invoice['status']}")
        status_label.setObjectName("info_label")

        # تلوين حالة الفاتورة
        if invoice['status'] == "مدفوعة":
            status_label.setStyleSheet("color: green;")
        elif invoice['status'] == "غير مدفوعة":
            status_label.setStyleSheet("color: blue;")
        elif invoice['status'] == "ملغية":
            status_label.setStyleSheet("color: red;")

        self.total_label = QLabel(f"<b>المبلغ الإجمالي:</b> {invoice['total']:.2f} ج.م")
        self.total_label.setObjectName("info_label")
        self.items_count_label = QLabel(f"<b>عدد العناصر:</b> {len(invoice_items)}")
        self.items_count_label.setObjectName("info_label")

        right_info.addWidget(status_label)
        right_info.addWidget(self.total_label)
        right_info.addWidget(self.items_count_label)
        info_layout.addLayout(right_info)

        layout.addLayout(info_layout)

        # إضافة عنوان قسم العناصر
        items_title = QLabel("عناصر الفاتورة")
        items_title.setObjectName("section_title")
        items_title.setFont(QFont("Arial", 14, QFont.Bold))
        layout.addWidget(items_title)

        # إضافة تعليمات للمستخدم
        hint_label = QLabel("ملاحظة: يمكنك النقر بزر الماوس الأيمن على أي عنصر لحذفه من الفاتورة")
        hint_label.setObjectName("hint_label")
        hint_label.setStyleSheet("color: #64748b; font-size: 12px; font-style: italic;")
        layout.addWidget(hint_label)

        # إنشاء جدول لعرض عناصر الفاتورة
        self.items_table = QTableWidget()
        self.items_table.setObjectName("items_table")
        self.items_table.setColumnCount(5)
        self.items_table.setHorizontalHeaderLabels(["اسم المنتج", "السعر (ج.م)", "الكمية", "الإجمالي (ج.م)", "كود المنتج"])
        items_header = self.items_table.horizontalHeader()
        if items_header:
            items_header.setSectionResizeMode(QHeaderView.Stretch)
            items_header.setSectionResizeMode(2, QHeaderView.ResizeToContents)
            items_header.setSectionResizeMode(4, QHeaderView.ResizeToContents)
        
        items_v_header = self.items_table.verticalHeader()
        if items_v_header:
            items_v_header.setVisible(False)
        self.items_table.setEditTriggers(QAbstractItemView.NoEditTriggers)
        self.items_table.setSelectionBehavior(QAbstractItemView.SelectRows)
        self.items_table.setSelectionMode(QAbstractItemView.SingleSelection)
        self.items_table.setAlternatingRowColors(False)

        # إضافة قائمة السياق للجدول
        self.items_table.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.items_table.customContextMenuRequested.connect(self.show_item_context_menu)

        # إضافة بيانات العناصر إلى الجدول
        self.populate_items_table()

        layout.addWidget(self.items_table)

        # إضافة أزرار الإجراءات
        buttons_layout = QHBoxLayout()

        # زر الطباعة
        print_btn = QPushButton("🖨️  طباعة الفاتورة")
        print_btn.setObjectName("action_button")
        print_btn.setFixedSize(140, 38)
        print_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        print_btn.clicked.connect(self.print_invoice)

        # زر حفظ التغييرات (إذا كانت الفاتورة قابلة للتعديل)
        if invoice['status'] != "ملغية":
            save_btn = QPushButton("💾 حفظ التغييرات")
            save_btn.setObjectName("action_button")
            save_btn.setFixedSize(140, 38)
            save_btn.setCursor(Qt.CursorShape.PointingHandCursor)
            save_btn.clicked.connect(self.save_changes)
            buttons_layout.addWidget(save_btn)

        # زر الإغلاق
        close_btn = QPushButton("إغلاق")
        close_btn.setObjectName("secondary_button")
        close_btn.setFixedSize(100, 38)
        close_btn.setCursor(Qt.CursorShape.PointingHandCursor)
        close_btn.clicked.connect(self.accept)

        buttons_layout.addWidget(print_btn)
        buttons_layout.addStretch()
        buttons_layout.addWidget(close_btn)

        layout.addLayout(buttons_layout)

        # تطبيق الأنماط
        self.apply_styles()
        # Aplicar configuración RTL a todos los widgets
        apply_rtl_to_all_widgets(self)

    def show_item_context_menu(self, position):
        """عرض قائمة السياق للعناصر"""
        # التحقق من وجود صف محدد
        selected_indexes = self.items_table.selectedIndexes()
        if not selected_indexes or self.invoice['status'] == "ملغية":
            return

        # إنشاء قائمة السياق
        context_menu = QMenu(self)
        context_menu.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # إضافة إجراء الحذف
        row = selected_indexes[0].row()
        product_item = self.items_table.item(row, 0)
        if not product_item:
            return
        product_name = product_item.text()

        # إضافة عنوان العنصر
        title_action = QAction(f"العنصر: {product_name}", self)
        title_action.setEnabled(False)
        title_font = title_action.font()
        title_font.setBold(True)
        title_action.setFont(title_font)
        context_menu.addAction(title_action)

        # إضافة فاصل
        context_menu.addSeparator()

        # إضافة إجراء تعديل السعر
        edit_price_action = QAction("💰 تعديل السعر", self)
        edit_price_action.triggered.connect(lambda: self.edit_item_price(row))
        context_menu.addAction(edit_price_action)

        # إضافة إجراء الحذف (إلغاء عملية البيع)
        delete_action = QAction("❌ حذف", self)
        delete_action.triggered.connect(lambda: self.delete_invoice_item(row))
        context_menu.addAction(delete_action)

        # عرض القائمة في موقع النقر
        context_menu.exec_(self.items_table.mapToGlobal(position))

    def edit_item_price(self, row):
        """تعديل سعر عنصر في الفاتورة"""
        # الحصول على البيانات الحالية للعنصر
        price_item = self.items_table.item(row, 1)
        quantity_item = self.items_table.item(row, 2)
        product_item = self.items_table.item(row, 0)
        
        if not price_item or not quantity_item or not product_item:
            return
            
        current_price = float(price_item.text())
        quantity = int(quantity_item.text())
        product_name = product_item.text()

        # إنشاء مربع حوار لتعديل السعر مع تطبيق التنسيقات
        from styles import AppStyles

        # إنشاء QInputDialog وتطبيق التنسيقات عليه
        input_dialog = QInputDialog(self)
        input_dialog.setWindowTitle("تعديل السعر")
        input_dialog.setLabelText(f"أدخل السعر الجديد للمنتج '{product_name}':")
        input_dialog.setInputMode(QInputDialog.DoubleInput)
        input_dialog.setDoubleValue(current_price)
        input_dialog.setDoubleRange(0.01, 999999.99)
        input_dialog.setDoubleDecimals(2)

        # تطبيق التنسيقات على النافذة
        input_dialog.setStyleSheet(AppStyles.get_input_dialog_style())

        # عرض النافذة والحصول على النتيجة
        ok = input_dialog.exec_() == QInputDialog.Accepted
        new_price = input_dialog.doubleValue() if ok else current_price

        if ok and new_price != current_price:
            # تحديث سعر العنصر في القائمة
            self.invoice_items[row]['unit_price'] = new_price
            self.invoice_items[row]['total_price'] = new_price * quantity

            # تحديث الجدول
            price_item = QTableWidgetItem(f"{new_price:.2f}")
            price_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.items_table.setItem(row, 1, price_item)

            total_item = QTableWidgetItem(f"{new_price * quantity:.2f}")
            total_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.items_table.setItem(row, 3, total_item)

            # تحديث إجمالي الفاتورة
            self.update_invoice_total()

            # تحديث معلومات الفاتورة
            self.update_invoice_info()

    def delete_invoice_item(self, row):
        """حذف عنصر من الفاتورة (إلغاء عملية البيع)"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent
            user_id = None

            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = getattr(parent, 'parent', None) if hasattr(parent, 'parent') else None

            if main_window:
                current_user = self._get_current_user_safely(main_window)
                if current_user:
                    user_id = current_user.get('id')
                # التحقق من صلاحية حذف عملية بيع
                from controllers.user_controller import UserController
                if user_id and not UserController.check_permission(user_id, "حذف عملية بيع", show_message=True, parent_widget=self):
                    return

            # الحصول على بيانات العنصر المحدد
            product_name = self.invoice_items[row]['product_name']
            product_code = self.invoice_items[row]['product_code']
            quantity = self.invoice_items[row]['quantity']
            total_price = self.invoice_items[row]['total_price']
            customer_name = self.invoice.get('customer_name', 'عميل غير مسجل')

            # عرض رسالة تأكيد
            confirmation = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف المنتج '{product_name}' من الفاتورة؟\n\n"
                f"سيتم إعادة الكمية ({quantity}) إلى المخزون وتحديث حساب العميل '{customer_name}'.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if confirmation == QMessageBox.Yes:
                # استدعاء المحرك لحذف المنتج من الفاتورة وتحديث المخزون وحساب العميل
                from models.invoices import InvoiceModel
                from models.products import ProductModel
                from models.customers import CustomerModel

                # حذف المنتج من الفاتورة في قاعدة البيانات
                success = InvoiceModel.delete_invoice_item(self.invoice['id'], product_code, quantity)

                if success:
                    # إعادة المنتج إلى المخزون
                    ProductModel.update_stock(product_code, quantity, 'add')

                    # تحديث حساب العميل
                    if customer_name != "عميل نقدي" and customer_name != "عميل غير مسجل":
                        CustomerModel.update_account(customer_name, -total_price)

                    # حذف العنصر من القائمة المحلية
                    self.invoice_items.pop(row)

                    # تحديث إجمالي الفاتورة
                    self.invoice['total'] -= total_price

                    # التحقق مما إذا كانت الفاتورة فارغة الآن
                    if len(self.invoice_items) == 0:
                        # حذف الفاتورة بالكامل إذا لم يتبق أي عناصر
                        InvoiceModel.delete_invoice(self.invoice['id'])
                        QMessageBox.information(self, "نجاح", "تم حذف العملية وإلغاء الفاتورة بالكامل بنجاح.")

                        # إغلاق النافذة
                        self.accept()

                        # طلب من الأب تحديث عرض الفواتير
                        self._safely_refresh_parent_table()
                    else:
                        # تحديث الجدول
                        self.populate_items_table()

                        # تحديث معلومات الفاتورة
                        self.update_invoice_info()

                        QMessageBox.information(self, "نجاح", "تم حذف العملية بنجاح.")

                        # تحديث تاب العملاء ونافذة تفاصيل العميل
                        self.refresh_customer_related_views()
                else:
                    QMessageBox.warning(self, "خطأ", "فشل في حذف العملية. يرجى المحاولة مرة أخرى.")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف العملية: {str(e)}")
            print(f"Error in delete_invoice_item: {str(e)}")

    def refresh_customer_related_views(self):
        """تحديث تاب العملاء ونوافذ تفاصيل العملاء المفتوحة"""
        try:
            # البحث عن النافذة الرئيسية
            main_window = self.window()
            if hasattr(main_window, 'content_widget'):
                # تحديث تاب العملاء
                from views.customers import CustomersView
                if main_window and hasattr(main_window, 'content_widget') and main_window.content_widget:
                    for i in range(main_window.content_widget.count()):
                        widget = main_window.content_widget.widget(i)
                        if isinstance(widget, CustomersView):
                            widget.refresh_customers_table()
                            break

            # تحديث جميع نوافذ تفاصيل العملاء المفتوحة
            from PyQt5.QtWidgets import QApplication
            from views.customers import CustomerDetailsDialog
            for widget in QApplication.allWidgets():
                if isinstance(widget, CustomerDetailsDialog) and widget.isVisible():
                    widget.refresh_customer_data()

        except Exception as e:
            print(f"[ERROR] خطأ في تحديث واجهات العملاء: {str(e)}")

    def update_invoice_total(self):
        """تحديث إجمالي الفاتورة"""
        total = sum(item['total_price'] for item in self.invoice_items)
        self.invoice['total'] = total

    def remove_item(self, row):
        """حذف عنصر من الفاتورة"""
        deleted_item = self.invoice_items[row]

        # حذف العنصر من القائمة
        self.invoice_items.pop(row)

        # تحديث إجمالي الفاتورة والقيم
        self.invoice['total'] -= deleted_item['total_price']

        # تحديث الجدول
        self.items_table.removeRow(row)

        # إعادة ترقيم الصفوف في الجدول
        for i in range(self.items_table.rowCount()):
            item = QTableWidgetItem(str(i + 1))
            item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.items_table.setItem(i, 0, item)

        # تحديث معلومات الفاتورة
        self.update_invoice_info()

    def update_invoice_info(self):
        """تحديث معلومات الفاتورة المعروضة"""
        self.total_label.setText(f"<b>المبلغ الإجمالي:</b> {self.invoice['total']:.2f} ج.م")
        self.items_count_label.setText(f"<b>عدد العناصر:</b> {len(self.invoice_items)}")

    def save_changes(self):
        """حفظ التغييرات على الفاتورة الرئيسية"""
        from models.invoices import InvoiceModel

        # تحديث بيانات الفاتورة في قاعدة البيانات
        try:
            # تحضير بيانات الفاتورة للتحديث
            invoice_data = {
                'total': self.invoice['total'],
                'subtotal': self.invoice['total'],  # نفترض أن الإجمالي الفرعي يساوي الإجمالي
                'tax': 0,  # يمكن تحديثه لاحقاً إذا كانت هناك ضرائب
                'discount': 0,  # يمكن تحديثه لاحقاً إذا كانت هناك خصومات
                'paid_amount': self.invoice.get('paid_amount', self.invoice['total']),
                'remaining_amount': self.invoice.get('remaining_amount', 0),
                'payment_method': self.invoice.get('payment_method', 'نقداً'),
                'status': self.invoice.get('status', 'مدفوعة'),
                'notes': self.invoice.get('notes', '')
            }

            # حفظ التغييرات في قاعدة البيانات
            success = InvoiceModel.update_invoice_with_items(
                self.invoice['id'],
                invoice_data,
                self.invoice_items
            )

            if success:
                # عرض رسالة تأكيد
                QMessageBox.information(self, "حفظ التغييرات", "تم حفظ التغييرات بنجاح.")

                # إغلاق النافذة
                self.accept()

                # طلب من الأب تحديث عرض الفواتير
                self._safely_refresh_parent_table()
            else:
                QMessageBox.warning(self, "خطأ في الحفظ", "فشل في حفظ التغييرات. يرجى المحاولة مرة أخرى.")

        except Exception as e:
            # عرض رسالة خطأ
            QMessageBox.critical(
                self,
                "خطأ في الحفظ",
                f"حدث خطأ أثناء محاولة حفظ التغييرات: {str(e)}"
            )

    def populate_items_table(self):
        """ملء جدول العناصر ببيانات عناصر الفاتورة"""
        # تحديث عدد الصفوف
        self.items_table.setRowCount(len(self.invoice_items))

        # إضافة البيانات إلى الجدول
        for row, item in enumerate(self.invoice_items):
            # اسم المنتج
            name_item = QTableWidgetItem(item["product_name"])
            self.items_table.setItem(row, 0, name_item)

            # سعر المنتج
            price_item = QTableWidgetItem(f"{item['unit_price']:.2f}")
            price_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.items_table.setItem(row, 1, price_item)

            # الكمية
            quantity_item = QTableWidgetItem(str(item["quantity"]))
            quantity_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.items_table.setItem(row, 2, quantity_item)

            # إجمالي السعر
            total_item = QTableWidgetItem(f"{item['total_price']:.2f}")
            total_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.items_table.setItem(row, 3, total_item)

            # كود المنتج
            product_code_item = QTableWidgetItem(str(item["product_code"]))
            product_code_item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
            self.items_table.setItem(row, 4, product_code_item)

    def print_invoice(self):
        """طباعة الفاتورة"""
        try:
            # الحصول على اسم العميل من قاعدة البيانات باستخدام customer_id
            customer_name = "عميل نقدي"  # القيمة الافتراضية
            customer_id = self.invoice.get('customer_id')
            if customer_id:
                try:
                    from models.customers import CustomerModel
                    customer = CustomerModel.get_customer_by_id(customer_id)
                    if customer:
                        customer_name = customer['name']
                        print(f"تم الحصول على اسم العميل من قاعدة البيانات: {customer_name}")
                    else:
                        print(f"لم يتم العثور على العميل بالمعرف: {customer_id}")
                except Exception as customer_error:
                    print(f"خطأ في الحصول على اسم العميل: {str(customer_error)}")

            # تحويل بيانات الفاتورة إلى التنسيق المطلوب
            invoice_data = {
                'reference_number': self.invoice.get('reference_number', ''),
                'date': self.invoice.get('date', ''),
                'customer_name': customer_name,
                'total': self.invoice.get('total', 0),
                'subtotal': self.invoice.get('subtotal', 0),
                'tax': self.invoice.get('tax', 0),
                'discount': self.invoice.get('discount', 0),
                'paid_amount': self.invoice.get('paid_amount', 0),
                'remaining_amount': self.invoice.get('remaining_amount', 0),
                'payment_method': self.invoice.get('payment_method', 'نقداً'),
                'status': self.invoice.get('status', 'مدفوعة'),
                'notes': self.invoice.get('notes', '')
            }

            # تحويل عناصر الفاتورة إلى التنسيق المطلوب
            items_data = []
            for item in self.invoice_items:
                items_data.append({
                    'product_name': item.get('product_name', ''),
                    'product_code': item.get('product_code', ''),
                    'quantity': item.get('quantity', 1),
                    'unit_price': item.get('unit_price', 0),
                    'total_price': item.get('total_price', 0)
                })

            # قراءة إعدادات الطباعة من الإعدادات
            from PyQt5.QtCore import QSettings
            settings = QSettings("MyCompany", "SmartManager")
            default_printer = settings.value("invoice_design/default_printer", "الطابعة الافتراضية")

            # قراءة معلومات الشركة
            from utils.settings_manager import SettingsManager
            settings_manager = SettingsManager()
            company_info = settings_manager.get_real_company_settings()

            # تنفيذ الطباعة باستخدام ESC/POS
            from utils.thermal_printer_helper import ESCPOSInvoicePrinter

            # إنشاء طابعة ESC/POS
            escpos_printer = ESCPOSInvoicePrinter()

            # طباعة معلومات التشخيص
            print(f"بيانات الفاتورة للطباعة:")
            print(f"  - رقم الفاتورة: {invoice_data.get('reference_number')}")
            print(f"  - اسم العميل: {invoice_data.get('customer_name')}")
            print(f"  - عدد العناصر: {len(items_data)}")
            print(f"  - الطابعة المحددة: {default_printer}")

            # طباعة الفاتورة
            success = escpos_printer.print_invoice(
                invoice_data,
                items_data,
                company_info,
                default_printer if default_printer != "الطابعة الافتراضية" else None
            )

            if success:
                QMessageBox.information(self, "نجح الطباعة", "تم طباعة الفاتورة بنجاح")
            else:
                QMessageBox.critical(self, "فشل الطباعة", "فشل في طباعة الفاتورة")

        except Exception as e:
            error_msg = f"حدث خطأ أثناء محاولة طباعة الفاتورة:\n{str(e)}\n\nتأكد من:\n"
            error_msg += "• تشغيل الطابعة الحرارية\n"
            error_msg += "• توصيل كابل USB أو الشبكة\n"
            error_msg += "• توفر ورق الطباعة\n"
            error_msg += "• تثبيت تعريف الطابعة الصحيح"

            QMessageBox.critical(self, "خطأ في الطباعة", error_msg)

    def mark_invoice_as_paid(self, invoice_id):
        """تعليم الفاتورة كمدفوعة"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            user_id = None

            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window:
                current_user = self._get_current_user_safely(main_window)
                if current_user:
                    user_id = current_user.get('id')
                # التحقق من صلاحية تعديل فاتورة
                from controllers.user_controller import UserController
                if user_id and not UserController.check_permission(user_id, "تعديل فاتورة", show_message=True, parent_widget=self):
                    return

            if self.update_invoice_status(invoice_id, "مدفوعة"):
                QMessageBox.information(self, "تم التحديث", f"تم تعليم الفاتورة رقم {invoice_id} كمدفوعة بنجاح.")
                self.populate_invoices_table()
        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء التحقق من الصلاحيات: {str(e)}")

    def delete_invoice(self, invoice_id):
        """حذف الفاتورة نهائياً من قاعدة البيانات"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            user_id = None

            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window:
                current_user = self._get_current_user_safely(main_window)
                if current_user:
                    user_id = current_user.get('id')
                # التحقق من صلاحية حذف فاتورة
                from controllers.user_controller import UserController
                if user_id and not UserController.check_permission(user_id, "حذف فاتورة", show_message=True, parent_widget=self):
                    return

            # الحصول على معلومات الفاتورة قبل الحذف
            invoice = InvoiceModel.get_invoice_by_id(invoice_id)
            if not invoice:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الفاتورة المطلوبة")
                return

            # عرض رسالة تأكيد مع تفاصيل الفاتورة
            customer_name = invoice.get('customer_name', 'عميل غير مسجل')
            total_amount = invoice.get('total', 0)
            reference_number = invoice.get('reference_number', '')

            reply = QMessageBox.question(
                self,
                "تأكيد الحذف",
                f"هل أنت متأكد من حذف الفاتورة رقم {invoice_id} نهائياً؟\n\n"
                f"الرقم المرجعي: {reference_number}\n"
                f"العميل: {customer_name}\n"
                f"المبلغ الإجمالي: {total_amount:.2f} ج.م\n\n"
                f"تحذير: سيتم حذف الفاتورة وجميع عناصرها نهائياً من قاعدة البيانات\n"
                f"وسيتم إعادة كميات المنتجات إلى المخزون.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # حذف الفاتورة باستخدام النموذج
                success = InvoiceModel.delete_invoice(invoice_id)

                if success:
                    QMessageBox.information(self, "تم الحذف", f"تم حذف الفاتورة رقم {invoice_id} بنجاح.")
                    self.populate_invoices_table()
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في حذف الفاتورة. يرجى المحاولة مرة أخرى.")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء حذف الفاتورة: {str(e)}")

    def cancel_invoice(self, invoice_id):
        """إلغاء الفاتورة (تغيير حالتها إلى ملغية)"""
        try:
            # التحقق من صلاحية المستخدم
            main_window = None
            parent = self.parent()
            user_id = None

            while parent:
                if hasattr(parent, 'current_user'):
                    main_window = parent
                    break
                parent = parent.parent()

            if main_window:
                current_user = self._get_current_user_safely(main_window)
                if current_user:
                    user_id = current_user.get('id')
                # التحقق من صلاحية إلغاء فاتورة
                from controllers.user_controller import UserController
                if user_id and not UserController.check_permission(user_id, "إلغاء فاتورة", show_message=True, parent_widget=self):
                    return

            # الحصول على معلومات الفاتورة قبل الإلغاء
            invoice = InvoiceModel.get_invoice_by_id(invoice_id)
            if not invoice:
                QMessageBox.warning(self, "خطأ", "لم يتم العثور على الفاتورة المطلوبة")
                return

            # التحقق من أن الفاتورة ليست ملغية بالفعل
            if invoice.get('status') == "ملغية":
                QMessageBox.warning(self, "تنبيه", "هذه الفاتورة ملغية بالفعل")
                return

            # عرض رسالة تأكيد مع تفاصيل الفاتورة
            customer_name = invoice.get('customer_name', 'عميل غير مسجل')
            total_amount = invoice.get('total', 0)
            reference_number = invoice.get('reference_number', '')
            current_status = invoice.get('status', '')

            reply = QMessageBox.question(
                self,
                "تأكيد إلغاء الفاتورة",
                f"هل أنت متأكد من إلغاء الفاتورة رقم {invoice_id}؟\n\n"
                f"الرقم المرجعي: {reference_number}\n"
                f"العميل: {customer_name}\n"
                f"المبلغ الإجمالي: {total_amount:.2f} ج.م\n"
                f"الحالة الحالية: {current_status}\n\n"
                f"ملاحظة: سيتم تغيير حالة الفاتورة إلى 'ملغية'\n"
                f"وسيتم إعادة كميات المنتجات إلى المخزون.",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                # إلغاء الفاتورة عن طريق تغيير حالتها وإعادة المنتجات إلى المخزون
                success = self.cancel_invoice_with_stock_return(invoice_id)

                if success:
                    QMessageBox.information(self, "تم الإلغاء", f"تم إلغاء الفاتورة رقم {invoice_id} بنجاح.\n\nتم إعادة جميع كميات المنتجات إلى المخزون.")
                    self.populate_invoices_table()
                else:
                    QMessageBox.critical(self, "خطأ", "فشل في إلغاء الفاتورة. يرجى المحاولة مرة أخرى.")

        except Exception as e:
            QMessageBox.critical(self, "خطأ", f"حدث خطأ أثناء إلغاء الفاتورة: {str(e)}")

    def cancel_invoice_with_stock_return(self, invoice_id):
        """إلغاء الفاتورة مع إعادة المنتجات إلى المخزون"""
        try:
            # الحصول على عناصر الفاتورة قبل الإلغاء
            items = InvoiceModel.get_invoice_items(invoice_id)
            if not items:
                # فاتورة فارغة - يمكن إلغاؤها مباشرة
                return self.update_invoice_status(invoice_id, "ملغية")

            # إعادة المنتجات إلى المخزون (فقط للمنتجات الفيزيائية)
            restored_products = []
            for item in items:
                product_id = item.get('product_id')
                quantity = item.get('quantity', 0)
                product_name = item.get('product_name', 'منتج غير معروف')
                
                if product_id and quantity > 0:
                    # الحصول على بيانات المنتج
                    product = ProductModel.get_product_by_id(product_id)
                    if product:
                        # إعادة الكمية إلى المخزون فقط للمنتجات الفيزيائية
                        product_type = product.get('product_type', 'physical')
                        if product_type != 'service':
                            current_stock = product.get('stock', 0)
                            new_quantity = current_stock + quantity
                            success = ProductModel.update_product_quantity(product_id, new_quantity)
                            if success:
                                restored_products.append(f"{product_name}: +{quantity}")
                                print(f"تم إعادة {quantity} وحدة من {product_name} إلى المخزون (من {current_stock} إلى {new_quantity})")
                            else:
                                print(f"فشل في إعادة كمية المنتج {product_name} إلى المخزون")
                        else:
                            print(f"تم تجاهل الخدمة {product_name} (لا يحتاج إعادة للمخزون)")
                    else:
                        print(f"لم يتم العثور على المنتج بالمعرف {product_id}")

            # تغيير حالة الفاتورة إلى ملغية
            success = self.update_invoice_status(invoice_id, "ملغية")
            
            if success:
                print(f"تم إلغاء الفاتورة {invoice_id} وإعادة {len(restored_products)} منتج إلى المخزون")
                return True
            else:
                print(f"فشل في تحديث حالة الفاتورة {invoice_id}")
                return False

        except Exception as e:
            print(f"خطأ في إلغاء الفاتورة: {str(e)}")
            return False

    def update_invoice_status(self, invoice_id, new_status):
        """تحديث حالة الفاتورة في قاعدة البيانات"""
        try:
            # الحصول على بيانات الفاتورة الحالية
            invoice = InvoiceModel.get_invoice_by_id(invoice_id)
            if not invoice:
                print(f"لم يتم العثور على الفاتورة بالمعرف {invoice_id}")
                return False

            # تحديث حالة الفاتورة
            update_data = {
                "status": new_status
            }
            
            # ذاك كانت الحالة ملغية، تعيين المبلغ المتبقي إلى صفر
            if new_status == "ملغية":
                update_data["paid_amount"] = 0
                update_data["remaining_amount"] = 0
            
            success = InvoiceModel.update_invoice(invoice_id, update_data)

            if success:
                print(f"تم تحديث حالة الفاتورة {invoice_id} إلى '{new_status}'")
                # تحديث جدول الفواتير
                self.populate_invoices_table()
                return True
            else:
                print(f"فشل في تحديث حالة الفاتورة {invoice_id}")
                return False
                
        except Exception as e:
            print(f"خطأ في تحديث حالة الفاتورة: {str(e)}")
            return False

    def apply_styles(self):
        """تطبيق الأنماط على العناصر"""
        # استخدام التنسيقات من ملف الستايلات
        self.setStyleSheet(AppStyles.get_all_view_styles())

        # إضافة تنسيقات خاصة
        additional_styles = """
            #search_input, #date_input {
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                padding: 8px 12px;
                background-color: white;
            }

            #search_input:hover, #date_input:hover {
                border: 1px solid #64748b;
            }

            #search_input:focus, #date_input:focus {
                border: 1px solid #3b82f6;
            }

            #stats_label {
                color: #64748b;
                font-size: 12px;
                padding: 8px 0;
            }

            #hint_label {
                color: #64748b;
                font-size: 11px;
                font-style: italic;
                padding: 5px 0;
            }

            QTableWidget {
                font-size: 13px;
                background-color: white;
                gridline-color: #e2e8f0;
            }

            QTableWidget::item {
                padding: 8px;
                background-color: #f8fafc;
                border-bottom: 1px solid #f1f5f9;
            }

            QTableWidget::item:selected {
                background-color: rgba(59, 130, 246, 0.15);
                color: #000000;
            }

            QHeaderView::section {
                background-color: #f8fafc;
                color: #475569;
                font-weight: bold;
                border: none;
                padding: 10px;
                border-bottom: 1px solid #cbd5e1;
                border-right: 1px solid #e2e8f0;
            }

            QHeaderView::section:first {
                border-top-right-radius: 6px;
            }

            QHeaderView::section:last {
                border-top-left-radius: 6px;
                border-right: none;
            }

            QMenu {
                background-color: white;
                border: 1px solid #cbd5e1;
                border-radius: 6px;
                padding: 5px;
            }

            QMenu::item {
                padding: 8px 25px;
                border-radius: 4px;
            }

            QMenu::item:selected {
                background-color: #e2e8f0;
                color: #22c55e;
            }

            QMenu::separator {
                height: 1px;
                background-color: #e2e8f0;
                margin: 5px 0px;
            }
        """

        current_style = self.styleSheet()
        self.setStyleSheet(current_style + additional_styles)

    def get_real_company_settings(self, settings=None):
        """قراءة إعدادات الشركة الحقيقية باستخدام SettingsManager الموحد"""
        try:
            from utils.settings_manager import SettingsManager
            
            # استخدام SettingsManager الموحد
            settings_manager = SettingsManager()
            company_info = settings_manager.get_real_company_settings()
            
            # التحقق من metadata والتعامل مع التحذيرات
            if '_metadata' in company_info:
                metadata = company_info.pop('_metadata')
                
                if not metadata.get('has_real_data', False):
                    print("⚠️ تحذير: يتم استخدام بيانات شركة غير مكتملة")
                    print("📝 يرجى تحديث معلومات الشركة في تاب الإعدادات")
                    
                    # عرض رسالة تحذيرية للمستخدم
                    try:
                        from utils.custom_widgets import show_warning
                        show_warning(
                            self,
                            "بيانات الشركة غير محدثة",
                            "يتم استخدام بيانات غير مكتملة للشركة في الفاتورة.\n\n"
                            "يرجى الذهاب إلى تاب 'الإعدادات' وتحديث:\n"
                            "• اسم الشركة\n"
                            "• رقم الهاتف\n"
                            "• عنوان الشركة\n"
                            "• ملاحظات الفاتورة\n\n"
                            "ثم حفظ الإعدادات لتظهر في الفواتير المطبوعة."
                        )
                    except Exception as warning_error:
                        print(f"خطأ في عرض التحذير: {str(warning_error)}")
                else:
                    print("✅ يتم استخدام بيانات الشركة المخصصة")
            
            return company_info

        except Exception as e:
            print(f"خطأ في قراءة إعدادات الشركة: {str(e)}")
            # إرجاع بيانات فارغة بدلاً من افتراضية
            return {
                'name': "",
                'phone': "",
                'address': "",
                'notes': "شكراً لتعاملكم معنا"
            }

    def refresh_page(self):
        """تحديث بيانات الصفحة عند الانتقال إليها أو عند فتح التطبيق"""
        # تحميل بيانات الفواتير من جديد
        self.populate_invoices_table()

        # تحديث حالة تفعيل الأزرار بناءً على صلاحيات المستخدم
        self.update_buttons_state()

        print("تم تحديث صفحة الفواتير")

    def update_buttons_state(self):
        """تحديث حالة تفعيل الأزرار بناءً على صلاحيات المستخدم"""
        # لا توجد أزرار لتحديث حالتها في تاب الفواتير حالياً
        pass

class InvoiceDetailsDialog(QDialog):
    """نافذة عرض تفاصيل الفاتورة"""

    def __init__(self, parent, invoice, invoice_items):
        super().__init__(parent)
        self.parent = parent
        self.invoice = invoice
        self.invoice_items = invoice_items

        self.setWindowTitle(f"تفاصيل الفاتورة #{invoice['id']}")
        self.setMinimumSize(800, 600)
        self.setLayoutDirection(Qt.LayoutDirection.RightToLeft)

        # إنشاء التخطيط الرئيسي
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(15)

        # إنشاء عنوان الصفحة
        title_label = QLabel(f"تفاصيل الفاتورة #{invoice['id']}")
        title_label.setObjectName("dialog_title")
        title_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        title_label.setFont(QFont("Arial", 18, QFont.Bold))
        layout.addWidget(title_label)

        # إضافة صف معلومات الفاتورة
        info_layout = QHBoxLayout()

        # القسم الأول: العميل والتاريخ
        left_info = QVBoxLayout()
        customer_label = QLabel(f"<b>العميل:</b> {invoice['customer_name'] if invoice['customer_name'] else 'عميل غير مسجل'}")
        customer_label.setObjectName("info_label")
        date_label = QLabel(f"<b>التاريخ:</b> {DateTimeUtils.format_date_for_display(invoice['date'])}")
        date_label.setObjectName("info_label")
        reference_label = QLabel(f"<b>الرقم المرجعي:</b> {invoice['reference_number']}")
        reference_label.setObjectName("info_label")

        left_info.addWidget(customer_label)
        left_info.addWidget(date_label)
        left_info.addWidget(reference_label)
        info_layout.addLayout(left_info)

        info_layout.addStretch()

        # القسم الثاني: الحالة والإجمالي
        right_info = QVBoxLayout()
        status_label = QLabel(f"<b>حالة الفاتورة:</b> {invoice['status']}")
        status_label.setObjectName("info_label")

        # تلوين حالة الفاتورة
        if invoice['status'] == "مدفوعة":
            status_label.setStyleSheet("color: green;")
        elif invoice['status'] == "غير مدفوعة":
            status_label.setStyleSheet("color: blue;")
        elif invoice['status'] == "ملغية":
            status_label.setStyleSheet("color: red;")

        self.total_label = QLabel(f"<b>المبلغ الإجمالي:</b> {invoice['total']:.2f} ج.م")
        self.total_label.setObjectName("info_label")
        self.items_count_label = QLabel(f"<b>عدد العناصر:</b> {len(invoice_items)}")
        self.items_count_label.setObjectName("info_label")

        right_info.add